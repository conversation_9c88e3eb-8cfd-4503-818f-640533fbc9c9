---
services:

  mariadb:
    image: mariadb:10.11.9
    ports:
      - "3407:3306"
    volumes:
      - mariadb-data:/var/lib/mysql
    networks:
      deubaimmo_network:
        aliases:
          - mariadb
    env_file:
      - env-mariadb.ini
    healthcheck:
      test: ["CMD", "healthcheck.sh", "--su-mysql", "--connect"]
      interval: 1s
      timeout: 5s
      retries: 90

  minio:
    image: minio/minio
    command: server /data
    ports:
      - "9067:9000"
    volumes:
      - ./data/minio:/data
    networks:
      deubaimmo_network:
        aliases:
          - minio
    environment:
      - VIRTUAL_HOST=files.deubaimmo.test
      - VIRTUAL_PORT=9067

  typo3:
    build: docker/typo3-dev/
    volumes:
      - ./typo3:/var/www/typo3/
    ports:
      - "5667:80"
    environment:
      - FIO_WEBMAKLER_ID=mogic-technicaluser
      - FIO_WEBMAKLER_PASSWORD=MPAiy;USzKR'$^.}XWk.Z9<:s-yFNX7R
      - FIO_WEBMAKLER_CLIENT_ID=mogic
      - FIO_WEBMAKLER_CLIENT_SECRET=WUOATesKSgebsbU7MAyQ
      - MINIO_PROD_USER=typo3-readonly
      - MINIO_PROD_PASSWORD=taiweemee0oZ
      - MINIO_PROD_CUSTOM_HOST=https://files.deutsche-bank-immobilien.de
      - MINIO_PROD_PUBLIC_URL=files.deutsche-bank-immobilien.de/dbi-typo3/
      - MINIO_LOCAL_USER=typo3-dev
      - MINIO_LOCAL_PASSWORD=phuShih1
      - MINIO_LOCAL_PUBLIC_URL=files.deubaimmo.test:9067/dbi-typo3/
      - TYPO3_CONTEXT=Development/Local
      - SENTRY_RELEASE=develop
      #nginx-proxy configuration:
      - VIRTUAL_HOST=deubaimmo.test
      - VIRTUAL_PORT=5667
    networks:
      deubaimmo_network:
        aliases:
          - deubaimmo.test
    depends_on:
      mariadb:
        condition: service_healthy
      minio:
        condition: service_started

  build:
    build: docker/build-frontend-assets
    volumes:
      - ./typo3/packages/dbi:/var/www
    restart: "no"
    profiles:
      - tools

  sync-database:
    build: docker/sync-database
    volumes:
      - .:/data
      - ./docker/sync-database/syncDatabase.sh:/syncDatabase.sh
    env_file:
      - env-mariadb.ini
    environment:
      - BUCKET_NAME=fio-dumps
      - DIRECTORY_PATH=deubaimmo/production
    depends_on:
      mariadb:
        condition: service_healthy
    networks:
      deubaimmo_network:
    restart: "no"
    profiles:
      - tools

volumes:
  mariadb-data:

networks:
  deubaimmo_network:
    name: deubaimmo
