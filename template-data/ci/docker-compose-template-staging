---
services:

  mariadb-deubaimmo:
    image: mariadb:10.11.9
    restart: always
    volumes:
      - mariadb-data:/var/lib/mysql
    environment:
      - MYSQL_ROOT_PASSWORD={{.db_rootUser_password}}
      - MYSQL_HOST={{.db_host}}
      - MYSQL_DATABASE={{.db_database}}
      - MYSQL_USER={{.mariadb_id}}
      - MYSQL_PASSWORD={{.mariadb_password}}
    healthcheck:
      test: ["CMD", "healthcheck.sh", "--su-mysql", "--connect"]
      interval: 1s
      timeout: 5s
      retries: 90

  minio:
    image: minio/minio
    command: server /data
    restart: always
    environment:
      - "MINIO_ROOT_USER={{.minio_root_user}}"
      - "MINIO_ROOT_PASSWORD={{.minio_root_password}}"
    labels:
      - "traefik.http.routers.minio-{{.CI_COMMIT_REF_SLUG}}.rule=Host(`{{.minio_domain}}`)"
      - "traefik.http.routers.minio-{{.CI_COMMIT_REF_SLUG}}.entrypoints=web"
      - "traefik.enable=true"
    volumes:
      - ./data/minio:/data

  typo3:
    image: {{.DOCKERIMAGE}}
    restart: always
    labels:
      - "traefik.http.routers.typo3-{{.CI_COMMIT_REF_SLUG}}.rule=Host(`{{.typo3_domain}}`)"
      - "traefik.http.routers.typo3-{{.CI_COMMIT_REF_SLUG}}.entrypoints=web"
      - "traefik.http.routers.typo3-{{.CI_COMMIT_REF_SLUG}}.middlewares=typo3-{{.ENVIRONMENT}}-auth"
      - "traefik.http.middlewares.typo3-{{.ENVIRONMENT}}-auth.basicauth.users={{.basic_auth}}"
      - "traefik.enable=true"
    environment:
      - ELANDERS_API_USER={{.elanders_api_user}}
      - ELANDERS_API_PASSWORD={{.elanders_api_password}}
      - FIO_WEBMAKLER_ID={{.fio_webmakler_id}}
      - FIO_WEBMAKLER_PASSWORD={{.fio_webmakler_password}}
      - FIO_WEBMAKLER_CLIENT_ID={{.fio_webmakler_client_id}}
      - FIO_WEBMAKLER_CLIENT_SECRET={{.fio_webmakler_client_secret}}
      - MINIO_LOCAL_USER={{.minio_typo3_user}}
      - MINIO_LOCAL_PASSWORD={{.minio_typo3_password}}
      - MINIO_LOCAL_PUBLIC_URL={{.minio_domain}}/{{.minio_bucket}}/
      - MINIO_PROD_USER={{.minio_prod_readonly_user}}
      - MINIO_PROD_PASSWORD={{.minio_prod_readonly_password}}
      - MINIO_PROD_CUSTOM_HOST={{.minio_prod_custom_host}}
      - MINIO_PROD_PUBLIC_URL={{.minio_prod_public_url}}
      - MYSQL_HOST={{.db_host}}
      - MYSQL_DATABASE={{.db_database}}
      - MYSQL_USER={{.mariadb_id}}
      - MYSQL_PASSWORD={{.mariadb_password}}
      - TYPO3_CONTEXT={{.TYPO3_CONTEXT}}
      - SENTRY_RELEASE={{.GIT_TAG_SENTRY}}
    depends_on:
      mariadb-deubaimmo:
        condition: service_healthy
      minio:
        condition: service_started
    logging:
      driver: "gelf"
      options:
        gelf-address: "udp://logs.mogic.com:12201"


volumes:
  mariadb-data:

networks:
  default:
    name: traefik
    external: true
