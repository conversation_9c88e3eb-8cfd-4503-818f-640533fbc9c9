---
services:
  minio:
    image: minio/minio
    command: server /data
    restart: always
    environment:
      - "MINIO_ROOT_USER={{.minio_root_user}}"
      - "MINIO_ROOT_PASSWORD={{.minio_root_password}}"
    labels:
      - "traefik.http.routers.minio-{{.CI_COMMIT_REF_SLUG}}.rule=Host(`{{.minio_domain}}`)"
      - "traefik.http.routers.minio-{{.CI_COMMIT_REF_SLUG}}.entrypoints=web"
      - "traefik.enable=true"
    volumes:
      - ./data/minio:/data

  typo3:
    image: {{.DOCKERIMAGE}}
    restart: always
    labels:
      - "traefik.http.routers.typo3-{{.CI_COMMIT_REF_SLUG}}.rule=Host(`{{.typo3_domain}}`, `www.{{.typo3_domain}}`)"
      - "traefik.http.routers.typo3-{{.CI_COMMIT_REF_SLUG}}.entrypoints=web"
      - "traefik.http.routers.typo3-{{.CI_COMMIT_REF_SLUG}}.middlewares=nonWWWtoWWW"

      - "traefik.http.middlewares.nonWWWtoWWW.redirectregex.regex=^https?://{{.typo3_domain}}(.*)"
      - "traefik.http.middlewares.nonWWWtoWWW.redirectregex.replacement=https://www.{{.typo3_domain}}$${1}"
      - "traefik.http.middlewares.nonWWWtoWWW.redirectregex.permanent=true"

      - "traefik.enable=true"
    environment:
      - ELANDERS_API_USER={{.elanders_api_user}}
      - ELANDERS_API_PASSWORD={{.elanders_api_password}}
      - FIO_WEBMAKLER_ID={{.fio_webmakler_id}}
      - FIO_WEBMAKLER_PASSWORD={{.fio_webmakler_password}}
      - FIO_WEBMAKLER_CLIENT_ID={{.fio_webmakler_client_id}}
      - FIO_WEBMAKLER_CLIENT_SECRET={{.fio_webmakler_client_secret}}
      - MINIO_PROD_USER={{.minio_typo3_user}}
      - MINIO_PROD_PASSWORD={{.minio_typo3_password}}
      - MINIO_PROD_PUBLIC_URL={{.minio_domain}}/{{.minio_bucket}}/
      - MYSQL_HOST={{.db_host}}
      - MYSQL_DATABASE={{.db_database}}
      - MYSQL_USER={{.mariadb_id}}
      - MYSQL_PASSWORD={{.mariadb_password}}
      - TYPO3_CONTEXT={{.TYPO3_CONTEXT}}
      - SENTRY_RELEASE={{.GIT_TAG_SENTRY}}
    depends_on:
      minio:
        condition: service_started
    logging:
      driver: "gelf"
      options:
        gelf-address: "udp://logs.mogic.com:12201"

volumes:
  mariadb-data:

networks:
  default:
    name: traefik
    external: true
