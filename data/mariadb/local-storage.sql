UPDATE sys_file_storage
  SET
    name = 'Production MinIO',
    is_default = 0,
    is_writable = 0,
    processingfolder = '2:_processed_'
 WHERE uid = 1;

INSERT INTO `sys_file_storage` (
  `uid`, `pid`, `tstamp`, `crdate`, `deleted`,
  `name`, `description`,
  `is_default`, `is_browsable`, `is_public`, `is_writable`, `is_online`,
  `auto_extract_metadata`, `processingfolder`,
  `driver`,
  `configuration`
) VALUES (
  2, 0, 1704720023, 1704720042, 0,
  'Local MinIO', '',
  '1', '1', '1', '1', '1',
  '1', NULL,
  'AusDriverAmazonS3',
  '<?xml version="1.0" encoding="utf-8" standalone="yes" ?>
<T3FlexForms>
    <data>
        <sheet index="sDEF">
            <language index="lDEF">
                <field index="basePath">
                    <value index="vDEF"></value>
                </field>
                <field index="pathType">
                    <value index="vDEF">relative</value>
                </field>
                <field index="caseSensitive">
                    <value index="vDEF">1</value>
                </field>
                <field index="baseUri">
                    <value index="vDEF"></value>
                </field>
                <field index="bucket">
                    <value index="vDEF">dbi-typo3</value>
                </field>
                <field index="region">
                    <value index="vDEF">eu-central-1</value>
                </field>
                <field index="customHost">
                    <value index="vDEF">http://minio:9000</value>
                </field>
                <field index="pathStyleEndpoint">
                    <value index="vDEF">1</value>
                </field>
                <field index="key">
                    <value index="vDEF">dummy-user</value>
                </field>
                <field index="secretKey">
                    <value index="vDEF">dummy-password</value>
                </field>
                <field index="publicBaseUrl">
                    <value index="vDEF">dummy-url</value>
                </field>
                <field index="cacheHeaderDuration">
                    <value index="vDEF">0</value>
                </field>
                <field index="protocol">
                    <value index="vDEF">http://</value>
                </field>
                <field index="signature">
                    <value index="vDEF">0</value>
                </field>
                <field index="baseFolder">
                    <value index="vDEF"></value>
                </field>
            </language>
        </sheet>
    </data>
</T3FlexForms>'
);

INSERT INTO `sys_filemounts` (
  `uid`, `pid`, `tstamp`, `deleted`, `hidden`, `sorting`,
  `description`, `title`, `identifier`, `read_only`
) VALUES (
  2,	0,	1713858442,	0,	0,	128,
  '',	'Komplettzugriff lokal',	'2:/',	0
);

UPDATE `be_groups`
  SET file_mountpoints = "1,2"
  WHERE uid = 5 AND title = "Recht: Dateien";
