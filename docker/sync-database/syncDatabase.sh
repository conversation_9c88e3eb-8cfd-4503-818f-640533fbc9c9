# todo - umbau auf nicht-socket
set -e

function executeSqlCommandInTargetDatabase {
  if [ "$1" == "" ]; then
    echo "provide an SQL statement as first parameter"
    exit 1
  fi

  mariadb --skip-ssl -h mariadb -u $MYSQL_USER -p$MYSQL_PASSWORD $MYSQL_DATABASE -e "$1" > /dev/null

}

function checkIfTableExistsInTargetDatabase {

  if [ "$1" == "" ]; then
    echo "provide a table as first parameter"
    exit 255
  fi

  table=$1;

  if [[ $(mariadb --skip-ssl -h mariadb -u $MYSQL_USER -p$MYSQL_PASSWORD $MYSQL_DATABASE --skip-column-names --silent -e "SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = '${table}';") -eq 1 ]]; then
    :
  else
    return 1
  fi
}

function loadOrCreateS3Config {
  S3_CONFIG_FILE="/data/s3-config.ini"

  # Check if s3-config.ini file exists
  if [ ! -f "$S3_CONFIG_FILE" ]; then
    echo "You have to configure access to the s3 backend which provides the dump files."

    # Prompt user for S3 / Minio server details
    #read -rp "Enter your S3 / Minio server URL: " S3_ENDPOINT
    S3_ENDPOINT=https://minio-data.mogic.com
    echo "Using minio endpoint: https://minio-data.mogic.com"
    read -rp "Enter your access key: " S3_ACCESS_KEY
    read -rp "Enter your secret key: " S3_SECRET_KEY

    # Write server details to s3-config.ini file
    printf "S3_ENDPOINT=%s\nS3_ACCESS_KEY=%s\nS3_SECRET_KEY=%s" $S3_ENDPOINT $S3_ACCESS_KEY $S3_SECRET_KEY > $S3_CONFIG_FILE

    echo "s3-config.ini file created with the provided server details."
  else
    echo "We use the predefined s3-config.ini file. If you want to reinitialize it, just delete it from your projects root directory and rexecute the task"
    . $S3_CONFIG_FILE
  fi
}

loadOrCreateS3Config

mc alias set s3-database-dump-server $S3_ENDPOINT $S3_ACCESS_KEY $S3_SECRET_KEY

# List all files in the directory
files=($(mc find "s3-database-dump-server/$BUCKET_NAME/$DIRECTORY_PATH"))

# Check if any files exist
if [ ${#files[@]} -eq 0 ]; then
  echo "No files found in the directory."
  exit 1
fi

# Find the newest file
newest_file=${files[0]}
for file in "${files[@]}"; do
  if [[ "$file" > "$newest_file" ]]; then
    newest_file="$file"
  fi
done

chosen_file="$newest_file"

# Download the chosen file
mc --quiet --no-color cp "$chosen_file" /data/dumps/$DIRECTORY_PATH.sql.gz
if [ $? -eq 0 ]; then
  echo "File downloaded successfully: $chosen_file"
else
  echo "Failed to download the file: $chosen_file"
fi

echo "importing database $DIRECTORY_PATH.sql.gz into local system"
pv "/data/dumps/$DIRECTORY_PATH.sql.gz" -f -L 100M\
    | zcat\
    | grep -v 'M!999999.*enable the sandbox mode'\
    | mariadb --skip-ssl -h mariadb -u $MYSQL_USER -p$MYSQL_PASSWORD $MYSQL_DATABASE
echo "done"

# import database in db server

if checkIfTableExistsInTargetDatabase "be_users"
then
  echo "TYPO3 database detected"

  #echo "-> disabling backend login for non-admins"
  #executeSqlCommandInTargetDatabase "UPDATE be_users SET disable = 1 WHERE admin = 0;"

  if checkIfTableExistsInTargetDatabase "tx_scheduler_task"; then
    echo "-> disabling scheduler tasks"
    executeSqlCommandInTargetDatabase "UPDATE tx_scheduler_task SET disable = 1;"
  fi
fi

echo "Removing imported dump file from local system"
rm /data/dumps/$DIRECTORY_PATH.sql.gz
echo "Done"
