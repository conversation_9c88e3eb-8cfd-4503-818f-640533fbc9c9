FROM gitlab-docker.mogic.com/docker/mogic-base-image:webapp-jammy-php8.2

ADD conf /etc/mogic

RUN ln -sf /etc/mogic/php/90-dbi.ini /etc/php/8.2/fpm/conf.d/\
    && ln -sf /etc/mogic/php/90-dbi.ini /etc/php/8.2/cli/conf.d/\
    && ln -sf /etc/mogic/php/fpm-dbi.conf /etc/php/8.2/fpm/pool.d/dbi.conf\
    && ln -sf /etc/mogic/php/fpm-www.conf /etc/php/8.2/fpm/pool.d/www.conf\
    && ln -sf /etc/mogic/nginx/site-dbi /etc/nginx/sites-enabled/dbi\
    && rm /etc/nginx/sites-enabled/default\
    \
    && ln -sf /etc/mogic/nginx/nginx.conf /etc/nginx/\
    && ln -sf /etc/mogic/nginx/fastcgi_params-extra /etc/nginx/\
    && cp /etc/mogic/cron/* /etc/cron.d/\
    \
    && chown -R www-data:www-data /var/www

VOLUME ["/var/www/typo3"]
WORKDIR /var/www/typo3/
