Deutsche Bank Immobilien
************************
<PERSON><PERSON><PERSON><PERSON><PERSON> von Postbank Immobilien


URLs
====

Testsystem

- Frontend: https://immobilien-deutschebank-stage.fio.de/
- Backend: https://immobilien-deutschebank-stage.fio.de/typo3/

Lokales Entwicklungssystem:

- Frontend: http://deubaimmo.test:5667/
- Backend: http://deubaimmo.test:5667/typo3/

  - Nutzer: admin87dbi
  - Passwort: https://keepersecurity.eu/vault/#detail/UGwWhYZ4f48SUJ9HZ2F5CA


Setup
=====
Einträge in die ``/etc/hosts``::

  127.0.0.1 deubaimmo.test files.deubaimmo.test
  ::1       deubaimmo.test files.deubaimmo.test

Initiales Setup::

  $ <NAME_EMAIL>:fio/deubaimmo.git
  $ make up-new-keep-sources

Hier wird man nach Zugangsdaten für S3 gefragt.
Diese sind zu finden unter https://service.mogic.com/user/profile/minio


Start
=====
Wenn man schonmal aufgesetzt hat::

  $ make start
