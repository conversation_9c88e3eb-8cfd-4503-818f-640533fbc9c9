<?php
/**
 * High Traffic Landing Page (HTLP) used in iframes by advertisements
 * to track the "pvid" parameter, which later will be submitted
 * to medialead
 */
if (!isset($_GET['pvid'])) {
    header('HTTP/1.0 400 Bad Request');
    header('Content-type: text/plain');
    echo "GET parameter \"pvid\" missing\n";
    exit();
}

//make it easy to split afterwards while keeping it very short
$cookieData = gmdate('c') . '|' . $_GET['pvid'];

setcookie(
    'af_pvid',
    $cookieData,
    [
        'expires'  => time() + 7 * 86400,
        'path'     => '/',
        'domain'   => '',
        'secure'   => true,
        'httponly' => false,
        'samesite' => 'None',
    ]
);

//header('HTTP/1.0 204 No Content');
header('HTTP/1.0 200 OK');
header('Access-Control-Allow-Credentials: true');
header('Content-Type: image/png');
echo base64_decode('iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAAC0lEQVQIW2NgAAIAAAUAAR4f7BQAAAAASUVORK5CYII=');
?>
