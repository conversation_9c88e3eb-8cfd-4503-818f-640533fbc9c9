commit 9f0db9baffcf5f34dc400a0cb5c7c31e5fd75e71
Author: <PERSON> <<EMAIL>>
Date:   Wed Apr 30 11:21:13 2025 +0200

    [BUGFIX] Avoid PHP errors with broken FE URL arguments

    When 'type', 'cHash' or '__RequestToken' URL arguments
    are arrays, FE triggers 'Array to string conversion'
    warning level PHP errors. TYPO3 never creates such links,
    this is usually "external manipulation". Still, the FE
    should not crash on this. The patch sanitizes these
    cases.

    Resolves: #104928
    Releases: main, 13.4
    Change-Id: I49c6c901d2ded4aef9252e6feac15c21b772d719
    Reviewed-on: https://review.typo3.org/c/Packages/TYPO3.CMS/+/89283
    Tested-by: <PERSON><PERSON> <<EMAIL>>
    Reviewed-by: <PERSON><PERSON> <<EMAIL>>
    Tested-by: <PERSON> <<EMAIL>>
    Reviewed-by: <PERSON> <<EMAIL>>
    Reviewed-by: <PERSON> <<EMAIL>>
    Tested-by: <PERSON> <<EMAIL>>
    Tested-by: core-ci <<EMAIL>>
    Tested-by: Christian Kuhn <<EMAIL>>
    Reviewed-by: Christian Kuhn <<EMAIL>>

diff --git a/Classes/Middleware/PageArgumentValidator.php b/Classes/Middleware/PageArgumentValidator.php
index 3c9249932bc..e5ceb7e3aa5 100644
--- a/Classes/Middleware/PageArgumentValidator.php
+++ b/Classes/Middleware/PageArgumentValidator.php
@@ -78,7 +78,10 @@ public function process(ServerRequestInterface $request, RequestHandlerInterface
             return $handler->handle($request);
         }
         // Evaluate the cache hash parameter or dynamic arguments when coming from a Site-based routing
-        $cHash = (string)($pageArguments->getArguments()['cHash'] ?? '');
+        $cHash = '';
+        if (isset($pageArguments->getArguments()['cHash']) && is_scalar($pageArguments->getArguments()['cHash'])) {
+            $cHash = (string)($pageArguments->getArguments()['cHash']);
+        }
         $queryParams = $pageArguments->getDynamicArguments();
         if ($cHash !== '' || !empty($queryParams)) {
             $relevantParametersForCacheHashArgument = $this->getRelevantParametersForCacheHashCalculation($pageArguments);
