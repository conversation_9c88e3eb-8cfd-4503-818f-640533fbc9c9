commit f33cc0b18eb882b380eeb4c89987ffc20409d23b
Author: <PERSON> <<EMAIL>>
Date:   Fri Jun 20 15:57:47 2025 +0200

    [BUGFIX] Correctly handle TextTableElement delimiter fallback

    When a table content element has no table_delimiter field,
    the first save fails.

    A TCA database row's field values are either
    - array, when the field is visible in the form
    - simple type, when the field is not in the form
      (with its default value)

    When saving the element the first time, the table_delimiter default value
    is a string "124", causing the `$row['table_delimiter'][0]` to do a
    substring operation. This leads to failure.

    Upon saving the next time, the default value is an integer and all is
    fine.

    This patch fixes the problem by checking if the value is really an array
    before trying to access the first array element.

    Resolves: #106917
    Releases: main, 13.4, 12.4
    Change-Id: Ieb93ad18097ee466f84a39745d1f07a0a37cfdd5

diff --git a/Classes/Form/Element/TextTableElement.php b/Classes/Form/Element/TextTableElement.php
index 42d36b7b277..557ba522178 100644
--- a/Classes/Form/Element/TextTableElement.php
+++ b/Classes/Form/Element/TextTableElement.php
@@ -230,8 +230,8 @@ public function render(): array
     protected function getTableWizard(string $dataId): string
     {
         $row = $this->data['databaseRow'];
-        $delimiter = ($row['table_delimiter'][0] ?? false) ? chr((int)$row['table_delimiter'][0]) : '|';
-        $enclosure = ($row['table_enclosure'][0] ?? false) ? chr((int)$row['table_enclosure'][0]) : '';
+        $delimiter = (is_array($row['table_delimiter']) && ($row['table_delimiter'][0] ?? false)) ? chr((int)$row['table_delimiter'][0]) : '|';
+        $enclosure = (is_array($row['table_enclosure']) && ($row['table_enclosure'][0] ?? false)) ? chr((int)$row['table_enclosure'][0]) : '';

         return sprintf(
             '<typo3-formengine-table-wizard %s></typo3-formengine-table-wizard>',
