commit 9f0db9baffcf5f34dc400a0cb5c7c31e5fd75e71
Author: <PERSON> <<EMAIL>>
Date:   Wed Apr 30 11:21:13 2025 +0200

    [BUGFIX] Avoid PHP errors with broken FE URL arguments

    When 'type', 'cHash' or '__RequestToken' URL arguments
    are arrays, FE triggers 'Array to string conversion'
    warning level PHP errors. TYPO3 never creates such links,
    this is usually "external manipulation". Still, the FE
    should not crash on this. The patch sanitizes these
    cases.

    Resolves: #104928
    Releases: main, 13.4
    Change-Id: I49c6c901d2ded4aef9252e6feac15c21b772d719
    Reviewed-on: https://review.typo3.org/c/Packages/TYPO3.CMS/+/89283
    Tested-by: <PERSON><PERSON> <<EMAIL>>
    Reviewed-by: <PERSON><PERSON> <<EMAIL>>
    Tested-by: <PERSON> <<EMAIL>>
    Reviewed-by: <PERSON> <<EMAIL>>
    Reviewed-by: <PERSON> <<EMAIL>>
    Tested-by: <PERSON> <<EMAIL>>
    Tested-by: core-ci <<EMAIL>>
    Tested-by: Christian Kuhn <<EMAIL>>
    Reviewed-by: Christian Kuhn <<EMAIL>>

diff --git a/Classes/Middleware/RequestTokenMiddleware.php b/Classes/Middleware/RequestTokenMiddleware.php
index 804ff6e3c5b..a9ac185dd9e 100644
--- a/Classes/Middleware/RequestTokenMiddleware.php
+++ b/Classes/Middleware/RequestTokenMiddleware.php
@@ -105,7 +105,10 @@ protected function resolveNoncePool(ServerRequestInterface $request): NoncePool
     protected function resolveReceivedRequestToken(ServerRequestInterface $request): ?RequestToken
     {
         $headerValue = $request->getHeaderLine(RequestToken::HEADER_NAME);
-        $paramValue = (string)($request->getParsedBody()[RequestToken::PARAM_NAME] ?? '');
+        $paramValue = '';
+        if (isset($request->getParsedBody()[RequestToken::PARAM_NAME]) && is_scalar($request->getParsedBody()[RequestToken::PARAM_NAME])) {
+            $paramValue = (string)($request->getParsedBody()[RequestToken::PARAM_NAME]);
+        }
         if ($headerValue !== '') {
             $tokenValue = $headerValue;
         } elseif (in_array($request->getMethod(), self::ALLOWED_METHODS, true)) {
diff --git a/Classes/Routing/PageRouter.php b/core/Classes/Routing/PageRouter.php
index b31032f98f0..a5ab224a5f0 100644
--- a/Classes/Routing/PageRouter.php
+++ b/Classes/Routing/PageRouter.php
@@ -106,18 +106,16 @@ public function matchRequest(ServerRequestInterface $request, ?RouteResultInterf

         // Legacy URIs (?id=12345) takes precedence, no matter if a route is given
         $requestId = ($request->getQueryParams()['id'] ?? null);
+        $type = '0';
+        if (isset($request->getQueryParams()['type']) && is_scalar($request->getQueryParams()['type'])) {
+            $type = (string)$request->getQueryParams()['type'];
+        }
         if ($requestId !== null) {
             if (MathUtility::canBeInterpretedAsInteger($requestId)
                 && (int)$requestId > 0
                 && !empty($pageId = $candidateProvider->getRealPageIdForPageIdAsPossibleCandidate((int)$requestId))
             ) {
-                return new PageArguments(
-                    (int)$pageId,
-                    (string)($request->getQueryParams()['type'] ?? '0'),
-                    [],
-                    [],
-                    $request->getQueryParams()
-                );
+                return new PageArguments((int)$pageId, $type, [], [], $request->getQueryParams());
             }
             throw new RouteNotFoundException('The requested page does not exist.', **********);
         }
@@ -562,7 +560,10 @@ protected function resolveType(Route $route, array &$remainingQueryParameters):
                 $decoratedParameters
             );
         }
-        return (string)$type;
+        if (is_scalar($type)) {
+            return (string)$type;
+        }
+        return '0';
     }

     /**
