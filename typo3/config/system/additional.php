<?php
/**
 * We have the following contexts:
 * - Production
 * - Production/Staging
 * - Development/Local
 */
$env = (string) TYPO3\CMS\Core\Core\Environment::getContext();

if ($env === 'Production' || $env === 'Production/Staging') {
    $GLOBALS['TYPO3_CONF_VARS']['DB']['Connections']['Default']['host']     = getenv('MYSQL_HOST');
    $GLOBALS['TYPO3_CONF_VARS']['DB']['Connections']['Default']['dbname']   = getenv('MYSQL_DATABASE');
    $GLOBALS['TYPO3_CONF_VARS']['DB']['Connections']['Default']['user']     = getenv('MYSQL_USER');
    $GLOBALS['TYPO3_CONF_VARS']['DB']['Connections']['Default']['password'] = getenv('MYSQL_PASSWORD');

    // Register exception handler
    $GLOBALS['TYPO3_CONF_VARS']['SYS']['productionExceptionHandler']
        = Networkteam\SentryClient\ProductionExceptionHandler::class;
    // Forward log messages to Sentry
    $GLOBALS['TYPO3_CONF_VARS']['LOG']['writerConfiguration'] = [
        \TYPO3\CMS\Core\Log\LogLevel::ERROR => [
            //\Networkteam\SentryClient\SentryLogWriter::class => [],
            \Mogic\Dbi\Exception\BetterSentryLogWriter::class => [],
        ],
    ];

} else if ($env === 'Development/Local') {
    $GLOBALS['TYPO3_CONF_VARS']['BE']['debug'] = true;
    $GLOBALS['TYPO3_CONF_VARS']['FE']['debug'] = true;
    $GLOBALS['TYPO3_CONF_VARS']['SYS']['displayErrors'] = '1';
    $GLOBALS['TYPO3_CONF_VARS']['SYS']['devIPmask'] = '*';

}

//MinIO file storage
if ($env === 'Production') {
    $GLOBALS['TYPO3_CONF_VARS']['EXTENSIONS']['aus_driver_amazon_s3']['storage_1'] = [
        'key'           => getenv('MINIO_PROD_USER'),
        'secretKey'     => getenv('MINIO_PROD_PASSWORD'),
        'publicBaseUrl' => getenv('MINIO_PROD_PUBLIC_URL'),
        'protocol'      => 'auto',
    ];

} else {
    $GLOBALS['TYPO3_CONF_VARS']['EXTENSIONS']['aus_driver_amazon_s3']['storage_1'] = [
        'key'           => getenv('MINIO_PROD_USER'),
        'secretKey'     => getenv('MINIO_PROD_PASSWORD'),
        'customHost'    => getenv('MINIO_PROD_CUSTOM_HOST'),
        'publicBaseUrl' => getenv('MINIO_PROD_PUBLIC_URL'),
        'protocol'      => 'https://',
    ];
    $GLOBALS['TYPO3_CONF_VARS']['EXTENSIONS']['aus_driver_amazon_s3']['storage_2'] = [
        'key'           => getenv('MINIO_LOCAL_USER'),
        'secretKey'     => getenv('MINIO_LOCAL_PASSWORD'),
        'publicBaseUrl' => getenv('MINIO_LOCAL_PUBLIC_URL'),
        'protocol'      => 'auto',
    ];
}

//Elanders MobileSales API
if ($env === 'Production') {
    //stored in keeper, see websitebuilder-docker/templates/docker-compose-prod.template
    $GLOBALS['TYPO3_CONF_VARS']['EXTENSIONS']['dbi']['elandersMsa'] = [
        'url'  => 'https://frontend.mobilesales.postbank.de',
        'user' => $_SERVER['ELANDERS_API_USER'],
        'pass' => $_SERVER['ELANDERS_API_PASSWORD'],
    ];
} else {
    $GLOBALS['TYPO3_CONF_VARS']['EXTENSIONS']['dbi']['elandersMsa'] = [
        'url'  => 'https://release.mobilesales.w2p-dev.elanders-germany.com',
        'user' => 'mogic',
        'pass' => 'B61ucZGoEFrk8bTXjyKCMR53cljyWyrJ',
    ];
}

//FIO
if ($env === 'Production' || $env === 'Production/Staging') {
    $GLOBALS['TYPO3_CONF_VARS']['EXTENSIONS']['dbi']['fio']['searchProfileApiPrefix']
        = 'https://portal.fio.de/api/v1/portals/d31e7e89-fb61-449d-8561-e61f1b6720a7/';

    $GLOBALS['TYPO3_CONF_VARS']['EXTENSIONS']['dbi']['fio']['webmakler'] = [
        'base_url'      => 'https://api.fio.de/webmakler/',
        'token_url'     => 'https://api.fio.de/auth/realms/vermarktung/protocol/openid-connect/token',
        'username'      => $_SERVER['FIO_WEBMAKLER_ID'],
        'password'      => $_SERVER['FIO_WEBMAKLER_PASSWORD'],
        'client_id'     => $_SERVER['FIO_WEBMAKLER_CLIENT_ID'],
        'client_secret' => $_SERVER['FIO_WEBMAKLER_CLIENT_SECRET'],
    ];

} else {
    $GLOBALS['TYPO3_CONF_VARS']['EXTENSIONS']['dbi']['fio']['searchProfileApiPrefix']
        = 'https://portal.fio.de/api_test/v1/portals/d31e7e89-fb61-449d-8561-e61f1b6720a7/';

    $GLOBALS['TYPO3_CONF_VARS']['EXTENSIONS']['dbi']['fio']['webmakler'] = [
        'base_url'      => 'https://api-staging.fio.de/webmakler/',
        'token_url'     => 'https://api-staging.fio.de/auth/realms/vermarktung/protocol/openid-connect/token',
        'username'      => $_SERVER['FIO_WEBMAKLER_ID'],
        'password'      => $_SERVER['FIO_WEBMAKLER_PASSWORD'],
        'client_id'     => $_SERVER['FIO_WEBMAKLER_CLIENT_ID'],
        'client_secret' => $_SERVER['FIO_WEBMAKLER_CLIENT_SECRET'],
    ];
}

//Maklaro
if ($env === 'Production') {
    $GLOBALS['TYPO3_CONF_VARS']['EXTENSIONS']['dbi']['maklaro'] = [
        'scriptUrl'    => 'https://slider.maklaro.com/src.latest.js',
        'partnerId'    => 'dbi-live',
        'sharedSecret' => 'fRE2k5bQ4sI9ZxjPWsUSeYR3',
        'leadFlowCode' => 'DBIwelcome',
    ];
} else {
    $GLOBALS['TYPO3_CONF_VARS']['EXTENSIONS']['dbi']['maklaro'] = [
        'scriptUrl'    => 'https://slider-staging.maklaro.com/src.latest.js',
        'partnerId'    => 'dbi-test',
        'sharedSecret' => 'T8JWTp1BoohwPRyJA1ne7JlZ',
        'leadFlowCode' => 'DBI_Welcome_Staging',
    ];
}

if ($env === 'Development/Local') {
    $extraConfFile = __DIR__ . '/additional.development.php';
    if (file_exists($extraConfFile)) {
        include $extraConfFile;
    }
}
