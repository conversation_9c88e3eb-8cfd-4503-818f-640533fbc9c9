{"name": "dbi", "version": "1.0.0", "main": "index.js", "license": "MIT", "type": "module", "scripts": {"minify:icons": "svgo -f ./Resources/Private/Icons/Mask -o ./Resources/Public/Icons/Mask && svgo -f ./Resources/Private/Icons/Poi -o ./Resources/Public/Icons/Theme/Poi", "build:icons": "yarn minify:icons && yarn build:spritemap", "build:spritemap": "npx svgstore -o ./Resources/Public/Icons/icon-spritemap.svg ./Resources/Public/Icons/Mask/**/*.svg", "css:dev": "npx @tailwindcss/cli -i ./Resources/Private/Css/main.css -o ./Resources/Public/Css/main.min.css --watch", "css:build": "npx @tailwindcss/cli -i ./Resources/Private/Css/main.css -o ./Resources/Public/Css/main.min.css --minify", "rte:build": "npx @tailwindcss/cli -i ./Resources/Private/Css/rte.css -o ./Resources/Public/Css/rte.min.css", "js:dev": "rollup --config --watch", "js:build": "rollup --config", "dev": "yarn build:icons && (yarn js:dev & yarn css:dev)", "build": "yarn build:icons && yarn rte:build  && yarn css:build && yarn js:build", "lint": "yarn eslint", "format:check": "prettier --check ."}, "dependencies": {"@rollup/plugin-commonjs": "^25.0.7", "@rollup/plugin-node-resolve": "^15.2.3", "@rollup/plugin-terser": "^0.4.4", "@tailwindcss/cli": "^4.1.6", "@tailwindcss/postcss": "^4.1.6", "@tailwindcss/typography": "^0.5.10", "alpinejs": "^3.13.3", "leaflet": "^1.9.4", "npm-run-all": "^4.1.5", "postcss": "^8.4.32", "rollup": "^4.9.1", "svgo": "^3.3.2", "svgstore-cli": "^2.0.1", "swiper": "^11.1.4", "tailwindcss": "^4.1.6"}, "devDependencies": {"@eslint/css": "^0.7.0", "@eslint/js": "^9.26.0", "eslint": "^9.26.0", "eslint-config-prettier": "^10.1.5", "globals": "^16.1.0", "prettier": "^3.5.3"}}