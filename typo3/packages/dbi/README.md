# Deutsche Bank immobilien - Typo3 Extension

## Stack

- Tailwind
- AlpineJS
- Rollup oder Vite (noch nicht ganz klar) - fürs erste erstmal rollup

## Local Development

### Docker

_kommt noch_

Plan: Über ein Make Task vom [Makefile](../../../Makefile) solltet ihr die Möglichkeit haben ein relevanten Node Container zu starten der idealerweise nach initialem Setup (`make up-new` oder `make up-new-keep-sources`) zur Verfügung steht. Mögliche Befehle könnten sein:
- `make build:assets`
- `make dev:assets`
- `make watch:assets`

### Manuell

#### Setup

Bitte besorgt euch den Node Version Manager und startet die lokale Entwicklungsumgebung mit: `nvm install`
Die hinterlegte [.nvmrc](.nvmrc) Datei sorgt dafür dass auch die richtige Version benutzt wird.

##### Alternativ

_Bitte die hinterlegte [.nvmrc](.nvmrc) beachten und die Node Version installieren die da als Versionshinweis hinterlegt ist._

#### Installieren

`yarn install`

#### Start

#### CSS (PostCSS, Tailwind) und JS (Rollup, Alpine)

`yarn dev`

