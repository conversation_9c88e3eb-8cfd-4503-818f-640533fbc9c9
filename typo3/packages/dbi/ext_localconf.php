<?php

use Mogic\Dbi\Controller\CookieManagementController;
use Mogic\Dbi\Controller\FioImmoProxyController;
use Mogic\Dbi\Controller\FioSearchProfileController;
use Mogic\Dbi\Controller\MarketPriceAssessmentController;
use Mogic\Dbi\Controller\ProjectListController;
use Mogic\Dbi\Controller\RealtorCenterListController;
use Mogic\Dbi\Controller\RealtorSearchController;
use Mogic\Dbi\Evaluation\EmptyToNullEvaluation;
use TYPO3\CMS\Core\Utility\ExtensionManagementUtility;
use TYPO3\CMS\Extbase\Utility\ExtensionUtility;

defined('TYPO3') or die('Access denied.');

ExtensionUtility::configurePlugin(
    'Dbi',
    'CookieManagement',
    [CookieManagementController::class => 'show,setEtracker'],
    [CookieManagementController::class => 'show,setEtracker']
);

ExtensionUtility::configurePlugin(
    'Dbi',
    'FioImmoProxyExpose',
    [FioImmoProxyController::class => 'expose'],
    [FioImmoProxyController::class => 'expose']
);

ExtensionUtility::configurePlugin(
    'Dbi',
    'FioImmoProxyList',
    [FioImmoProxyController::class => 'list'],
    [FioImmoProxyController::class => 'list']
);

ExtensionUtility::configurePlugin(
    'Dbi',
    'FioImmoProxySearch',
    [FioImmoProxyController::class => 'search'],
    [FioImmoProxyController::class => 'search']
);

ExtensionUtility::configurePlugin(
    'Dbi',
    'FioSearchProfileDelete',
    [FioSearchProfileController::class => 'delete'],
    [FioSearchProfileController::class => 'delete']
);

ExtensionUtility::configurePlugin(
    'Dbi',
    'MarketPriceAssessment',
    [MarketPriceAssessmentController::class => 'show'],
    []
);

ExtensionUtility::configurePlugin(
    'Dbi',
    'ProjectList',
    [ProjectListController::class => 'list'],
    []
);

ExtensionUtility::configurePlugin(
    'Dbi',
    'ProjectSlider',
    [ProjectListController::class => 'slider'],
    []
);

ExtensionUtility::configurePlugin(
    'Dbi',
    'RealtorCenterList',
    [RealtorCenterListController::class => 'list'],
    []
);

ExtensionUtility::configurePlugin(
    'Dbi',
    'RealtorSearchResults',
    [RealtorSearchController::class => 'results'],
    [RealtorSearchController::class => 'results']
);
ExtensionUtility::configurePlugin(
    'Dbi',
    'RealtorSearchSearchbar',
    [RealtorSearchController::class => 'searchbar,autocomplete'],
    [RealtorSearchController::class => 'searchbar,autocomplete']
);


// Register the class to be available in 'eval' of TCA
$GLOBALS['TYPO3_CONF_VARS']['SC_OPTIONS']['tce']['formevals'][EmptyToNullEvaluation::class] = '';

/***************
 * Fluid Components
 */
$GLOBALS['TYPO3_CONF_VARS']['EXTCONF']['fluid_components']['namespaces']['Mogic\\Dbi\\Components'] =
    \TYPO3\CMS\Core\Utility\ExtensionManagementUtility::extPath('dbi', 'Resources/Private/Components');

$GLOBALS['TYPO3_CONF_VARS']['EXTCONF']['fluid_components']['typeAliases']['Center'] = \Mogic\Dbi\Domain\Model\Center::class;

$GLOBALS['TYPO3_CONF_VARS']['EXTCONF']['fluid_components']['typeAliases']['Teammember'] = \Mogic\Dbi\Domain\Model\Teammember::class;

/***************
 * General
 */
ExtensionManagementUtility::addPageTSConfig(
    '@import "EXT:dbi/Configuration/TsConfig/Old/tt_content.tsconfig"'
);
ExtensionManagementUtility::addUserTSConfig(
    '@import "EXT:dbi/Configuration/TsConfig/User/Default.tsconfig"'
);

$GLOBALS['TYPO3_CONF_VARS']['GFX']['imagefile_ext'] = 'gif,jpg,jpeg,tif,tiff,bmp,pcx,tga,png,pdf,ai,svg,webp';

// Register custom RTE
if (empty($GLOBALS['TYPO3_CONF_VARS']['RTE']['Presets']['dbi'])) {
    $GLOBALS['TYPO3_CONF_VARS']['RTE']['Presets']['dbi'] = 'EXT:dbi/Configuration/RTE/Default.yaml';
}

// Generic Gallery
// Register Models
$GLOBALS['TYPO3_CONF_VARS']['EXTCONF']['fluid_components']['typeAliases']['GalleryCollection'] = \FelixNagel\GenericGallery\Domain\Model\GalleryCollection::class;
$GLOBALS['TYPO3_CONF_VARS']['EXTCONF']['fluid_components']['typeAliases']['GalleryItem'] = \FelixNagel\GenericGallery\Domain\Model\GalleryItem::class;


// Register Upgrade Wizard
$GLOBALS['TYPO3_CONF_VARS']['SC_OPTIONS']['ext/install']['update']['dbi_themeColorUpgradeWizard']
    = \Mogic\Dbi\Upgrades\ThemeColorUpgradeWizard::class;
