CREATE TABLE pages (
    projectdata mediumtext,
);

CREATE TABLE tx_dbi_center (
    uid int(11) NOT NULL auto_increment,
    pid int(11) DEFAULT '0' NOT NULL,
    fio_id varchar(50) DEFAULT '' NOT NULL,
    -- deprecated:
    fio_group_id varchar(32) DEFAULT '' NOT NULL,
    fio_group_ids varchar(64) DEFAULT '' NOT NULL,
    fio_group_name varchar(255) DEFAULT '' NOT NULL,

    name2 varchar(255) DEFAULT '' NOT NULL,
    address_street varchar(255) DEFAULT '' NOT NULL,
    address_postalcode varchar(16) DEFAULT '' NOT NULL,
    address_city varchar(255) DEFAULT '' NOT NULL,
    address_state varchar(16) DEFAULT '' NOT NULL,
    telephone varchar(32) DEFAULT '' NOT NULL,
    fax varchar(32) DEFAULT '' NOT NULL,
    email varchar(64) DEFAULT '' NOT NULL,
    loc_lat double(10,7) DEFAULT NULL,
    loc_long double(10,7) DEFAULT NULL,

    image_pageheader int(11) DEFAULT '0' NOT NULL,
    image_mainpage int(11) DEFAULT '0' NOT NULL,
    image_search int(11) DEFAULT '0' NOT NULL,
    image_awards int(11) DEFAULT '0' NOT NULL,

    contact_openinghours text DEFAULT '' NOT NULL,

    search_radius int(11) DEFAULT NULL,
    is_headquarter tinyint(4) DEFAULT '0' NOT NULL,
    redirect int(11) DEFAULT '0' NOT NULL,
    hide_in_search tinyint(4) DEFAULT '0' NOT NULL,

    tstamp int(11) unsigned DEFAULT '0' NOT NULL,
    crdate int(11) unsigned DEFAULT '0' NOT NULL,
    cruser_id int(11) unsigned DEFAULT '0' NOT NULL,
    deleted tinyint(4) unsigned DEFAULT '0' NOT NULL,
    hidden tinyint(4) unsigned DEFAULT '0' NOT NULL,
    starttime int(11) unsigned DEFAULT '0' NOT NULL,
    endtime int(11) unsigned DEFAULT '0' NOT NULL,

    t3ver_oid int(11) DEFAULT '0' NOT NULL,
    t3ver_id int(11) DEFAULT '0' NOT NULL,
    t3ver_wsid int(11) DEFAULT '0' NOT NULL,
    t3ver_label varchar(255) DEFAULT '' NOT NULL,
    t3ver_state tinyint(4) DEFAULT '0' NOT NULL,
    t3ver_stage int(11) DEFAULT '0' NOT NULL,
    t3ver_count int(11) DEFAULT '0' NOT NULL,
    t3ver_tstamp int(11) DEFAULT '0' NOT NULL,
    t3ver_move_id int(11) DEFAULT '0' NOT NULL,

    sys_language_uid int(11) DEFAULT '0' NOT NULL,
    l10n_parent int(11) DEFAULT '0' NOT NULL,
    l10n_diffsource mediumblob,

    PRIMARY KEY (uid),
    KEY parent (pid),
    KEY t3ver_oid (t3ver_oid,t3ver_wsid),
    KEY language (l10n_parent,sys_language_uid)
);

CREATE TABLE tx_dbi_cities (
    uid int(11) NOT NULL auto_increment,
    pid int(11) DEFAULT '0' NOT NULL,

    postalcode varchar(5) DEFAULT '' NOT NULL,
    city varchar(32) DEFAULT '' NOT NULL,
    district varchar(32) DEFAULT '' NOT NULL,

    tstamp int(11) unsigned DEFAULT '0' NOT NULL,
    crdate int(11) unsigned DEFAULT '0' NOT NULL,
    cruser_id int(11) unsigned DEFAULT '0' NOT NULL,
    deleted tinyint(4) unsigned DEFAULT '0' NOT NULL,
    hidden tinyint(4) unsigned DEFAULT '0' NOT NULL,

    PRIMARY KEY (uid),
    KEY parent (pid),
    KEY postalcode (postalcode),
    FULLTEXT citydistrict (city,district)
);

CREATE TABLE tx_dbi_teammember (
    uid int(11) NOT NULL auto_increment,
    pid int(11) DEFAULT '0' NOT NULL,
    pid_center int(11) DEFAULT '0' NOT NULL,

    name_lastname varchar(255) DEFAULT '' NOT NULL,
    name_firstname varchar(255) DEFAULT '' NOT NULL,
    name_salutation varchar(16) DEFAULT '',
    name_degree varchar(64) DEFAULT '',
    image int(11) DEFAULT '0',
    `function` varchar(255) DEFAULT '',
    identification varchar(64) DEFAULT '',
    contact_telephone varchar(32) DEFAULT '' NOT NULL,
    contact_mobile varchar(32) DEFAULT '',
    contact_fax varchar(32) DEFAULT '',
    contact_email varchar(64) DEFAULT '' NOT NULL,
    languages varchar(128) DEFAULT '' NOT NULL,

    awards_maklerempfehlung_id varchar(16) DEFAULT '' NOT NULL,
    awards_maklerempfehlung_hide tinyint(1) DEFAULT '0',
    awards_capital_image varchar(255) DEFAULT '',
    awards_capital_hide tinyint(1) DEFAULT '0',

    image_awards int(11) DEFAULT '0' NOT NULL,

    fio_user_id varchar(32) DEFAULT '' NOT NULL,
    fio_username varchar(64) DEFAULT '' NOT NULL,
    fio_group_id varchar(32) DEFAULT '' NOT NULL,

    tstamp int(11) unsigned DEFAULT '0' NOT NULL,
    crdate int(11) unsigned DEFAULT '0' NOT NULL,
    cruser_id int(11) unsigned DEFAULT '0' NOT NULL,
    deleted tinyint(4) unsigned DEFAULT '0' NOT NULL,
    hidden tinyint(4) unsigned DEFAULT '0' NOT NULL,
    starttime int(11) unsigned DEFAULT '0' NOT NULL,
    endtime int(11) unsigned DEFAULT '0' NOT NULL,
    sorting int(11) DEFAULT '0' NOT NULL,

    PRIMARY KEY (uid),
    KEY parent (pid),
    KEY fio_username (fio_username)
);

CREATE TABLE tx_dbi_tradingareas (
    uid int(11) NOT NULL auto_increment,
    pid int(11) DEFAULT '0' NOT NULL,

    postalcode varchar(5) DEFAULT '' NOT NULL,
    fio_username varchar(64) DEFAULT '' NOT NULL,
    population int(11) DEFAULT '0' NOT NULL,

    tstamp int(11) unsigned DEFAULT '0' NOT NULL,
    crdate int(11) unsigned DEFAULT '0' NOT NULL,
    cruser_id int(11) unsigned DEFAULT '0' NOT NULL,
    deleted tinyint(4) unsigned DEFAULT '0' NOT NULL,
    hidden tinyint(4) unsigned DEFAULT '0' NOT NULL,

    PRIMARY KEY (uid),
    KEY parent (pid),
    KEY postalcode (postalcode),
    KEY fio_username (fio_username)
);

CREATE TABLE tt_content (
    tx_dbi_container_background_color varchar(12) DEFAULT 'transparent' NOT NULL,
    tx_dbi_container_into_prev_container tinyint(4) DEFAULT '0' NOT NULL,
    tx_dbi_container_inset tinyint(4) DEFAULT '0' NOT NULL,
    tx_dbi_container_stretched tinyint(4) DEFAULT '0' NOT NULL,
    tx_dbi_container_divider tinyint(4) DEFAULT '0' NOT NULL,    tx_dbi_container_margin varchar(50) DEFAULT 'lg' NOT NULL,
    tx_dbi_container_nav_title varchar(255) DEFAULT NULL,
    tx_dbi_stage_divider tinyint(4) DEFAULT '1' NOT NULL,
    tx_dbi_stage_divide_slant tinyint(4) DEFAULT '1' NOT NULL,
    tx_dbi_stage_image int(11) unsigned DEFAULT '0' NOT NULL,
    tx_dbi_stage_placeholder_widget_show tinyint(4) DEFAULT '0' NOT NULL,
    tx_dbi_stage_placeholder_widget_headline varchar(255) DEFAULT '' NOT NULL,
    tx_dbi_stage_placeholder_widget_subline varchar(255) DEFAULT '' NOT NULL,
    tx_dbi_container_cta varchar(1024) DEFAULT '' NULL,
    tx_dbi_container_header_align varchar(12) DEFAULT 'base' NOT NULL,
);
