/** @type {import('tailwindcss').Config} */
module.exports = {
  corePlugins: {
    container: false,
  },
  darkMode: "class",
  content: [
    "./Resources/Private/Layouts/**/*.html",
    "./Resources/Private/Partials/**/*.html",
    "./Resources/Private/Components/**/*.html",
    "./Resources/Private/Templates/**/*.html",
  ],
  safelist: [
    "-translate-x-full",
    "translate-x-0",
    "text-right",
    "text-left",
    "text-justify",
    "underline",
    "list-disc",
    "list-decimal",
    "list-dash",
    "list-timeline",
    "list-check",
    "list-counter",
    "h-screen",
    "fixed",
    "top-0",
    "left-0",
    "powermail-errors-list",
    "clip-path-separator-slant-md",
    "clip-path-separator-slant-xl",
    "clip-path-separator-half",
    {
      pattern: /bg-gray-100/,
      variants: ["even-row", "odd-row"],
    },
    "swiper-slide-active",
  ],
  theme: {
    fontFamily: {
      sans: ['"DeutscheBankUI"', "sans-serif"],
    },
    colors: {
      transparent: "transparent",
      current: "currentColor",
      white: "#ffffff",
      black: "#000000",
      "black-opaque": "rgba(0,0,0,0.7)",
      "electric-brand": {
        50: "#eff7ff",
        100: "#e0ecff",
        200: "#c0defd",
        300: "#95cafb",
        400: "#5eaaf7",
        500: "#3e8cf3",
        600: "#296de7",
        700: "#2059d5",
        800: "#2148ac",
        900: "#204088",
      },
      brand: {
        50: "#f5f9fe",
        100: "#e7f4fe",
        200: "#bfd5f7",
        300: "#2b6bd9",
        400: "#0550d1",
        500: "#0d43b3",
        600: "#163796",
        700: "#1e2a78",
        800: "#1b246a",
        900: "#1a2061",
        950: "#16184e",
      },
      gray: {
        50: "#f7f7f7",
        100: "#f0f0f0",
        200: "#e1e1e1",
        300: "#cbcbcb",
        400: "#b6b6b6",
        500: "#a0a0a0",
        600: "#8a8a8a",
        700: "#747474",
        800: "#494949",
        900: "#494949",
        950: "#333333",
      },
      red: {
        50: "#ffeceb",
        100: "#f6dddd",
        200: "#ff8f85",
        300: "#ff695c",
        400: "#ff4433",
        500: "#e20505",
        600: "#b50404",
        700: "#880303",
        800: "#5a0202",
        900: "#2d0101",
      },
      yellow: {
        50: "#fef6e6",
        100: "#fceecc",
        200: "#ffe5b4",
        300: "#ffcb65",
        400: "#f5ba33",
        500: "#f2a900",
        600: "#c28700",
        700: "#995400",
        800: "#665a40",
        900: "#302200",
      },
      green: {
        50: "#ebf8f3",
        100: "#d6f1e6",
        200: "#dbebca",
        300: "#85d6b5",
        400: "#34bb84",
        500: "#25a62d",
        600: "#088242",
        700: "#054e28",
        800: "#324940",
        900: "#0a251a",
      },
    },
    extend: {
      aspectRatio: {
        none: "unset",
        image: "4 / 3",
        "image-wide": "2 / 1",
        "image-stage": "16 / 5",
      },
      borderRadius: {
        sm: "3px",
      },
      objectPosition: {
        "40%-center": "40% center",
      },
      gridTemplateColumns: {
        "custom-2": "2fr 1fr",
        "custom-2-reverse": "1fr 2fr",
        custom: "repeat(var(--columns), minmax(0, 1fr));",
      },
      gridTemplateRows: {
        custom: "repeat(var(--rows), minmax(0, 1fr));",
      },
      typography: {
        DEFAULT: {
          css: {
            color: "#000000",
          },
        },
      },
      clipPath: {
        "separator-half": "polygon(0 0, 50% 0%, 50% 100%, 0% 100%)",
        "separator-slant-md": "polygon(0 0, 70% 0, 40% 100%, 0% 100%)",
        "separator-slant-xl": "polygon(0 0, 63% 0, 40% 100%, 0% 100%)",
      },
    },
  },
  plugins: [
    require("@tailwindcss/typography"),
    function ({ matchUtilities, addUtilities, addVariant, theme }) {
      matchUtilities(
        {
          "divide-gap-x": (value) => {
            value = value === "0" ? "0px" : value;
            return {
              "& > :not([hidden]):not(:last-child)": {
                "padding-right": value,
              },
              "& > :not([hidden]) ~ :not([hidden])": {
                "padding-left": value,
              },
            };
          },
          "divide-gap-y": (value) => {
            value = value === "0" ? "0px" : value;
            return {
              "& > :not([hidden]):not(:last-child)": {
                "padding-bottom": value,
              },
              "& > :not([hidden]) ~ :not([hidden])": {
                "padding-top": value,
              },
            };
          },
          "clip-path-separator-half": (value) => {
            return {
              "clip-path": value || "polygon(0 0, 50% 0%, 50% 100%, 0% 100%)",
            };
          },
          "clip-path-separator-slant-md": (value) => {
            return {
              "clip-path": value || "polygon(0 0, 70% 0, 40% 100%, 0% 100%)",
            };
          },
          "clip-path-separator-slant-xl": (value) => {
            return {
              "clip-path": value || "polygon(0 0, 63% 0, 40% 100%, 0% 100%)",
            };
          },
        },
        {
          values: theme("spacing"),
        },
      ),
        addUtilities(
          {
            ".clip-path-separator-half": {
              "clip-path": "polygon(0 0, 50% 0%, 50% 100%, 0% 100%)",
            },
            ".clip-path-separator-slant-md": {
              "clip-path": "polygon(0 0, 70% 0, 40% 100%, 0% 100%)",
            },
            ".clip-path-separator-slant-xl": {
              "clip-path": "polygon(0 0, 63% 0, 40% 100%, 0% 100%)",
            },
          },
          ["hover", "focus"],
        );

      addVariant("even-row", "& tbody > tr:nth-child(even)");
      addVariant("odd-row", "& tbody > tr:nth-child(odd)");
      // matchUtilities({
      //   "even-row:bg": (value) => {
      //     return {
      //       "table > tbody > tr:nth-child(even)": {
      //           "background-color": value
      //       }
      //     }
      //   },
      //   "odd-row:bg": (value) => {
      //     return {
      //       "table > tbody > tr:nth-child(odd)": {
      //           "background-color": value
      //       }
      //     }
      //   }
      // },{
      //   values: theme("colors"),
      // })
    },
  ],
};
