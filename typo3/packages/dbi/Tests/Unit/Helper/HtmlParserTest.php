<?php

declare(strict_types=1);

use Mogic\Dbi\Helper\HtmlParser;

class HtmlParserTest extends PHPUnit\Framework\TestCase
{
    public function testGetTagContent(): void
    {
        $this->assertEquals(
            'Immobilien - 1',
            HtmlParser::getTagContent(
                file_get_contents(__DIR__ . '/HtmlParserTest.head1.htm'),
                'title'
            )
        );
    }

    public function testGetAllEmptyTags(): void
    {
        $this->assertEquals(
            [
                [
                    'name' => 'link',
                    'attributes' => [
                        'data-n-head' => 'ssr',
                        'rel' => 'icon',
                        'type' => 'image/x-icon',
                        'href' => '/fio-logo-favicon.svg',
                    ],
                    'value' => null,
                ],
                [
                    'name' => 'link',
                    'attributes' => [
                        'data-n-head' => 'ssr',
                        'rel'  => 'preload',
                        'href' => 'https://portal.fio.de/api_test/v2/styles/portalintegration/3',
                        'as'   => 'style',
                    ],
                    'value' => null,
                ],
            ],
            HtmlParser::getAllEmptyTags(
                <<<HTM
<meta data-n-head="ssr" charset="utf-8">
<link data-n-head="ssr" rel="icon" type="image/x-icon" href="/fio-logo-favicon.svg"><link data-n-head="ssr" rel="preload" href="https://portal.fio.de/api_test/v2/styles/portalintegration/3" as="style">
HTM,
                'link'
            )
        );
    }

    public function testGetAllNonEmptyTags(): void
    {
        $this->assertEquals(
            [
                [
                    'name' => 'style',
                    'attributes' => [
                        'data-vue-ssr-id' => '99c594aa:0 9f412a32:0 429a8ffd:0',
                    ],
                    'value' => <<<VAL

@font-face {
  font-family: 'swiper-icons';
}

VAL,
                ],
                [
                    'name' => 'style',
                    'attributes' => [
                        'type' => 'text/css',
                    ],
                    'value' => <<<VAL

.yo {}

VAL,
                ],
            ],
            HtmlParser::getAllNonEmptyTags(
                <<<HTM
<meta data-n-head="ssr" charset="utf-8">
<link data-n-head="ssr" rel="icon" type="image/x-icon" href="/fio-logo-favicon.svg">
<style data-vue-ssr-id="99c594aa:0 9f412a32:0 429a8ffd:0">
@font-face {
  font-family: 'swiper-icons';
}
</style>
<style type="text/css">
.yo {}
</style>
HTM,
                'style'
            )
        );
    }
}
