<?php

declare(strict_types=1);

use Mogic\Dbi\Helper\HtmlFixer;
use GuzzleHttp\Psr7\Utils as Psr7Utils;

class HtmlFixerTest extends PHPUnit\Framework\TestCase
{
    public function testAbsolutifyCssLinks(): void
    {
        $this->assertEquals(
            <<<CSS
@font-face{font-display:block;src:url(https://example.org/_nuxt/a.woff2) format("woff2"),url(https://example.org/_nuxt/b.ttf) format("truetype")}
@font-face {
  font-family: 'swiper-icons';
  src: url("data:application/font-woff;charset=utf-8;base64, d09GRgAB")
}
CSS,
            HtmlFixer::absolutifyCssLinks(
                <<<CSS
@font-face{font-display:block;src:url(/_nuxt/a.woff2) format("woff2"),url(/_nuxt/b.ttf) format("truetype")}
@font-face {
  font-family: 'swiper-icons';
  src: url("data:application/font-woff;charset=utf-8;base64, d09GRgAB")
}
CSS,
                Psr7Utils::uriFor('https://example.org/abc')
            )
        );
    }

    public function testAbsolutifyBodyLinks(): void
    {
        $this->assertEquals(
            <<<HTM
<img src="https://example.org/abc/fallback.svg" srcset="">
<img alt="23" src="https://portal.fio.de/cdntest"/>
<script src="https://example.org/_nuxt/6dc2cac.modern.js" defer></script>
<linkrel="stylesheet" href="https://example.org/abc/css/dbi-fonts.css" data-body="true">
HTM,
            HtmlFixer::absolutifyBodyLinks(
                <<<HTM
<img src="./fallback.svg" srcset="">
<img alt="23" src="https://portal.fio.de/cdntest"/>
<script src="/_nuxt/6dc2cac.modern.js" defer></script>
<linkrel="stylesheet" href="./css/dbi-fonts.css" data-body="true">
HTM,
                Psr7Utils::uriFor('https://example.org/abc/def')
            )
        );
    }
}
