<?php

use TYPO3\CMS\Core\DataHandling\PageDoktypeRegistry;
use TYPO3\CMS\Core\Utility\GeneralUtility;

defined('TYPO3') or die('Access denied.');

//custom page types
$dokTypeRegistry = GeneralUtility::makeInstance(PageDoktypeRegistry::class);
// page type: Immobilienprojekt
$dokTypeRegistry->add(
    11,
    [
        'type' => 'web',
        'allowedTables' => '*',
    ],
);

//custom record save handler
$GLOBALS['TYPO3_CONF_VARS']['SC_OPTIONS']['t3lib/class.t3lib_tcemain.php']['processDatamapClass'][]
    = 'Mogic\Dbi\Backend\TceMainHook';
