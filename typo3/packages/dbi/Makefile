checkstyle:
	cd ../../../ && make -s checkstyle

clear-cache:
	cd ../../../ && make -s typo3-clear-cache

build-frontend-assets:
	cd ../../../ && make -s build-frontend-assets

local-checkstyle:
	find Resources/Private/ -iname '*.html'\
	    | grep -v Resources/Private/Components/Carousel/Carousel.html\
	    | grep -v Resources/Private/Components/Accordion/Accordion.html\
	    | xargs -L1 xmllint --noout
	find Resources/Private/ -iname '*.html'\
	    | grep -v Resources/Private/Components/Carousel/Carousel.html\
	    | grep -v Resources/Private/Components/Accordion/Accordion.html\
	    | xargs -L1 xmllint --noout 2>&1\
	    | wc -l | xargs test 0 -eq

checkstyle-frontend:
	yarn run lint

checkstyle-format:
	yarn run format:check
