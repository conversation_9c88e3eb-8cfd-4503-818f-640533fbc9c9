import { nodeResolve } from '@rollup/plugin-node-resolve';
import terser from '@rollup/plugin-terser';
import commonjs from '@rollup/plugin-commonjs';

export default {
  input: {
    main: 'Resources/Private/JavaScript/main.js',
  },
  output: {
    dir: 'Resources/Public/JavaScript/',
    entryFileNames: '[name].min.js',
    format: 'iife',
  },
  plugins: [
    nodeResolve(),
    commonjs(),
    terser(),
  ],
};
