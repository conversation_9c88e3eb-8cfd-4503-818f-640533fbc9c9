<?xml version="1.0" encoding="utf-8"?>
<html
  data-namespace-typo3-fluid="true"
  xmlns="http://www.w3.org/1999/xhtml"
  xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers"
  xmlns:dbic="http://typo3.org/ns/Mogic/Dbi/Components"
  xmlns:x-bind="http://example.org/dummy-ns"
  xmlns:x-on="http://example.org/dummy-ns"
  xmlns:v="http://typo3.org/ns/TYPO3/CMS/Vhs/ViewHelpers"
>
  <f:section name="MenuItem">
    <li class="{listItemClasses}" x-on:click.outside="handleClickOutside">
        <f:variable name="hasActiveChildren" value="0" />
        <f:for each="{item.children}" as="child">
            <f:if condition="{child.active} == 1">
                <f:variable name="hasActiveChildren" value="1" />
            </f:if>
        </f:for>
        <a
            href="{item.link}"
            title="{item.title}"
            target="{item.target -> f:if(condition: item.target, then: item.target, else: '_self')}"
            class="mainNavigationLink {anchorClasses}"
            x-on:click.stop="onClick($event)"
        >
            {item.title}
        </a>

        <f:if condition="{item.children -> f:count()} > 0">
            <ul
                class="group-hover:opacity-100 group-hover:visible group-hover:translate-y-2
                    group-focus-within:opacity-100 group-focus-within:visible group-focus-within:translate-y-2
                    transition-all duration-300 ease-out flex-col absolute top-13 z-10 px-7.5 py-2 bg-gray-50 border-b border-brand-400"
            >
                <f:for each="{item.children}" as="item" iteration="i">
                    <f:variable name="additionalItemMenuListClasses" value="text-brand-950"/>
                    <f:variable name="additionalAnchorClasses" value=""/>
                    <f:if condition="{item.active}">
                        <f:variable name="additionalItemMenuListClasses" value="text-brand-400"/>
                        <f:variable name="additionalAnchorClasses" value="before:content-[''] before:inline-block before:absolute before:w-2 before:h-2 before:bg-brand-400 before:rounded-full before:top-4 before:-left-4"/>
                    </f:if>
                    <f:variable name="subMenuItemBorder" value="border-b border-gray-100" />
                    <f:render
                        section="SubMenuItem"
                        arguments="{
                            item: item,
                            listItemClasses: 'relative hover:text-brand-600 text-base py-2 {subMenuItemBorder} {additionalItemMenuListClasses}',
                            anchorClasses: '{additionalAnchorClasses}'

                        }"
                    />
                </f:for>
            </ul>
        </f:if>
    </li>
  </f:section>

  <f:section name="SubMenuItem">

    <li class="{listItemClasses}">
        <a href="{item.link}" title="{item.title}" target="{item.target -> f:if(condition: item.target, then: item.target, else: '_self')}" class="cursor-pointer {anchorClasses}">
            {item.title}
        </a>
    </li>
  </f:section>

  <f:section name="MenuListItems">
    <f:for each="{menu}" as="item" iteration="i">
      <f:variable name="additionalItemMenuListClasses"
        value="" />
      <f:variable name="itemIsActive"
        value="{item.active}" />

      <f:if condition="{item.data.shortcut}">
        <f:if condition="{item.data.shortcut} == {page.uid}">
          <f:variable name="itemIsActive"
            value="1" />
        </f:if>
      </f:if>

      <f:if condition="{itemIsActive}">
        <f:if condition="{type} == 'mainMenu'">
          <f:variable name="additionalItemMenuListClasses"
            value="underline" />
        </f:if>
      </f:if>

      <f:render
        section="MenuItem"
        arguments="{
          item: item,
          listItemClasses: 'group relative flex items-center sm:font-xs lg:font-base pt-2 pb-4 hover:underline focus:underline underline-offset-8 {additionalItemMenuListClasses}',
          anchorClasses: 'pt-2 pb-1 text-base',
        }"
      ></f:render>
    </f:for>
  </f:section>

  <f:section name="SubMenuListItems">
    <f:for each="{menu}" as="item" iteration="i">
      <f:variable name="additionalAnchorClasses"
        value=" hover:text-brand-500" />

      <f:if condition="{item.active}">
        <f:variable name="additionalAnchorClasses"
          value="text-brand-200" />
      </f:if>

      <f:render
        section="MenuItem"
        arguments="{
          item: item,
          listItemClasses: 'flex lg:px-2 items-center',
          anchorClasses: 'py-2.5 text-base {additionalAnchorClasses}'
        }"
      />
    </f:for>
  </f:section>

  <f:section name="MenuListItemsWithSubMenus">
    <f:for each="{menu}" as="item" iteration="i">
      <f:variable name="menuClassses" value="py-4 text-base" />
      <f:if condition="{item.active}">
        <f:variable name="menuClassses" value="{menuClassses} relative text-brand-200" />
      </f:if>
      <li x-data="{open: false, id: $id('dropdown-button') }" x-on:click="open = !open">
        <span class="flex items-center justify-between px-10">
          <div class="flex gap-2 items-center">
            <f:if condition="{item.active}">
              <span class="w-2 h-2 bg-brand-200 rounded-full"></span>
            </f:if>
            <a href="{item.link}" title="{item.title}" target="{link.target -> f:if(condition: link.target, then: link.target, else: '_self')}" class="{menuClassses} cursor-pointer hover:text-brand-500">
            {item.title}
            </a>
          </div>
          <f:if condition="{item.hasSubpages}">
            <button
              type="button"
              x-bind:aria-expanded="open"
              x-bind:aria-controls="id"
              class="block origin-center transition duration-200 cursor-pointer"
              x-bind:class="{'rotate-180': open}"
              x-bind:aria-label="open ? 'Nnavigation zuklappen' : 'Navigation aufklappen'"
            >
              <dbic:icon name="chevron-down" class="size-5" />
            </button>
          </f:if>
        </span>
        <f:if condition="{item.hasSubpages}">
          <ul x-bind:id="id" x-show="open" class="bg-brand-800 px-10 sm:px-16 py-4 shadow-inner">
            <f:render section="SubMenuListItems" arguments="{menu: item.children}" />
          </ul>
        </f:if>
      </li>
    </f:for>
  </f:section>

  <f:section name="Menu">
    <nav aria-label="Primärnavigation" class="{menuClasses}" id="{type}">
      <ul class="{menuListClasses}">
        <f:render section="MenuListItems" arguments="{menu: menu, subMenu: subMenu page: page, type: type}" />
      </ul>
    </nav>
  </f:section>

  <f:section name="Main">
    <f:render section="Menu" arguments="{
        menu: mainMenu,
        page: data,
        menuClasses: 'hidden lg:flex relative text-sm leading-none text-white',
        menuListClasses: 'mainNavigation flex relative z-10 w-full lg:gap-6 xl:gap-8',
        type: 'mainMenu'
    }">
    </f:render>
  </f:section>
</html>
