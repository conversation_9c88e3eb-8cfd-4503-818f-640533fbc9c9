<?xml version="1.0" encoding="utf-8"?>
<html
  data-namespace-typo3-fluid="true"
  xmlns="http://www.w3.org/1999/xhtml"
  xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers"
  xmlns:x-bind="http://example.org/dummy-ns"
  xmlns:x-on="http://example.org/dummy-ns"
>
  <f:section name="BurgerMenuIcon">
    <svg viewBox="0 0 20 20" fill="none" aria-hidden="true">
      <line x1="2.5" y1="5" x2="17.5" y2="5"
        stroke="currentcolor" stroke-linecap="round"
        x-bind:class="{'hidden': !!open}"
      />

      <line x1="2.5" y1="10" x2="17.5" y2="10"
        stroke="currentcolor" stroke-linecap="round"
        class="transition-transform duration-200"
        x-bind:transform="!!open ? 'rotate(-45 10 10)' : ''"
      />
      <line x1="2.5" y1="10" x2="17.5" y2="10"
        stroke="currentcolor" stroke-linecap="round"
        class="transition-transform duration-200"
        x-bind:transform="!!open ? 'rotate(45 10 10)' : ''"
      />

      <line x1="2.5" y1="15" x2="17.5" y2="15"
        stroke="currentcolor"  stroke-linecap="round"
        x-bind:class="{'hidden': !!open}"
      />
    </svg>
  </f:section>

  <f:section name="Main">
    <nav aria-label="Primärnavigation für mobile Geräte" class="flex lg:hidden items-center ml-3 md:ml-6" x-bind="focusTrapBinding">
      <button
        type="button"
        class="size-8 text-white "
        aria-controls="mobile-menu"
        x-bind:aria-expanded="open"
        x-on:click="toggle"
        x-bind:aria-label="open ? 'Primärnavigation zuklappen' : 'Primärnavigation aufklappen'"
      >
        <f:render section="BurgerMenuIcon" />
      </button>

      <ul id="mobile-menu"
        x-bind:class="open
            ? 'opacity-100 translate-y-0 pointer-events-auto visible'
            : 'opacity-0 -translate-y-4 pointer-events-none invisible'"
        class="absolute transition-all duration-200 ease-out
           left-0 flex flex-col top-20 w-full h-dvh overflow-auto pt-4 pb-20
            bg-brand-700 text-white leading-none divide-y divide-white/20 z-50"
        x-cloak=""
      >
        <f:render partial="Navigation/Main" section="MenuListItemsWithSubMenus"
          arguments="{menu: mainMenu}" />
          <li class="sticky bottom-0 mt-auto divide-y divide-white/20">
            <ul>
                <f:render partial="Navigation/Support" section="MenuListItems"
                arguments="{
                    menu: headerSupportMenu,
                    class: 'px-10 py-4',
                    itemClasses: 'bg-brand-950',
                }" />
            </ul>
          </li>
      </ul>
    </nav>
  </f:section>
</html>
