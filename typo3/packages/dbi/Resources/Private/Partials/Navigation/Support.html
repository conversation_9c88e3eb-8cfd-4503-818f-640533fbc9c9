<?xml version="1.0" encoding="utf-8"?>
<html
  data-namespace-typo3-fluid="true"
  xmlns="http://www.w3.org/1999/xhtml"
  xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers"
  xmlns:dbic="http://typo3.org/ns/Mogic/Dbi/Components"
>
  <f:section name="MenuListItems">
    <f:for each="{menu}" as="item" iteration="i">
        <f:variable name="classes" value="{class}" />
        <f:if condition="{activeClasses}">
          <f:variable name="itemIsActive"
            value="{item.active}" />
          <f:if condition="{item.data.shortcut}">
            <f:if condition="{item.data.shortcut} == {page.uid}">
              <f:variable name="itemIsActive"
                value="1" />
            </f:if>
          </f:if>

          <f:if condition="{itemIsActive}">
            <f:variable name="classes" value="{class} {activeClasses}" />
          </f:if>
        </f:if>

      <li class="flex cursor-pointer {itemClasses}">
        <a href="{item.link}" title="{item.title}" target="{item.target -> f:if(condition: item.target, then: item.target, else: '_self')}" class="{classes}">
          {item.title}
        </a>
      </li>
    </f:for>
  </f:section>

  <f:section name="Header">
    <nav aria-label="Sekundärnavigation" class="text-sm flex gap-x-4 lg:gap-x-6 py-2 items-start">
      <ul class="hidden lg:flex gap-x-4 leading-normal h-9 items-center">
        <f:render section="MenuListItems" arguments="{
          menu: headerSupportMenu,
          class: 'hover:underline underline-offset-8 text-white',
          page: data,
        }" />
      </ul>
    </nav>
  </f:section>

  <f:section name="Footer">
    <nav aria-label="Fußzeile Navigation">
      <ul class="flex gap-6 justify-start flex-wrap">
        <f:render section="MenuListItems" arguments="{
          menu: footerSupportMenu,
          class: 'flex items-center gap-x-2',
        }" />
      </ul>
    </nav>
  </f:section>
</html>
