<?xml version="1.0" encoding="utf-8"?>
<html
  data-namespace-typo3-fluid="true"
  xmlns="http://www.w3.org/1999/xhtml"
  xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers"
  xmlns:dbic="http://typo3.org/ns/Mogic/Dbi/Components"
  xmlns:x-bind="http://example.org/dummy-ns"
  xmlns:x-on="http://example.org/dummy-ns"
>
  <f:section name="MenuListItems">
    <f:for each="{menu}" as="item" iteration="i">
      <div x-data="{open: false}" class="flex border-b border-gray-500 md:border-none flex-col gap-5 md:gap-6 pt-5 pb-6 md:p-0">
        <span class="text-white text-base hover:text-brand-200 md:hover:text-white flex justify-between leading-none" x-on:click="open = !open">
          {item.title}
          <f:if condition="{item.children}">
            <button
                type="button"
                class="block md:hidden origin-center transition duration-500"
                x-bind:class="{'rotate-180': !!open}"
                x-bind:aria-label="open ? ' {item.title} zuklappen' : '{item.title} aufklappen'"
            >
              <dbic:icon name="chevron-down" class="size-5" />
            </button>
          </f:if>
        </span>
        <f:if condition="{item.children}">
          <nav aria-label="Footer-Navigation: {item.title}" x-bind:class="{'hidden md:block': !open}">
            <ul class="flex flex-col gap-4">
              <f:render section="SubMenuListItems" arguments="{menu: item.children}" />
            </ul>
          </nav>
        </f:if>
      </div>
    </f:for>
  </f:section>

  <f:section name="SubMenuListItems">
    <f:for each="{menu}" as="item" iteration="i">
      <li class="flex">
        <a href="{item.link}" title="{item.title}" target="{item.target -> f:if(condition: item.target, then: item.target, else: '_self')}" class="inline-flex text-sm text-brand-200 hover:bg-brand-800 hover:underline gap-x-2 items-center leading-normal">
          {item.title}
        </a>
      </li>
    </f:for>
  </f:section>

  <f:section name="Main">
    <f:render section="MenuListItems" arguments="{menu: footerMenu}" />
  </f:section>
</html>
