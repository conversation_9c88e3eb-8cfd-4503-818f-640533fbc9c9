<html
  xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
  xmlns:v="http://typo3.org/ns/TYPO3/CMS/Fluid/Vhs"
  xmlns:dbic="http://typo3.org/ns/Mogic/Dbi/Components"
  data-namespace-typo3-fluid="true"
  >
  <f:section name="Main">


    <footer class="bg-brand-950 text-brand-200 page-width page-padding">
        <f:if condition="{specialFooterElementPageUid}">
            <div class="bg-brand-950">
                <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 md:gap-x-6 gap-y-4 container mx-auto *:items-start py-6">
                    <v:content.render pageUid="{specialFooterElementPageUid}" column="0" />
                </div>
            </div>
        </f:if>
      <div class="h-1 border-b border-gray-500 bleeding-content"></div>
      <div class="container py-8 xl:px-0">
        <div class="grid grid-cols-1 md:grid-cols-2 md:gap-x-6 md:gap-y-8 xl:grid-cols-4">
          <f:render partial="Navigation/Footer" section="Main" arguments="{_all}" />
          <ul class="flex xl:justify-self-end items-start gap-6 xl:gap-0 pt-5 md:pt-1.5">
            <f:for each="{awards}" as="awardimage">
              <li>
                <dbic:image image="{awardimage}" ratio="" class="flex-1 w-full max-w-32 md:max-w-48 h-40 xl:w-45 md:h-57 xl:max-w-none object-contain"/>
              </li>
            </f:for>
          </ul>
        </div>
      </div>
      <div class="h-1 border-b border-gray-500 bleeding-content"></div>
      <div class="container py-8 xl:px-0">
        <div class="flex flex-col gap-6 md:flex-row md:justify-between text-sm leading-normal">
          <div class="text-white">
            © <f:format.date format="Y">now</f:format.date> {copyright}
          </div>
          <f:render partial="Navigation/Support" section="Footer" arguments="{_all}" />
        </div>
      </div>
    </footer>
  </f:section>
</html>
