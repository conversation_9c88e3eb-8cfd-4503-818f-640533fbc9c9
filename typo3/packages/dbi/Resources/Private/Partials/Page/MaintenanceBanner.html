<?xml version="1.0" encoding="utf-8"?>
<html xmlns="http://www.w3.org/1999/xhtml" lang="en"
    xmlns:dbi="http://typo3.org/ns/Mogic/Dbi/ViewHelpers"
    xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers"
    xmlns:v="http://typo3.org/ns/FluidTYPO3/Vhs/ViewHelpers"
    xmlns:dbic="http://typo3.org/ns/Mogic/Dbi/Components"
    xmlns:x-on="http://example.org/dummy-ns"
  >
  <f:section name="MaintenanceBanner">
    <f:if condition="{v:variable.typoscript(path: 'plugin.tx_dbi.settings.maintenanceBanner.active')} == 1">
      <f:variable name="text" value="{v:variable.typoscript(path: 'plugin.tx_dbi.settings.maintenanceBanner.text')}"/>
      <f:variable name="maintenanceCtaUid" value="{v:variable.typoscript(path: 'plugin.tx_dbi.settings.maintenanceBanner.btnPid')}" />
      <f:variable name="maintenanceCtaTitle" value="{v:variable.typoscript(path: 'plugin.tx_dbi.settings.maintenanceBanner.btnText')}" />

      <div x-data="maintenanceBanner()"
        data-hash="{dbi:hash.md5(string: text)}"
        x-show="show"
        x-cloak=""
        x-on:hidealert="setMaintenanceCookie();show=false"
        class="my-8 page-width"
        id="maintenanceBanner"
      >
        <f:if condition="{maintenanceCtaUid}">
          <f:then>
            <dbic:alert type="info" closeable="1" cta="{
              link: maintenanceCtaUid,
              title: maintenanceCtaTitle
            }">
              {text}
            </dbic:alert>
          </f:then>
          <f:else>
            <dbic:alert type="info" closeable="1">
              {text}
            </dbic:alert>
          </f:else>
        </f:if>
      </div>
    </f:if>
  </f:section>
</html>
