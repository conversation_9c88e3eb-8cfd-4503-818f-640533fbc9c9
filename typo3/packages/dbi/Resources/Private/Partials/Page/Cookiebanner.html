<?xml version="1.0" encoding="utf-8"?>
<html
  xmlns="http://www.w3.org/1999/xhtml"
  lang="en"
  xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers"
  xmlns:v="http://typo3.org/ns/FluidTYPO3/Vhs/ViewHelpers"
  xmlns:dbic="http://typo3.org/ns/Mogic/Dbi/Components"
  xmlns:x-bind="http://example.org/dummy-ns"
  xmlns:x-on="http://example.org/dummy-ns"
  xmlns:x-ref="http://example.org/dummy-ns"
  xmlns:x-data="http://example.org/dummy-ns"
  xmlns:x-cloak="http://example.org/dummy-ns"
  xmlns:x-show="http://example.org/dummy-ns"
>
  <f:section name="p">
    <v:variable.set
      name="pidCookiebannerContent"
      value="{v:variable.typoscript(path: 'plugin.tx_dbi.settings.cookiebanner.content')}"
    />
    <f:if condition="{pidCookiebannerContent}">
      <f:comment><!-- Don't ask. TYPO3 v7 fluid is cruel. --></f:comment>
      <v:variable.set
        name="pidsCookiebannerHide"
        value="{v:variable.typoscript(path:'plugin.tx_dbi.settings.cookiebanner.hide')}"
      />
      <v:variable.set
        name="pidsCookiebannerHide"
        value="{v:iterator.explode(glue: ',', content: pidsCookiebannerHide)}"
      />
      <v:variable.set name="pageUid" value="{v:page.info(field: 'uid')}" />
      <v:variable.set
        name="indexOfPid"
        value="{v:iterator.indexOf(needle: pageUid, haystack: pidsCookiebannerHide)}"
      />
      <f:if condition="{indexOfPid} == -1">
        <f:render
          section="Banner"
          arguments="{pidCookiebannerContent: pidCookiebannerContent}"
        />
      </f:if>
    </f:if>
  </f:section>

  <f:section name="Banner">
    <dialog
      id="cookiebanner"
      class="max-w-main max-h-full w-full h-full top-1/2 left-1/2 -translate-1/2 backdrop:bg-white backdrop:opacity-40 bg-transparent"
      x-bind="dialogBinding"
      x-data="cookieBanner()"
      x-ref="cookieDialog"
    >
      <div
        class="group/cookiebanner absolute top-1/2 left-1/2 -translate-1/2  max-w-4xl h-full w-full md:h-auto md:mt-0 py-10 px-6 md:px-10 flex flex-col gap-6 bg-white shadow-lg overflow-y-auto "
      >

        <dbic:logo
          hide-wordmark="1"
          class="size-12 md:size-16 shrink-0 mb-2 text-brand-700"
        />

        <div
          class="flex flex-col gap-6 text-brand-950 *:*:first:font-medium *:*:not-last:mb-6 *:*:*:not-last:mb-6"
        >
          <v:content.render pageUid="{pidCookiebannerContent}" column="0" />
        </div>

        <section class="flex flex-col gap-6 md:flex-row md:flex-wrap">
          <dbic:button
            type="button"
            class="cursor-pointer"
            additionalAttributes="{'x-on:click.prevent': 'allowCookies'}"
          >
            Einwilligen
          </dbic:button>
          <dbic:button
            type="button"
            theme="secondary"
            class="cursor-pointer"
            additionalAttributes="{'x-on:click.prevent': 'declineCookies'}"
          >
            Ablehnen
          </dbic:button>
        </section>

        <f:variable name="additionalLinksObject"
          ><v:content.get pageUid="{pidCookiebannerContent}" column="1"
        /></f:variable>
        <f:if condition="{additionalLinksObject}">
          <div
            class="flex flex-col md:flex-row md:flex-wrap gap-6 *:self-start"
          >
            <f:for
              each="{additionalLinksObject}"
              as="link"
              iteration="iterator"
            >
              <f:cObject typoscriptObjectPath="lib.tx_mask.content">
                {link.uid}
              </f:cObject>
            </f:for>
          </div>
        </f:if>
      </div>
    </dialog>

    <div
      id="cookiebannerdnt"
      class="bottom-10 bg-white shadow-lg md:bottom-auto md:top-20 md:right-10 md:w-96 mx-6 py-5 px-6 z-1000 overflow-hidden"
      x-cloak=""
      x-data="dntBanner()"
      x-show="enabled"
      x-bind:class="{'fixed': enabled }"
    >
      <v:content.render pageUid="{pidCookiebannerContent}" column="2" />
      <div
        class="absolute bottom-0 left-0 w-full h-1 bg-brand-500 -translate-x-full transition-transform ease-linear duration-[9s]"
        data-progressbar=""
        x-ref="progressbar"
      ></div>
    </div>
  </f:section>
</html>
