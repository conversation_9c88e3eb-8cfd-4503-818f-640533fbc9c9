<html
	xmlns:vh="http://typo3.org/ns/In2code/Powermail/ViewHelpers"
	xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
  	xmlns:dbic="http://typo3.org/ns/Mogic/Dbi/Components"
  	data-namespace-typo3-fluid="true"
>
    <f:variable name="alertType" value="" />
    <f:if condition="{warningsExist}">
        <f:variable name="alertType" value="warning" />
    </f:if>
    <f:if condition="{noticesExist}">
        <f:variable name="alertType" value="info" />
    </f:if>
    <f:if condition="{errorsExist}">
        <f:variable name="alertType" value="error" />
    </f:if>

    <f:form.validationResults>
        <f:if condition="{validationResults.flattenedErrors}">
            <div class="flex flex-col gap-2 mb-5">
                <f:for each="{validationResults.flattenedErrors}" as="errors">
                    <f:for each="{errors}" as="singleError">
                        <dbic:alert type="{alertType}" closeable="true">
                            <f:if condition="{singleError.message} == 'spam_details'">
                                <f:then>
                                    <f:translate key="validationerror_spam" /> {singleError.arguments.spamfactor}
                                </f:then>
                                <f:else>
                                    <f:if condition="{singleError.arguments.marker}">
                                        <vh:getter.getFieldPropertyFromMarkerAndForm marker="{singleError.arguments.marker}" form="{form}" property="title"/>:
                                    </f:if>
                                </f:else>
                            </f:if>
                            <f:translate key="validationerror_{singleError.message}">{singleError.message}</f:translate>
                        </dbic:alert>
                    </f:for>
                </f:for>
            </div>
        </f:if>
    </f:form.validationResults>
</html>
