<html
	xmlns:vh="http://typo3.org/ns/In2code/Powermail/ViewHelpers"
	xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
  	xmlns:dbic="http://typo3.org/ns/Mogic/Dbi/Components"
  	data-namespace-typo3-fluid="true"
>
	<f:variable name="itemsLength" value="{field.modifiedSettings -> f:count()}" />

	<f:variable name="title" value="" />
	<f:if condition="{itemsLength} > 1 AND {field.css} != 'nolabel'">
		<f:variable name="title"><vh:string.escapeLabels>{field.title}</vh:string.escapeLabels></f:variable>
	</f:if>

	<div class="group/checkbox @container relative {field.css} {settings.styles.framework.fieldAndLabelWrappingClasses}">
		<f:if condition="{title}">
			<dbic:form.label
				for="{field.marker}"
				mandatory="{f:if(condition: '{itemsLength} > 1  AND {field.mandatory}', then: 'true', else: 'false')} "
				class="mb-1"
			>{title}</dbic:form.label>
		</f:if>
		<div class="flex flex-col gap-3.5 pb-7 @xs:pb-5">
			<f:for each="{field.modifiedSettings}" as="setting" iteration="index">
				<dbic:form.checkbox
					value="{setting.value}"
					id="{field.marker}_{index.cycle}"
					property="{field.marker}"
					multiple="true"
					checked="{vh:misc.prefillMultiField(field:field, mail:mail, cycle:index.cycle)}"
					required="{f:if(condition: '{field.mandatory} AND !{title}', then: 'true', else: 'false')}"
					additionalAttributes="{vh:validation.validationDataAttribute(field:field, iteration:index)}"
					checkboxClasses="{settings.styles.framework.fieldClasses} {vh:validation.errorClass(field:field, class:'border-red-500 text-red-500')}"
					class="{settings.styles.framework.fieldAndLabelWrappingClasses} {settings.styles.framework.checkClasses} {vh:validation.errorClass(field:field, class:'powermail_field_error')}"
				>
					<p>
						<f:format.htmlentitiesDecode>
							<vh:string.escapeLabels>{setting.label}</vh:string.escapeLabels>
						</f:format.htmlentitiesDecode>
					</p>
				</dbic:form.checkbox>
			</f:for>
		</div>
		<f:if condition="{settings.validation.client}">
			<div class="absolute bottom-0 left-11 w-full empty:hidden powermail_field_error_container_{field.marker}"></div>
		</f:if>
	</div>
</html>
