<html
	xmlns:vh="http://typo3.org/ns/In2code/Powermail/ViewHelpers"
	xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
    xmlns:v="http://typo3.org/ns/FluidTYPO3/Vhs/ViewHelpers"
  	xmlns:dbic="http://typo3.org/ns/Mogic/Dbi/Components"
  	data-namespace-typo3-fluid="true"
>
    <f:variable name="showPageTitle" value="false" />
    <f:variable name="onlyForScreanReaders" value="sr-only" />
    <f:if condition="{form.css} == 'showlegend'">
        <f:variable name="showPageTitle" value="true" />
        <f:variable name="fieldsetClasses" value="md:grid-cols-3" />
        <f:variable name="onlyForScreanReaders" value="" />
    </f:if>

    <f:switch expression="{page.css}">
        <f:case value="twocolumns">
            <f:variable name="fieldsGridClasses" value="md:grid-cols-2" />
        </f:case>
        <f:case value="threecolumns">
            <f:variable name="fieldsGridClasses" value="md:grid-cols-3" />
        </f:case>
        <f:defaultCase>
            <f:variable name="fieldsGridClasses" value="grid-cols-1" />
        </f:defaultCase>
    </f:switch>


    <div class="group/fieldset grid mb-5 {fieldsetClasses}" role="group" aria-labelledby="legend-{page.uid}">
          <f:variable name="fieldsGridClasses" value="md:col-span-2 {fieldsGridClasses}" />
          <span class="relative mb-5 {onlyForScreanReaders}" id="legend-{page.uid}">{page.title}</span>
        <div class="grid items-start gap-x-5 grow mb-5 {fieldsGridClasses} prose-a:underline">
            <f:for each="{page.fields}" as="field" iteration="iteration">
                <f:switch expression="{field.css}">
                    <f:case value="twocolspan">
                        <v:variable.set name="field.css" value="md:col-span-2" />
                    </f:case>
                    <f:case value="threecolspan">
                        <v:variable.set name="field.css" value="md:col-span-3" />
                    </f:case>
                    <f:case value="faketwocolspan">
                        <v:variable.set name="field.css" value="md:col-span-2 md:w-1/2 md:pr-2.5" />
                    </f:case>
                    <f:case value="nolabel">
                        <v:variable.set name="field.css" value="nolabel" />
                    </f:case>
                    <f:defaultCase>
                        <v:variable.set name="field.css" value="col-span-1" />
                    </f:defaultCase>
                </f:switch>
                <v:variable.set name="field.css" value="group/field {field.css}" />

                <vh:misc.createRowTags columns="{settings.styles.numberOfColumns}" class="{settings.styles.framework.rowClasses}" iteration="{iteration}">
                    <f:render partial="Form/Field/{vh:String.Upper(string:field.type)}" arguments="{field:field}"/>
                </vh:misc.createRowTags>
            </f:for>
        </div>

        <f:if condition="{settings.main.moresteps}">
            <div class="group/buttons">
                <f:if condition="!{iterationPages.isLast}">
                    <dbic:button
                        icon="chevron-left"
                        additionalAttributes="{'data-powermail-morestep-show' : iterationPages.cycle}"
                    />
                </f:if>
                <f:if condition="!{iterationPages.isFirst}">
                    <dbic:button
                        theme="secondary"
                        icon="chevron-right"
                        additionalAttributes="{'data-powermail-morestep-show' : iterationPages.index - 1}"
                    />
                </f:if>
            </div>
        </f:if>
    </div>
</html>
