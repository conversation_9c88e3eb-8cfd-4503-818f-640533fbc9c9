<html
	xmlns:vh="http://typo3.org/ns/In2code/Powermail/ViewHelpers"
	xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
  	xmlns:dbic="http://typo3.org/ns/Mogic/Dbi/Components"
  	data-namespace-typo3-fluid="true"
>
	<f:variable name="title" value="" />
	<f:if condition="{field.css} != 'nolabel'">
		<f:variable name="title"><vh:string.escapeLabels>{field.title}</vh:string.escapeLabels></f:variable>
	</f:if>

	<div class="group/radio @container relative {field.css} {settings.styles.framework.fieldAndLabelWrappingClasses}">
		<f:if condition="{title}">
			<dbic:form.label
				for="{field.marker}"
				mandatory="{field.mandatory}"
				class="mb-1"
			>{title}</dbic:form.label>
		</f:if>
		<div class="flex flex-col gap-3.5 pb-7 @xs:pb-5">
			<f:for each="{field.modifiedSettings}" as="setting" iteration="index">
				<dbic:form.radio
					value="{setting.value}"
					id="{field.marker}_{index.cycle}"
					property="{field.marker}"
					checked="{vh:misc.prefillMultiField(field:field, mail:mail, cycle:index.cycle)}"
					additionalAttributes="{vh:validation.validationDataAttribute(field:field, iteration:index)}"
					radioClasses="{settings.styles.framework.fieldClasses} {vh:validation.errorClass(field:field, class:'border-red-500 text-red-500')}"
					class="{settings.styles.framework.fieldAndLabelWrappingClasses} {settings.styles.framework.checkClasses} {vh:validation.errorClass(field:field, class:'powermail_field_error')}"
				>
					<f:format.htmlentitiesDecode>
						<vh:string.escapeLabels>{setting.label}</vh:string.escapeLabels>
					</f:format.htmlentitiesDecode>
				</dbic:form.radio>
			</f:for>
		</div>
		<f:if condition="{settings.validation.client}">
			<div class="absolute bottom-0 left-0 w-full empty:hidden powermail_field_error_container_{field.marker}"></div>
		</f:if>
	</div>
</html>
