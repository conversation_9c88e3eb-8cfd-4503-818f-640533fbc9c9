<html
	xmlns:vh="http://typo3.org/ns/In2code/Powermail/ViewHelpers"
	xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
  	xmlns:dbic="http://typo3.org/ns/Mogic/Dbi/Components"
  	data-namespace-typo3-fluid="true"
>
	<f:variable name="label" value="" />
	<f:if condition="{field.css} != 'nolabel'">
		<f:variable name="label"><vh:string.escapeLabels>{field.title}</vh:string.escapeLabels></f:variable>
	</f:if>

	<dbic:form.toggle
		property="{field.marker}"
		id="{field.marker}"
		label="{label}"
		checked="{vh:misc.prefillField(field:field, mail:mail)}"
		additionalAttributes="{vh:validation.validationDataAttribute(field:field)}"
		class="{field.css} {settings.styles.framework.fieldAndLabelWrappingClasses}"
	/>
</html>