<html
	xmlns:vh="http://typo3.org/ns/In2code/Powermail/ViewHelpers"
	xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
  	xmlns:dbic="http://typo3.org/ns/Mogic/Dbi/Components"
  	data-namespace-typo3-fluid="true"
>
	<dbic:form.textarea
		property="{field.marker}"
		id="{field.marker}"
		value="{vh:misc.prefillField(field:field, mail:mail)}"
		placeholder="{field.placeholder}"
		additionalAttributes="{vh:validation.validationDataAttribute(field:field)}"
		textareaClasses="{settings.styles.framework.fieldClasses} {vh:validation.errorClass(field:field, class:'border-red-500 text-red-500')}"
		class="mb-2 {field.css} {settings.styles.framework.fieldAndLabelWrappingClasses}"
		>
		<dbic:form.label
			for="{field.marker}"
			title="{field.description}"
			class="{settings.styles.framework.labelClasses}"
			mandatory="{field.mandatory}"
		>
			<vh:string.escapeLabels>{field.title}</vh:string.escapeLabels>
		</dbic:form.label>
	</dbic:form.textarea>
</html>
