<?xml version="1.0" encoding="utf-8"?>
<html xmlns="http://www.w3.org/1999/xhtml" lang="en"
     xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers"
>
  <f:section name="p">
    <f:if condition="{settings.title}"><h1>{settings.title}</h1></f:if>
    <f:if condition="{settings.subtitle}"><h2>{settings.subtitle}</h2></f:if>
    <form method="GET" action="{f:uri.page(pageUid: settings.target_page)}#results" id="realtor-search" class="search-form">
      <input id="realtor-search-ajaxurl" type="hidden" name="realtorSearchAjaxUrl" value="{f:uri.page(pageUid: settings.target_page, pageType: 5)}" disabled=""/>
      <input id="realtor-search-input"
             type="search"
             dir="ltr"
             autocorrect="off"
             autocomplete="off"
             autocapitalize="off"
             maxlength="30" tabindex="1"
             name="q" value="{q}"
             placeholder="PLZ, Ort oder Name Immobilienberater*in"
             />
      <button id="realtor-search-submit" type="submit" disabled="" autocomplete="off" class="button button--dark">Suchen</button>
    </form>

    Ajax autocomplete URL:
    <tt><f:uri.page pageUid="{settings.target_page}" pageType="5" absolute="1"/>?q=#INPUT#</tt><br/>

    <script type="text/javascript">
      var autocompleteUrl = "<f:uri.page pageUid="{settings.target_page}" pageType="5"/>";
    </script>
  </f:section>
</html>
