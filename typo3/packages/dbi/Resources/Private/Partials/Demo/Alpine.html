<html
  xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
  xmlns:dbic="http://typo3.org/ns/Mogic/Dbi/Components"
  xmlns:x-on="http://example.org/dummy-ns"
  data-namespace-typo3-fluid="true"
  >
  <f:section name="Main">
    <div x-data="{ test: false }">
      <a x-on:click="test = !test" class="cursor-pointer inline-flex gap-8 select-none">
        Alpine Test
        <template x-if="test">
          <span class="text-green-400 inline-flex items-center gap-x-2">
            <dbic:icon name="check" class="w-[1em] h-[1em]" />
            Success
          </span>
        </template>
        <template x-if="!test">
          <span class="text-red-500 inline-flex items-center gap-x-2">
            <dbic:icon name="x-circle" class="w-[1em] h-[1em]" />
            Rejected
          </span>
        </template>
      </a>
    </div>
  </f:section>
</html>
