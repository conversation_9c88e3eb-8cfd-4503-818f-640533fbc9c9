<?xml version="1.0" encoding="utf-8"?>
<html
  data-namespace-typo3-fluid="true"
  xmlns="http://www.w3.org/1999/xhtml"
  xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers"
  xmlns:dbic="http://typo3.org/ns/Mogic/Dbi/Components"
>
  <f:section name="Main">
    <header class="page-width" role="banner">
        <div class="header-links">
            <h2>Configurable Header Links</h2>
            <f:if condition="{headerLinks}">
                <nav class="header-navigation">
                    <ul class="header-links-list">
                        <f:for each="{headerLinks}" as="headerLink">
                            <li class="header-link-item">
                                <f:link.typolink parameter="{headerLink.data.link}" class="header-link">
                                    {headerLink.data.title}
                                </f:link.typolink>
                            </li>
                        </f:for>
                    </ul>
                </nav>
                <f:else>
                    <p>No header links configured.</p>
                </f:else>
            </f:if>
        </div>

        <!-- Debug output (remove this in production) -->
        <f:comment>
            <f:debug>{headerLinks}</f:debug>
        </f:comment>
    </header>
  </f:section>
</html>
