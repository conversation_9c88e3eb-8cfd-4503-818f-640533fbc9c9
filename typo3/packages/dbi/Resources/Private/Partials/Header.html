<?xml version="1.0" encoding="utf-8"?>
<html
  data-namespace-typo3-fluid="true"
  xmlns="http://www.w3.org/1999/xhtml"
  xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers"
  xmlns:dbic="http://typo3.org/ns/Mogic/Dbi/Components"
>
  <f:section name="Main">
    <header class="page-width" role="banner">
        <div class="hidden lg:block page-padding z-20 relative bg-brand-950">
            <div class="container">
                <f:render partial="Navigation/Support" section="Header" arguments="{_all}" />
                <div class="absolute top-0 left-0 bottom-0 right-0 -z-10 bg-brand-950 dark"></div>
            </div>
        </div>
        <div class="page-padding z-20 relative">
            <div class="px-4 lg:container bleeding-content flex gap-3 lg:gap-10 pb-6 pt-6 lg:pb-0 text-white bg-brand-700">
                    <f:link.typolink parameter="1" class="flex text-white" title="Zur Deutsche Bank Immobilien Startseite" additionalAttributes="{'aria-label': 'Deutsche Bank Logo - zur Startseite'}">
                        <dbic:logo hide-wordmark="1" class="w-8 h-9 sm:w-10 sm:h-11 lg:w-[75px] lg:h-[75px]" />
                    </f:link.typolink>
                <div class="flex flex-col w-full" x-data="navMenu('mobile-nav')">
                    <div class="flex w-full lg:justify-between">
                            <f:link.typolink parameter="1" class=" text-white leading-none inline-flex font-bold">
                                <dbic:logo hide-logomark="1" class="text-[18px] sm:text-[22px] lg:text-[18px]"/>
                            </f:link.typolink>
                        <f:if condition="{marketPriceEstimationPid}">
                            <dbic:button
                                buttonSize="no-padding"
                                link="{f:uri.typolink(parameter: marketPriceEstimationPid)}"
                                theme="primary"
                                class="hidden md:flex w-24 xs:w-auto ml-auto lg:gap-2 items-center px-3 lg:px-4 lg:py-2.5"
                            >
                                <dbic:icon name="sold-immobilie" class="flex"/>
                                <span class="truncate">Immobilie einschätzen</span>
                            </dbic:button>
                            <f:if condition="{brokerSearchPid}">
                              <dbic:button
                                buttonSize="no-padding"
                                link="{f:uri.typolink(parameter: brokerSearchPid)}"
                                theme="mobile-header-button"
                                class="flex md:hidden ml-auto"
                              >
                                <span class="sr-only">Makler suchen</span>
                                <dbic:icon name="share" class="w-10 h-10"/>
                              </dbic:button>
                            </f:if>
                            <dbic:button
                                buttonSize="no-padding"
                                link="{f:uri.typolink(parameter: marketPriceEstimationPid)}"
                                theme="mobile-header-button"
                                class="flex md:hidden ml-3"
                            >
                              <span class="sr-only">Immobilie einschätzen</span>
                              <dbic:icon name="sold-immobilie" class="w-10 h-10"/>
                            </dbic:button>
                        </f:if>
                        <f:render partial="Navigation/Mobile" section="Main" arguments="{_all}" />
                    </div>
                    <f:render partial="Navigation/Main" section="Main" arguments="{_all}" />
                </div>
                <div class="absolute top-0 left-0 bottom-0 right-0 -z-10 bg-brand-700 dark"></div>
            </div>
        </div>
    </header>
  </f:section>
</html>
