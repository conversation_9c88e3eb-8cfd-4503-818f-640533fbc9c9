@import 'tailwindcss';

@config "../../../tailwind.config.js";

/*
  The default border color has changed to `currentcolor` in Tailwind CSS v4,
  so we've added these compatibility styles to make sure everything still
  looks the same as it did with Tailwind CSS v3.

  If we ever want to remove these styles, we need to add an explicit border
  color utility to any element that depends on these defaults.
*/
@layer base {
  *,
  ::after,
  ::before,
  ::backdrop,
  ::file-selector-button {
    border-color: var(--color-gray-200, currentcolor);
  }
}

@utility heading-1 {
  @apply text-4xl lg:text-6xl;
}

@utility heading-2 {
  @apply text-4xl lg:text-5xl;
}

@utility heading-3 {
  @apply text-2xl lg:text-3xl;
}

@utility heading-4 {
  @apply text-xl lg:text-2xl;
}

@utility heading-5 {
  @apply text-lg;
}

@utility heading-6 {
  @apply text-base;
}

@utility list-dash {
  & li {
    @apply flex gap-2.5 p-0 text-base mb-4;

    &:before {
      @apply size-5 mt-0.5 flex-none;
      content: '';
    }
  }

  & li:before {
    background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMjAiIHZpZXdCb3g9IjAgMCAyMCAyMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICAgIDxwYXRoIGQ9Ik0xOC40MzggMTAuN0gxLjU2M2MtLjMxMSAwLS41NjMtLjI5LS41NjMtLjY1IDAtLjM1OC4yNTItLjY1LjU2My0uNjVoMTYuODc1Yy4zMSAwIC41NjIuMjkyLjU2Mi42NSAwIC4zNi0uMjUxLjY1LS41NjMuNjV6IiBmaWxsPSIjMzMzIi8+Cjwvc3ZnPgo=');
  }
}

@utility list-timeline {
  margin-top: 12px;
  & li {
    @apply relative flex gap-2.5 p-0 text-base mb-4;
  }

  & li::before {
    @apply size-5 mt-1 flex-none;
    content: '';
    background-image: url("data:image/svg+xml,%3Csvg%20xmlns='http://www.w3.org/2000/svg'%20width='18'%20height='19'%20viewBox='0%200%2018%2019'%20fill='none'%3E%3Ccircle%20cx='9'%20cy='9.50049'%20r='9'%20fill='%230D43B3'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
  }

  & li::after {
    content: '';
    @apply absolute left-2 top-8 w-px bg-gray-300 z-0;
    height: calc(100% - 2rem);
  }
}

@utility list-check {
  & li {
    @apply flex gap-2.5 p-0 text-base mb-4;

    &:before {
      @apply size-5 mt-0.5 flex-none;
      content: '';
    }
  }

  & li:before {
    background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMjAiIHZpZXdCb3g9IjAgMCAyMCAyMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICAgIDxwYXRoIGQ9Ik02LjYwNiAxNS40NTVjLS4yOTIgMC0uNTY2LS4xMTQtLjc3Mi0uMzJsLTMuNzI4LTMuNzI3YS4zNjQuMzY0IDAgMCAxIC41MTUtLjUxNGwzLjcyOCAzLjcyOGEuMzc1LjM3NSAwIDAgMCAuNTE0IDBMMTcuMzggNC4xMDdhLjM2My4zNjMgMCAxIDEgLjUxNC41MTRMNy4zNzcgMTUuMTM2Yy0uMjA1LjIwNS0uNDc5LjMxOS0uNzcxLjMxOXoiIGZpbGw9IiMzMzMiLz4KPC9zdmc+Cg==');
  }
}

@utility list-counter {
  & li {
    @apply flex gap-2.5 p-0 text-base mb-4;

    &:before {
      @apply size-5 mt-0.5 flex-none;
      content: '';
    }
  }

  & li {
    counter-increment: list-number;

    &:before {
      @apply border rounded-full text-xs inline-flex justify-center leading-none items-center border-gray-800;
      content: counter(list-number);
    }
  }
}

@layer base {
  @font-face {
    font-family: 'DeutscheBankUI';
    font-style: normal;
    font-weight: 400;
    src: url('../Fonts/DeutscheBankUI-Regular.woff2') format('woff2'),
         url('../Fonts/DeutscheBankUI-Regular.woff') format('woff');
  }

  @font-face {
    font-family: 'DeutscheBankUI';
    font-style: normal;
    font-weight: 500;
    src: url('../Fonts/DeutscheBankUI-Medium.woff2') format('woff2'),
         url('../Fonts/DeutscheBankUI-Medium.woff') format('woff');
  }

  @font-face {
    font-family: 'DeutscheBankUI';
    font-style: normal;
    font-weight: 700;
    src: url('../Fonts/DeutscheBankUI-Bold.woff2') format('woff2'),
         url('../Fonts/DeutscheBankUI-Bold.woff') format('woff');
  }

  @font-face {
    font-family: 'DeutscheBankUI';
    font-style: italic;
    font-weight: 400;
    src: url('../Fonts/DeutscheBankUI-RegularItalic.woff2') format('woff2'),
         url('../Fonts/DeutscheBankUI-RegularItalic.woff') format('woff');
  }

    body {
        @apply w-full;
    }

    :root {
        @apply text-base text-brand-950 font-sans;
    }

    ol:not([class]) {
        @apply list-decimal;
        padding-left: 1em;
    }

    ul:not([class]) {
        @apply list-disc;
        padding-left: 1em;
    }

    .table table.odd-row\:bg-gray-100 th {
        @apply bg-transparent text-brand-500 font-normal;
    }

    td, th {
        @apply p-2.5 text-left;
    }

    a {
        text-decoration: underline;
    }
}

@layer components {
    [class^="heading-"] {
        @apply text-brand-950 dark:text-current font-medium;
    }
}
