@import 'tailwindcss';

@config "../../../tailwind.config.js";

/*
  The default border color has changed to `currentcolor` in Tailwind CSS v4,
  so we've added these compatibility styles to make sure everything still
  looks the same as it did with Tailwind CSS v3.

  If we ever want to remove these styles, we need to add an explicit border
  color utility to any element that depends on these defaults.
*/
@layer base {
  *,
  ::after,
  ::before,
  ::backdrop,
  ::file-selector-button {
    border-color: var(--color-gray-200, currentcolor);
  }



}

@theme {
  --breakpoint-xs:  435px;
  --breakpoint-2xl: 1280px;
  --breakpoint-3xl: 1440px;

  --container-main: 1920px;
  --container-container: 1136px;
  --container-container-indent: 852px;

  --spacing-content-padding: 24px;
  --spacing-page: 16px;
  --spacing-page-md: 40px;

  --text-2xs: 0.625rem;
  --text-2xs--line-height: 1.5;

  --text-xs--line-height: 1.5;

  --text-xl--line-height: 1.32;

  --text-2xl--line-height: 1.32;

  --text-3xl: 1.75rem;
  --text-3xl--line-height: 1.28;

  --text-4xl--line-height: 1.26;

  --text-5xl: 2.25rem;
  --text-5xl--line-height: 1.32;

  --text-6xl: 2.5rem;
  --text-6xl--line-height: 1.32;
}

@utility page-padding {
  @apply px-page lg:px-page-md;
}

@utility page-width {
  @apply max-w-main mx-auto
}

@utility heading-1 {
  @apply text-4xl lg:text-5xl;
}

@utility heading-2 {
  @apply text-3xl lg:text-4xl;
}

@utility heading-3 {
  @apply text-2xl lg:text-3xl;
}

@utility heading-4 {
  @apply text-xl lg:text-2xl;
}

@utility heading-5 {
  @apply text-lg lg:text-xl;
}

@utility heading-6 {
  @apply text-base lg:text-lg;
}

@utility list-dash {
  & li {
    @apply flex gap-2.5 p-0 text-base mb-4;

    &:before {
      @apply size-5 mt-0.5 flex-none;
      content: '';
    }
  }

  & li:before {
    background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMjAiIHZpZXdCb3g9IjAgMCAyMCAyMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICAgIDxwYXRoIGQ9Ik0xOC40MzggMTAuN0gxLjU2M2MtLjMxMSAwLS41NjMtLjI5LS41NjMtLjY1IDAtLjM1OC4yNTItLjY1LjU2My0uNjVoMTYuODc1Yy4zMSAwIC41NjIuMjkyLjU2Mi42NSAwIC4zNi0uMjUxLjY1LS41NjMuNjV6IiBmaWxsPSIjMzMzIi8+Cjwvc3ZnPgo=');
  }
}


@utility list-timeline {
  margin-top: 12px;
  & li {
    @apply relative flex gap-2.5 p-0 text-base mb-4;
  }

  & li::before {
    @apply size-5 mt-1 flex-none;
    content: '';
    background-image: url("data:image/svg+xml,%3Csvg%20xmlns='http://www.w3.org/2000/svg'%20width='18'%20height='19'%20viewBox='0%200%2018%2019'%20fill='none'%3E%3Ccircle%20cx='9'%20cy='9.50049'%20r='9'%20fill='%230D43B3'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
  }

  & li::after {
    content: '';
    @apply absolute left-2 top-8 w-px bg-gray-300 z-0;
    height: calc(100% - 2rem);
  }
}

@utility list-check {
  & li {
    @apply flex gap-2.5 p-0 text-base mb-4;

    &:before {
      @apply size-5 mt-0.5 flex-none;
      content: '';
    }
  }

  & li:before {
    background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMjAiIHZpZXdCb3g9IjAgMCAyMCAyMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICAgIDxwYXRoIGQ9Ik02LjYwNiAxNS40NTVjLS4yOTIgMC0uNTY2LS4xMTQtLjc3Mi0uMzJsLTMuNzI4LTMuNzI3YS4zNjQuMzY0IDAgMCAxIC41MTUtLjUxNGwzLjcyOCAzLjcyOGEuMzc1LjM3NSAwIDAgMCAuNTE0IDBMMTcuMzggNC4xMDdhLjM2My4zNjMgMCAxIDEgLjUxNC41MTRMNy4zNzcgMTUuMTM2Yy0uMjA1LjIwNS0uNDc5LjMxOS0uNzcxLjMxOXoiIGZpbGw9IiMzMzMiLz4KPC9zdmc+Cg==');
  }
}

@utility list-counter {
  & li {
    @apply flex gap-2.5 p-0 text-base mb-4;

    &:before {
      @apply size-5 mt-0.5 flex-none;
      content: '';
    }
  }

  & li {
    counter-increment: list-number;

    &:before {
      @apply border rounded-full text-xs inline-flex justify-center leading-none items-center border-gray-800;
      content: counter(list-number);
    }
  }
}

@utility button {
  @apply inline-flex items-center justify-center gap-2 rounded-xs text-sm font-medium bg-transparent border w-fit
  focus:ring-3 focus:ring-offset-2 focus:ring-brand-400 border-transparent;

  & svg {
    @apply w-6 h-6;
  }
}

@utility container {
  @apply max-w-container mx-auto px-6 xl:px-0;
}

@utility bleeding-content {
  @apply -mx-page lg:-mx-page-md
}

@utility input {
  @apply py-2 px-3 mb-7 @xs:mb-5 border border-gray-500 rounded-sm bg-white text-gray-800 leading-6
            placeholder:text-gray-500
            group-has-[.powermail-errors-list]/field:text-red-500 group-has-[.powermail-errors-list]/field:border-red-500
            disabled:bg-gray-100 disabled:border-gray-500 disabled:text-gray-500;
}

@utility powermail-errors-list {
  @apply absolute bottom-0 left-0 text-red-500 text-[.75em] leading-none;
}

@utility swiper {
  & .swiper-slide-active {
    @apply opacity-100;
  }
}

@utility swiper-slide-active {
  .swiper & {
    @apply opacity-100;
  }
}

@utility scrollbar-hidden {
  &::-webkit-scrollbar {
    display: none;
  }
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}

@layer base {
  @font-face {
    font-family: 'DeutscheBankUI';
    font-style: normal;
    font-weight: 400;
    src: url('../Fonts/DeutscheBankUI-Regular.woff2') format('woff2'),
         url('../Fonts/DeutscheBankUI-Regular.woff') format('woff');
  }

  @font-face {
    font-family: 'DeutscheBankUI';
    font-style: normal;
    font-weight: 500;
    src: url('../Fonts/DeutscheBankUI-Medium.woff2') format('woff2'),
         url('../Fonts/DeutscheBankUI-Medium.woff') format('woff');
  }

  @font-face {
    font-family: 'DeutscheBankUI';
    font-style: normal;
    font-weight: 700;
    src: url('../Fonts/DeutscheBankUI-Bold.woff2') format('woff2'),
         url('../Fonts/DeutscheBankUI-Bold.woff') format('woff');
  }

  @font-face {
    font-family: 'DeutscheBankUI';
    font-style: italic;
    font-weight: 400;
    src: url('../Fonts/DeutscheBankUI-RegularItalic.woff2') format('woff2'),
         url('../Fonts/DeutscheBankUI-RegularItalic.woff') format('woff');
  }

  html {
    @apply scroll-smooth;
  }

  body {
    @apply w-full text-brand-950;
  }

  table {
    @apply odd-row:bg-gray-100 w-full table-fixed;
  }

  thead {
    @apply text-brand-500;
  }

  td, th {
    @apply p-2.5 text-left align-top;
  }

  details summary::-webkit-details-marker {
    display:none;
  }

  .mainNavigation > li > ul {
    opacity: 0%;
    visibility: hidden;
  }

  .mainNavigationLink:hover + ul {
    opacity: 100%;
    visibility: visible;
  }
}

@layer components {
  [class^="heading-"] {
    @apply text-brand-950 dark:text-current font-medium;
  }

  .heading-6 {
    @apply font-normal
  }

  [class^="heading-"] ~ span {
    @apply text-brand-950 dark:text-current
  }

    input[type="search"]::-webkit-search-decoration,
    input[type="search"]::-webkit-search-cancel-button,
    input[type="search"]::-webkit-search-results-button,
    input[type="search"]::-webkit-search-results-decoration {
      display: none;
    }
}

[x-cloak] { display: none !important; }
