export default {
  create: (name, value, days)  => {
    const date = new Date();
    date.setTime(date.getTime() + (days*24*60*60*1000));
    const expires = `expires=${date.toUTCString()}`;
    document.cookie = `${name}=${value};${expires};path=/;SameSite=Lax`;
  },

  delete: (name) => {
    const date = new Date(0); // start of epoch
    const expires = `expires=${date.toUTCString()}`;
    const value = "";
    document.cookie = `${name}=${value};${expires};path=/;SameSite=Lax`;
  },

  get: (name) => {
    const relevantCookies = document.cookie.split(';')
      .filter(c => c.trim().startsWith(name + '='))

    if (relevantCookies.length === 0) {
      return null;
    }

    const [_name, value] = relevantCookies
      .reverse()[0]
        .trim().split("=")

    return value
  }
}