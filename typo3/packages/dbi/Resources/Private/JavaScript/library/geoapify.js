export default class FioGeoApiHelper {
    static tileUrl = 'https://geoapify.fio.de/v1/tile/osm-liberty/{z}/{x}/{y}.png?apiKey=47a5703edcab496c91814717f03a3505'

    constructor() {
        this.url = new URL('https://geoapify.fio.de/v2/places')
        this.url.searchParams.append("apiKey", "47a5703edcab496c91814717f03a3505")
    }

    async fetch({categories, lat, lng, radius = 5000, limit = 30}) {
        this.url.searchParams.append("categories", categories)
        this.url.searchParams.append("filter", `circle:${lng},${lat},${radius}`)
        this.url.searchParams.append("limit", limit)
        this.url.searchParams.append("bias", `proximity:${lng},${lat}`)

        const response = await fetch(this.url.href);
        return await response.json();
    }
}
