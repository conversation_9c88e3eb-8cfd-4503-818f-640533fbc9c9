import {
  popup as <PERSON><PERSON><PERSON>,
  tileLayer as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  marker as <PERSON><PERSON><PERSON><PERSON>,
  map as <PERSON><PERSON><PERSON>,
  divIcon as LDivIcon,
  geoJSON as LGeoJSON
} from "leaflet";

export default class LeafletMap {
  static createMap(mapElement) {
    return LMap(mapElement);
  }

  static createTileLayer(tileUrl, options) {
    return LTileLayer(
      tileUrl, options
    )
  }

  static createGeoJsonLayer(data) {
      return LGeoJSON(data.geoJson, {
          pointToLayer: function (feature, coords) {
              return LeafletMap.createMarker(
                  coords,
                  {
                      refIcon: data.icon,
                      clickable: true,
                      popup: `<span>${feature.properties.name}</span>`,
                      iconSize: data.iconSize ?? undefined
                  }
              )
          }
      })
  }

  static createMarker(coords, { refIcon = null, clickable = false, popup = null, iconSize = 32 }) {
      if (!refIcon) {
          return LMarker(
              coords
          )
      }

      const marker = LMarker(
          coords,
          {
              clickable,
              icon: LeafletMap.createMarkerIcon(refIcon, {
                size: iconSize
              })
          }
      )

      if (!popup) {
          return marker
      }

      const markerPopUp = LPopup()
      markerPopUp.setContent(popup)
      marker.bindPopup(popup);
      return marker
  }


  static createMarkerIcon(refIcon, {
    size = 32,
    cssClass = ""
  } = {}) {
      return LDivIcon({
          html: refIcon.cloneNode(true),
          className: `bg-transparent border-0 text-brand-500 ${cssClass}`,
          iconSize: [size, size]
      });
  }
}
