export function toggleScrollState(state, position = 'fixed') {
  const body = document.body
  const scrollY = window.scrollY

  if (!!state) {
      Object.assign(body.style, {
          position: position,
          overflow: "hidden",
          top: `-${scrollY}px`,
      });
  } else {
      Object.assign(body.style, {
          position: null,
          overflow: null,
          top: `${scrollY}px`,
      });
  }
}

export function trapFocus(event, focusableElements) {
  if (event.key !== 'Tab') return;

  if (focusableElements.length == 0) return

  const first = focusableElements[0];
  const last = focusableElements[focusableElements.length - 1];

  if (event.shiftKey && document.activeElement === first) {
    event.preventDefault();
    last.focus();
  } else if (!event.shiftKey && document.activeElement === last) {
    event.preventDefault();
    first.focus();
  }
}
