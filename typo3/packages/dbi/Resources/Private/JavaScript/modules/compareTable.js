export default () => ({
    isScrollable: false,
    pageCount: 1,
    currentPage: 1,
    columnGap: 16,
    calculatedColumnWidth: 250,
    columnsPerPage: 1,
    rowHeaderGap: null,

    init() {
        this.$nextTick(() => {
            this.setupTable();
            this.updatePagination();
            this.handleResize()
        });

        this.$watch('currentPage', () => {
            this.scrollToPage();
        });

        let resizeTimeout;
        window.addEventListener('resize', () => {
            clearTimeout(resizeTimeout);
            resizeTimeout = setTimeout(() => {
                this.handleResize();
            }, 100);
        });
    },

    setupTable() {
        const { isMobile } = this.getBreakpointFlags();

        // Fake column space between row header and row cells
        const spaceBetweenColumns = this.$refs.spaceBetweenColumns;
        if (!isMobile) {
            spaceBetweenColumns.style.width = `${this.$refs.firstColumnOverlapTable.clientWidth + this.columnGap + 1}px`;
            this.rowHeaderGap = spaceBetweenColumns.clientWidth
        }

        this.updateColumnWidths();
        this.updateRowHeights();
    },

    getBreakpointFlags() {
        return {
            isMobile: window.matchMedia('(max-width: 767px)').matches,
            isTablet: window.matchMedia('(min-width: 768px) and (max-width: 1024px)').matches,
            isDesktop: window.matchMedia('(min-width: 1025px)').matches,
        };
    },

    updateColumnWidths() {
        const { isMobile } = this.getBreakpointFlags();

        const scrollWrapper = this.$refs.list;
        const rowWidths = scrollWrapper.querySelectorAll('td');
        const rowsHeaders = scrollWrapper.querySelectorAll('tbody th')

        const containerWidth = scrollWrapper.clientWidth;

        // On mobile, first column is hidden, so don't subtract its width
        const firstColumnWidth = isMobile ? 0 : this.$refs.firstColumnOverlapTable.clientWidth;
        rowsHeaders.forEach(header=>{
            header.style.width = `${firstColumnWidth}px`
        })

        const columnGap = isMobile ? 0 : this.columnGap;
        const availableWidth = containerWidth - firstColumnWidth - columnGap;

        // Calculate how many columns can fit
        const minColumnWidth = 250;
        const maxColumnsVisible = Math.floor(availableWidth / minColumnWidth);

        // Determine columns per page and calculate column width
        let columnsPerPage;
        let columnWidth;
        if (isMobile) {
            columnsPerPage = 1;
            columnWidth = availableWidth - 1;
        } else if (window.innerWidth <= 1024) {
            columnsPerPage = Math.min(2, maxColumnsVisible);
            columnWidth = Math.max(minColumnWidth, availableWidth / columnsPerPage);
        } else {
            columnsPerPage = Math.min(3, maxColumnsVisible);
            columnWidth = Math.max(minColumnWidth, availableWidth / columnsPerPage);
        }

        rowWidths.forEach(row => {
            row.style.minWidth = `${columnWidth}px`;
            row.style.width = `${columnWidth}px`;
        });

        if (isMobile) {
            const table = scrollWrapper.querySelector('table');
            if (table) {
                table.style.width = `${columnWidth}px`;
            }
        }

        this.calculatedColumnWidth = columnWidth;
        this.columnsPerPage = columnsPerPage;
    },

    updateRowHeights() {
        const rowHeights = this.$refs.list.querySelectorAll('tr');
        const overlapTable = this.$refs.firstColumnOverlapTable;
        const rows = overlapTable.querySelectorAll('tr th');

        rows.forEach((row, index) => {
            if (rowHeights[index]) {
                row.style.height = `${rowHeights[index].clientHeight}px`;
                // Add border to the table on top of the first header row
                if (index !== 0) {
                    row.style.border = `1px solid #e1e1e1`;
                }
            }
        });
    },

    updatePagination() {
        const { isMobile } = this.getBreakpointFlags();

        const scrollWrapper = this.$refs.list;

        // On mobile, first column is hidden, so don't subtract its width
        const firstColumnWidth = isMobile ? 0 : this.$refs.firstColumnOverlapTable.clientWidth;
        const columnGap = isMobile ? 0 : this.columnGap;
        const availableWidth = scrollWrapper.clientWidth - firstColumnWidth - columnGap;

        this.pageCount = Math.round(scrollWrapper.scrollWidth / availableWidth);
        this.isScrollable = this.pageCount > 1;

        if (this.currentPage > this.pageCount) {
            this.currentPage = 1;
        }
    },

    scrollToPage() {
        const { isMobile } = this.getBreakpointFlags();
        const scrollWrapper = this.$refs.list;

        const spaceBetweenColumns = this.$refs.spaceBetweenColumns;
        if(this.currentPage === this.pageCount) {
            if (!isMobile) {
                spaceBetweenColumns.style.width = `${this.rowHeaderGap + 1}px`;
            }
        } else {
            spaceBetweenColumns.style.width = `${this.rowHeaderGap}px`;
        }

        let scrollLeft = 0;
        if (this.currentPage > 1) {
            const columnsToScroll = (this.currentPage - 1) * this.columnsPerPage;
            scrollLeft = columnsToScroll * this.calculatedColumnWidth;
        }

        scrollWrapper.scrollTo({
            left: scrollLeft,
            behavior: 'smooth',
        });
    },

    handleResize() {
        this.updateColumnWidths();
        this.updateRowHeights();
        this.updatePagination();
    },

    get hasPrev() {
        return this.currentPage > 1;
    },

    get hasNext() {
        return this.currentPage < this.pageCount;
    },
});
