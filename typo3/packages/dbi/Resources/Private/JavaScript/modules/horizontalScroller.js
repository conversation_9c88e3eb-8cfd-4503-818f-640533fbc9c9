export default (pageCountForce = false) => ({
    isScrollable: false,
    pageCount: 1,
    currentPage: 1,
    scrollerWidth: 0,

    init() {
        this.$nextTick(
            () => {
                this.isScrollable = this.$refs['scroller'].clientWidth < this.$refs['scroller'].scrollWidth;
                this.scrollerWidth = (!this.isScrollable)
                    ? this.$refs['scroller'].clientWidth
                    : this.$refs['scroller'].clientWidth - 64;
                this.pageCount = !!pageCountForce
                    ? this.$refs['scroller'].childElementCount
                    : Math.ceil(this.$refs['scroller'].scrollWidth / this.scrollerWidth);
            }
        );

        this.$watch('currentPage', () => {
            this.$refs['scroller'].scrollTo({
                left: (this.currentPage - 1) * this.$refs['scroller'].offsetWidth,
                behavior: 'smooth',
            });
        })

    },
    get hasPrev() {
        return this.currentPage > 1;
    },
    get hasNext() {
        return this.currentPage < this.pageCount;
    },
    isOverflowed(el) {
        if(!this.isScrollable) {
            return false;
        }
        return this.currentPage * this.scrollerWidth < el.offsetLeft + el.clientWidth
    }
})
