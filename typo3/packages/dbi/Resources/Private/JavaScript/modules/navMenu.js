import { toggleScrollState, trapFocus } from "../library/functions"

export default (id) => ({
    open: false,
    lastFocusedElement: null,
    focusableElements: [],
    id: id,
    tappedOnce: false,
    linkEl: null,
    currentPointerType: 'mouse',

    init() {
        this.$watch("open", (open) => toggleScrollState(open));

        document.addEventListener('pointerdown', (e) => {
            this.currentPointerType = e.pointerType;
        });
    },

    focusTrapBinding: {
        ['x-bind:id']() {
            return this.id;
        },
        ['x-on:keydown'](event) {
            this.handleTabKeyDown(event);
        }
    },

    toggle() {
        this.open = !this.open
        if (this.open) {
            this.openMenu()
        } else {
            this.closeMenu()
        }
    },

    openMenu() {
        this.lastFocusedElement = document.activeElement;

        this.$nextTick(() => {
            const container = document.getElementById(this.id);
            if (!container) return;

            this.focusableElements = [
                ...container.querySelectorAll('a[href], button, input, textarea, select, details, [tabindex]:not([tabindex="-1"])')
            ];

            if (this.focusableElements.length > 0) {
                this.focusableElements[0].focus();
            }
        });
    },

    closeMenu() {
        if (this.lastFocusedElement) {
            this.$nextTick(() => {
                this.lastFocusedElement.focus();
            })
        }
    },

    handleTabKeyDown(event) {
        if (!this.open) return;

        if (event.key === 'Escape') {
            this.closeMenu();
            return;
        }

        trapFocus(event, this.focusableElements);
    },

    isUsingTouch() {
        return this.currentPointerType === 'touch' ||
               /iPad|iPhone|iPod|Android/i.test(navigator.userAgent) ||
               (navigator.maxTouchPoints && navigator.maxTouchPoints > 0);
    },

    isIOS() {
        return (
            /iPad|iPhone|iPod|Macintosh/.test(navigator.userAgent)
        );
    },

    onClick(event) {

        if (!this.isUsingTouch()) return;
        if (this.isIOS()) return;

        const isSameLinkTapped = this.linkEl === event.currentTarget;

        if (!this.tappedOnce || !isSameLinkTapped) {
            event.preventDefault();
            this.tappedOnce = true;
            this.linkEl = event.currentTarget;
        } else {
            this.tappedOnce = false;
        }
    },

    handleClickOutside() {
        this.tappedOnce = false;
        this.linkEl = null;
    }
})
