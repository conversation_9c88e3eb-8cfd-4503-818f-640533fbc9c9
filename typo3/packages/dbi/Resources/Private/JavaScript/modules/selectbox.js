export default ({
    options,
    value
}) => ({
    options: options,
    currentIndex: -1,
    open: false,

    get currentOption() {
        if (this.currentIndex < 0) {
            return null;
        }
        return this.options[this.currentIndex];
    },
    get currentOptionValue() {
        return this.currentOption
            ? this.currentOption.value
            : "";
    },
    get currentOptionLabel() {
        return this.currentOption
            ? this.currentOption.label
            : "";
    },
    init() {
        this.currentIndex = this.getIndexByValue(value)
    },
    close() {
        if (!this.open) {
            return
        }
        this.open = false
        this.$refs.input.focus()
    },
    toggle() {
        this.open = !this.open
    },
    handleKeyDown() {
        if(!this.open) {
            this.open = true
        }
        if(this.currentIndex >= this.options.length - 1) {
            return
        }
        this.currentIndex++;
    },
    handleKeyUp() {
        if(this.currentIndex === 0) {
            return
        }
        this.currentIndex--;
    },
    getIndexByValue(value) {
        return this.options.findIndex(
            (option) => option.value == value
        );
    }
})
