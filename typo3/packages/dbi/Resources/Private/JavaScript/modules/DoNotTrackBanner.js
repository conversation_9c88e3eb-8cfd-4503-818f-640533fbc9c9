import CookieHelper from "../library/cookie";

export default (options = {
  duration: 10000, // 10 Sekunden
  cookieName: "consentCookie",
  cookieDuration: 180,
}) => ({
  enabled: false,
  duration: options.duration,
  cookieName: options.cookieName,

  init() {
    this.$nextTick(
      () => {
        if (!!this.visible) {
          this.activate()
        }
      }
    );

    this.$watch(
      "enabled",
      (enabled) => {
        window.setTimeout(
          () => this.$refs.progressbar.classList.toggle(
            'translate-x-0', enabled
          ),
          100
        )
      }
    )
  },

  get visible() {
    return CookieHelper.get(this.cookieName) === "dnt"
  },

  activate() {
    this.enabled = true
    window.setTimeout(
      () => {
        this.enabled = false
        CookieHelper.create(this.cookieName, "no", this.cookieDuration)
      },
      this.duration
    );
  }
})
