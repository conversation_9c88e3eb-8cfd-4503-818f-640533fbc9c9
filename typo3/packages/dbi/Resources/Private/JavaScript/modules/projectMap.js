import FioGeoApiHelper from "../library/geoapify";
import LeafletMap from "../library/leaflet";

export default ({
    options,
    poiData
}) => ({
    layers: [],
    activeLayers: [],
    tileLayerOptions: {},
    gridLayerOptions: {},
    layerOptions: {
        attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
    },
    init() {
        const { lat, lng, zoom } = options
        this._map = LeafletMap.createMap(this.$refs['mapElement']);
        this._map.setView([
            lat, lng
        ], zoom);

        this._map.scrollWheelZoom.disable();

        const tileLayer = LeafletMap.createTileLayer(
            FioGeoApiHelper.tileUrl,
            {
               ...this.tileLayerOptions,
               ...this.gridLayerOptions,
               ...this.layerOptions
            }
        )
        const mainPinMarker = LeafletMap.createMarker(
            [lat, lng],
            { refIcon: this.$refs.mapCenterIcon, iconSize: 48}
        )

        const layerKeys = Object.keys(poiData.config);

        mainPinMarker.setZIndexOffset(poiData.fetchLimit * layerKeys.length + 1)

        mainPinMarker.addTo(this._map)
        tileLayer.addTo(this._map)

        layerKeys.forEach(
            async (layerKey) => {
                const layerData = poiData.config[layerKey]
                const geoApiHelper = new FioGeoApiHelper()
                const geoJson = await geoApiHelper.fetch({
                    categories: layerData.categories,
                    lat,
                    lng,
                    radius: poiData.radius ?? undefined,
                    limit: poiData.fetchLimit ?? undefined
                })
                layerData.geoJson = geoJson
                this.addLayer(layerData)
            }
        );
    },

    addLayer(data) {
        data.icon = this.$refs[data.layer + 'MarkerIcon']
        this.layers[data.layer] = LeafletMap.createGeoJsonLayer(
            data
        )
    },

    toggleLayer(layerName) {
        const currentLayerName = Object.keys(this.layers).filter(
            (layerName) => this.isLayerActive(layerName)
        )[0]

        if (currentLayerName) {
            this._map.removeLayer(this.layers[currentLayerName])
        }

        if (layerName !== currentLayerName) {
            this.layers[layerName].addTo(this._map);
        }
    },

    isLayerActive(layerName) {
        const layer = this.layers[layerName]
        if (!layer) {
            return false
        }

        return this._map.hasLayer(layer)
    }
})
