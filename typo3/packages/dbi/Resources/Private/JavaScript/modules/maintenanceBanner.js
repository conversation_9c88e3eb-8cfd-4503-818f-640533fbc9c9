export default () => ({
    show: false,
    cookie<PERSON>ey: "seenMaintenance",
    hash: null,

    init() {
      this.hash = this.$el.dataset['hash'];
      this.show = !this.hasMaintenanceCookie()
    },

    get cookieString() {
      return `${this.cookieKey}=${this.hash}`
    },

    hasMaintenanceCookie() {
      return document.cookie
        .split(";")
        .some(
          item => item.includes(this.cookieString)
        )
    },

    setMaintenanceCookie() {
      const expires = (new Date(Date.now()+ 30*86400*1000)).toUTCString();
      document.cookie = `${this.cookieString};expires=${expires};Path=/;SameSite=Lax;`
    }
})
