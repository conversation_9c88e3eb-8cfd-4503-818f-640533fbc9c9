
import Swiper from 'swiper';
import { toggleScrollState } from '../library/functions';

export default (options) => ({
  currentIndex: 0,
  realLength: options.length,
  realIndex: 0,
  withThumbs: !!options.thumbSlider,
  fullscreen: false,
  init() {
    const identifier = this.$id('gallery')
    this.identifier = identifier
    const mainSelector = `[data-gallery-main="${identifier}"]`

    this.$nextTick(
      () => {
        if (!!this.withThumbs) {
          this.thumbSlider = new Swiper(`[data-gallery-thumb="${identifier}"]`, {
            loop: true,
            slidesPerView: 4,
            freeMode: true,
            watchSlidesProgress: true,
            centeredSlides: true,
            spaceBetween: 10,
          });
        }
        this.mainSlider = new Swiper(mainSelector, {
          loop: true,
        })

        this.$watch("fullscreen", () => {
          toggleScrollState(this.fullscreen, null)
          this.mainSlider.update()
        })

        this.mainSlider.on("slideChange", (swiper) => {
          this.currentIndex = swiper.realIndex
          this.realIndex = swiper.realIndex % this.realLength

          if (this.withThumbs) {
            this.thumbSlider.slideToLoop(swiper.realIndex)
          }
        })
      }
    )
  },

  get pagination() {
    return `${this.realIndex + 1}/${this.realLength}`
  },

  next() {
    this.mainSlider.slideNext()
  },

  prev() {
    this.mainSlider.slidePrev()
  },

  gotoFromThumb(event) {
    const thumbIndex = parseInt(event.currentTarget.dataset.slideIndex)
    const copyIndex = parseInt(event.currentTarget.dataset.copyIndex)
    const copyThumbIndex = thumbIndex + (this.realLength * copyIndex)
    this.mainSlider.slideToLoop(copyThumbIndex)
  },

  toggleFullscreen(event) {
    const targetIsButton = event.target.nodeName === "BUTTON"
    const targetIsImage = event.target.nodeName === "IMG"

    if (targetIsButton) {
      return
    }

    if (this.fullscreen === true && !!targetIsImage) {
      return;
    }

    this.fullscreen = !this.fullscreen
  }
})