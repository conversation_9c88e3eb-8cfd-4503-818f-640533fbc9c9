export default (options = {
  dataKey: "data",
  query: ""
}) => ({
  inputValue: "",
  suggestions: [],
  active: false,
  suggestionKeys: [options.dataKey],
  cursorIndex: 0,
  searchUrl: null,
  minLengthOfSuggestions: 1,

  init() {
    this.searchUrl = this.$refs.searchUrl.value ?? false
    this.inputValue = options.query

    window.addEventListener('keydown', () => {
      this.interactionMethod = 'keyboard'
    })

    window.addEventListener('mousedown', () => {
      this.interactionMethod = 'mouse'
    })

    window.addEventListener('touchstart', () => {
      this.interactionMethod = 'touch'
    })
  },

  async fetch(query) {
    const response = await fetch(this.searchUrl + '?q=' + this.inputValue)
    const result = await response.json()

    if (typeof result.length == 'number') {
      this.suggestions = result.map(item => {
        item.type = options.dataKey
        return item
      })
      return
    }

    const suggestions = Object.keys(result).reduce((prev, current) => {
      return [...prev, ...result[current].map(item => {
        item.type = current
        return item
      })]
    }, []).map((item, index) => {
      item.index = index
      return item
    })

    this.suggestionKeys = [...new Set(suggestions.map(item => item.type))];
    this.suggestions = suggestions
    return
  },

  listIsEnabled() {
    if (!this.active) {
      return false;
    }

    if (this.suggestions.length < this.minLengthOfSuggestions) {
      return false;
    }

    return true
  },

  clearValue() {
    this.inputValue = ""
  },

  enterValue() {
    const cursorIndex = this.cursorIndex
    const suggestions = this.suggestions

    if (cursorIndex < 0 || suggestions.length === 0) {
      return this.submitValue(
        this.inputValue
      )
    }

    return this.submitValue(
      suggestions[cursorIndex].query
    )
  },

  submitValue(value) {
    this.inputValue = value
    this.$nextTick(
      () => this.$refs.form.dispatchEvent(new Event("submit"))
    )
  },

  submit() {
    this.$el.submit()
  },

  moveCursorDown() {
    this.cursorIndex++

    if ( this.cursorIndex >= this.suggestions.length ) {
      this.cursorIndex = 0;
    }
  },

  moveCursorUp() {
    this.cursorIndex--

    if ( this.cursorIndex < -1 ) {
      this.cursorIndex = this.suggestions.length - 1;
    }
  },

  handleFocus() {
    if (this.interactionMethod === 'keyboard') {
      this.active = true
    }
  },

  handleBlur() {
    if (this.interactionMethod === 'keyboard') {
      this.active = false
    }
  },
})
