export default () => ({
    itemsLength: 0,
    mainActiveIndex: 0,

    init() {
        this.itemsLength = this.$refs['carousel'].childElementCount;

        this.$watch("mainActiveIndex", () => {
            if (!this.$refs['carousel']) {
                return;
            }

            this.$refs['carousel'].scrollTo({
                left: this.$refs['carousel'].offsetWidth * this.indicatorActiveIndex,
                behavior: 'smooth',
            });
        });
    },
    get indicatorActiveIndex(){
        return Math.floor(this.mainActiveIndex / this.itemsPerSlide);
    },
    get indicatorLength(){
        return [...this.$refs['indicators'].children]
            .filter(
                element => element.offsetParent !== null
            ).length
    },
    get itemsPerSlide() {
        if (this.indicatorLength > 1) {
            return [...this.$refs['carousel'].children]
                .filter(
                    element => element.offsetLeft < element.offsetParent.offsetWidth
                ).length
        }

        return this.itemsLength;
    },
    get hasNext() {
        return this.indicatorActiveIndex <  Math.ceil(this.itemsLength / this.itemsPerSlide) - 1;
    },
    get hasPrev() {
        return this.indicatorActiveIndex != 0;
    },
    scrollHandler(
        event
    ) {
        event.stopImmediatePropagation();
        this.mainActiveIndex = Math.ceil(this.$refs['carousel'].scrollLeft / this.$refs['carousel'].clientWidth) * this.itemsPerSlide;
    }
})
