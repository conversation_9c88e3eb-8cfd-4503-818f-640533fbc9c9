import <PERSON><PERSON><PERSON><PERSON><PERSON> from "../library/cookie";
import ETracker from "../library/etracker";
import { toggleScrollState } from "../library/functions";

export default (
  options = {
    etrackerEnabled: true,
    cookieName: "consentCookie",
    cookieDuration: 180,
  },
) => ({
  scrollBlocked: false,
  cookiesAllowed: null,
  etrackerEnabled: options.etrackerEnabled,
  duration: options.duration,
  cookieName: options.cookieName,

  init() {
    this.$nextTick(() => {
      if (!this.visible) return;

      this.scrollBlocked = this.visible;
      this.$refs.cookieDialog.showModal();
    });

    this.$watch("scrollBlocked", (scrollBlocked) =>
      toggleScrollState(scrollBlocked),
    );

    this.$watch("cookiesAllowed", (cookiesAllowed) => {
      if (this.etrackerEnabled) {
        cookiesAllowed ? ETracker.enable() : ETracker.disable();
      }

      CookieHelper.create(
        this.cookieName,
        cookiesAllowed ? "yes" : "no",
        this.cookieDuration,
      );
    });
  },

  get visible() {
    return !["dnt", "yes", "no"].includes(CookieHelper.get(this.cookieName));
  },

  dialogBinding: {
    ["x-on:keydown"](event) {
      if (event.key === "Escape") {
        event.preventDefault();
      }
    },
  },

  //For proper tab navigation that starts from the top of the page after closing the cookie banner
  resetFocus() {
    const main = document.querySelector('main, [role="main"], #main');
    if (main) {
      main.setAttribute("tabindex", "-1");
      main.focus();
      main.addEventListener(
        "blur",
        () => {
          main.removeAttribute("tabindex");
        },
        { once: true },
      );
    }
  },

  allowCookies() {
    this.cookiesAllowed = true;
    this.scrollBlocked = false;
    this.$refs.cookieDialog.close();

    this.resetFocus();
  },
  declineCookies() {
    this.cookiesAllowed = false;
    this.scrollBlocked = false;
    this.$refs.cookieDialog.close();

    this.resetFocus();
  },
});
