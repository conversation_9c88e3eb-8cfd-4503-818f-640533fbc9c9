import {getElementPropById} from "../library/domQueryHelper.js";

export default () => ({
    links: {},
    currentIndex: undefined,
    open: false,

    init() {
        const subMainMenuOffset = getElementPropById('subMainMenu', 'clientHeight', 0)
        const offset = this.$el.clientHeight + subMainMenuOffset

        this.links = [...document.querySelectorAll('a[data-container-anchor="1"]')]
            .map(
                (content, index) => {
                    return {
                        title: content.getAttribute('name'),
                        topOffset: content.offsetTop - offset,
                        index
                    }
                }
            );
    },

    get subTitle() {
        return this.currentIndex == undefined
            ? ''
            : this.links[this.currentIndex].title;
    },

    handleJumpTo(
        index
    ) {
        let maintenanceBannerOffset = getElementPropById('maintenanceBanner', 'clientHeight', 0)

        if (maintenanceBannerOffset !== 0) {
            maintenanceBannerOffset += 4 * 16 // margin of maintenance banne: 4rem (my-8)
        }

        scrollTo({
            top: this.links[index].topOffset + maintenanceBannerOffset,
            behavior: "smooth",
        });
    },
    handleScroll() {
        const currentLink = this.links.filter(
            (link)=>{
                return window.scrollY >= link.topOffset;
            })
            .pop();
        this.currentIndex = (currentLink) ? currentLink.index : undefined;
    }
})
