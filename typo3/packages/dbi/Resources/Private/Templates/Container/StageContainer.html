<html
  xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
  xmlns:dbic="http://typo3.org/ns/Mogic/Dbi/Components"
  data-namespace-typo3-fluid="true"
>
  <dbic:stage image="{stage_images.0}" divider="{data.tx_dbi_stage_divider}">
    <f:if condition="{children_100}">
      <f:for each="{children_100}" as="content">
        <f:cObject typoscriptObjectPath="lib.tx_mask.content">
          {content.uid}
        </f:cObject>
      </f:for>
    </f:if>

    <f:if condition="{data.tx_dbi_stage_placeholder_widget_show}">
      <div
        class="md:absolute xl:w-auto md:w-2/5 md:top-1/2 md:left-3/4 md:-translate-1/2 p-6 bg-white flex flex-col items-center justify-center gap-4 z-10"
      >
        <hgroup class="flex flex-col text-center gap-3">
          <h4 class="text-xl font-medium text-brand-950">
            {data.tx_dbi_stage_placeholder_widget_headline}
          </h4>
          <p class="text-sm text-brand-700">
            {data.tx_dbi_stage_placeholder_widget_subline}
          </p>
        </hgroup>
        <section class="grid grid-cols-2 gap-6 w-full max-w-xs">
          <f:link.typolink
            parameter="{marketPriceEstimationPid}"
            additionalParams="type=house"
            class="flex flex-col items-center gap-1 rounded-xs shadow-md bg-brand-700 text-white py-3 px-2"
          >
            <dbic:icon name="home" class="size-[50px]" />
            Haus
          </f:link.typolink>
          <f:link.typolink
            parameter="{marketPriceEstimationPid}"
            additionalParams="type=apartment"
            class="flex flex-col items-center gap-1 rounded-xs shadow-md bg-brand-700 text-white py-3 px-2"
          >
            <dbic:icon name="flat" class="size-[50px]" />
            Wohnung
          </f:link.typolink>
        </section>
        <f:link.typolink
          parameter="{marketPriceEstimationPid}"
          additionalParams="type=home"
          class="px-6 py-4.5 text-sm font-medium rounded-xs bg-brand-400 text-white"
        >
          Weiter
        </f:link.typolink>
      </div>
    </f:if>
  </dbic:stage>
</html>
