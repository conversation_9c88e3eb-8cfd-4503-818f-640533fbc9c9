<?xml version="1.0" encoding="utf-8"?>
<html xmlns="http://www.w3.org/1999/xhtml" lang="en"
     xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers"
     xmlns:v="http://typo3.org/ns/FluidTYPO3/Vhs/ViewHelpers"
     xmlns:wsb="http://typo3.org/ns/Fio/Websitebuilder/ViewHelpers"
>
  <f:layout name="EmptyP"/>

  <f:section name="p">
    <div class="cookieManagementEtracker">
      <f:if condition="{etrackerEnabled}">
        <f:then>
          <f:format.raw>{settings.etrackerTextEnabled}</f:format.raw>
          <f:form action="setEtracker" method="post">
            <f:form.button type="submit" name="enable" value="0" class="a">
              <f:format.raw>{settings.etrackerButtonDisable}</f:format.raw>
            </f:form.button>
          </f:form>
          <script type="text/javascript">
            window.addEventListener(
                'load',
                function() {
                    cookiebannerAllowEtracker();
                }
            );
          </script>
        </f:then>
        <f:else>
          <f:format.raw>{settings.etrackerTextDisabled}</f:format.raw>
          <f:form action="setEtracker" controller="CookieManagement" method="post">
            <f:form.button type="submit" name="enable" value="1" class="a">
              <f:format.raw>{settings.etrackerButtonEnable}</f:format.raw>
            </f:form.button>
          </f:form>
          <script type="text/javascript">
            window.addEventListener(
                'load',
                function() {
                    cookiebannerDeclineEtracker();
                }
            );
          </script>
        </f:else>
      </f:if>
    </div>
  </f:section>
</html>
