<?xml version="1.0" encoding="utf-8"?>
<html xmlns="http://www.w3.org/1999/xhtml" lang="en"
     xmlns:be="http://typo3.org/ns/TYPO3/CMS/Backend/ViewHelpers"
     xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers"
>
  <f:layout name="BackendDefault"/>

  <f:section name="Main">
    <h1>Neues Immobiliencenter anlegen</h1>
    <form method="POST" action="{be:moduleLink(route:'dbi_createcenter')}">
      <style type="text/css">
        .error {
        color: red;
        }
      </style>

      <h2>Center</h2>
      <div class="form-group">
        <div class="form-section">
          <div class="row">
            <div class="form-group col-sm-12">
              <label for="name2">Name (Ort/Region)</label> *
              <f:if condition="{errors.name2}">
                <div class="error">{errors.name2}</div>
              </f:if>
              <div class="form-control-wrap">
                <input class="form-control" type="text" id="name2"
                       name="data[name2]" value="{data.name2}"
                       required=""/>
              </div>
            </div>
          </div>
        </div>

        <div class="form-section">
          <div class="row">
            <div class="form-group col-sm-12">
              <label for="fio_id">FIO-ID</label>
              <f:if condition="{errors.fio_id}">
                <div class="error">{errors.fio_id}</div>
              </f:if>
              <div class="form-control-wrap">
                <input class="form-control" type="text" id="fio_id"
                       name="data[fio_id]" value="{data.fio_id}"/>
              </div>
            </div>
          </div>
        </div>

        <div class="form-section">
          <div class="row">
            <div class="form-group col-sm-12">
              <label for="slug">URL (Slug)</label> *
              <f:if condition="{errors.slug}">
                <div class="error">{errors.slug}</div>
              </f:if>
              <div class="form-control-wrap">
                <input class="form-control" type="text" id="slug"
                       name="data[slug]" value="{data.slug}"
                       required=""/>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="form-group">
        <input class="btn btn-default" type="submit" name="create" value="Center anlegen"/> (Dauert etwas)
      </div>
    </form>
  </f:section>
 </html>
