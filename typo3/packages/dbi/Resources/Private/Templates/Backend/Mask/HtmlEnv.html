<html xmlns="http://www.w3.org/1999/xhtml"
    xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
    xmlns:v="http://typo3.org/ns/FluidTYPO3/Vhs/ViewHelpers"
    data-namespace-typo3-fluid="true"
    >
    Umgebungen:
    <f:for each="{data.tx_mask_env}" as="envRow" iteration="iteration">
        <f:if condition="{envRow.tx_mask_environment} == ''"><f:then>alles</f:then><f:else>{envRow.tx_mask_environment}</f:else></f:if><f:if condition="{iteration.isLast}==0">,</f:if>
    </f:for>
    <hr/>

    <f:if condition="{data.tx_mask_env.0}">
        <f:format.nl2br><f:format.stripTags>{data.tx_mask_env.0.tx_mask_html}</f:format.stripTags></f:format.nl2br>
    </f:if>
</html>
