<html
  xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers"
  data-namespace-typo3-fluid="true"
  >
  <f:if condition="{data.assets.0}">
    <div class="preview-thumbnails-element">
      <div class="preview-thumbnails-element-image">
        <f:image
          image="{data.assets.0}"
        />
      </div>
    </div>
  </f:if>
  <f:if condition="{data.tx_mask_overline}">
    <span>Overline: <em>{data.tx_mask_overline}</em></span><br />
  </f:if>
  <f:transform.html>{data.bodytext}</f:transform.html>

  <f:for each="{data.tx_mask_buttons}" as="button">
    <pre>{button.tx_mask_link}</pre><br />
  </f:for>
 </html>
