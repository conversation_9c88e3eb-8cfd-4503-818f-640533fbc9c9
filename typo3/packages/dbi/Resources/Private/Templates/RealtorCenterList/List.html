<?xml version="1.0" encoding="utf-8"?>
<html xmlns="http://www.w3.org/1999/xhtml" lang="en"
     xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers"
     xmlns:dbi="http://typo3.org/ns/Mogic/Dbi/ViewHelpers"
     xmlns:dbic="http://typo3.org/ns/Mogic/Dbi/Components"
     data-namespace-typo3-fluid="true"
>
  <f:layout name="EmptyP"/>

  <f:section name="p">
    <div class="border-y border-gray-200 py-6 flex flex-col divide-y divide-gap-y-6 divide-gray-200">
      <f:for each="{centers}" as="center">
        <f:render section="center" arguments="{center: center}"/>
      </f:for>
    </div>
  </f:section>

  <f:section name="center">
    <dbic:center.card center="{center}" />
  </f:section>
</html>
