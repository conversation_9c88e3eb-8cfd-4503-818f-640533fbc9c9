<?xml version="1.0" encoding="utf-8"?>
<html xmlns="http://www.w3.org/1999/xhtml" lang="en"
      xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers"
      xmlns:dbic="http://typo3.org/ns/Mogic/Dbi/Components"
>
  <f:layout name="EmptyP"/>

  <f:section name="p">
    <dbic:carousel items="{projects}" type="project" />
  </f:section>


  <f:section name="project">
    <f:if condition="{project.media}">
      {v:resource.record.fal(table: 'pages', field: 'media', uid: project.uid)
        -> v:iterator.first()
        -> v:variable.set(name: 'image')}
      <f:if condition="{image}">
        <f:then>
          <f:image treatIdAsReference="1" src="{image.id}" crop="{image.crop}"
                   title="{image.title}" alt="{image.alternative}"/>
          <span>{image.title}</span>
        </f:then>
      </f:if>
    </f:if>

    <f:if condition="{project.price}">
      <p><PERSON><PERSON><PERSON>reise: {project.price}</p>
    </f:if>

    <h2>{project.title}</h2>

    <dl>
      <f:for each="{project.keyfacts}" key="key" as="value">
        <dt>{key}</dt>
        <dd>{value}</dd>
      </f:for>
    </dl>

    <f:link.page pageUid="{project.uid}">Projekt ansehen</f:link.page>
  </f:section>
</html>
