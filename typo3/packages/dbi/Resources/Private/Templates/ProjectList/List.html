<?xml version="1.0" encoding="utf-8"?>
<html xmlns="http://www.w3.org/1999/xhtml" lang="en"
      xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers"
      xmlns:v="http://typo3.org/ns/FluidTYPO3/Vhs/ViewHelpers"
      xmlns:dbic="http://typo3.org/ns/Mogic/Dbi/Components"
>
  <f:layout name="EmptyP"/>


  <f:section name="p">

    <f:if condition="{f:count(subject: projects)}">
      <f:then>
        <div class="projects-container grid gap-8 grid-cols-1 md:grid-cols-2">
          <f:for each="{projects}" as="project">
            <dbic:project project="{project}"/>
          </f:for>
        </div>

        <f:if condition="{showPager}">
          <dbic:pager pagination="{pagination}"/>
        </f:if>
      </f:then>
      <f:else>
        Keine Projekte gefunden.
      </f:else>
    </f:if>
  </f:section>


</html>
