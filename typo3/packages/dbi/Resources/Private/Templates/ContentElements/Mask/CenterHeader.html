<html
  xmlns="http://www.w3.org/1999/xhtml"
  lang="en"
  xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers"
  xmlns:dbi="http://typo3.org/ns/Mogic/Dbi/ViewHelpers"
  xmlns:v="http://typo3.org/ns/FluidTYPO3/Vhs/ViewHelpers"
  xmlns:dbic="http://typo3.org/ns/Mogic/Dbi/Components"
  data-namespace-typo3-fluid="true"
>
  {dbi:center() -> v:variable.set(name: 'center')}
  <f:variable
    name="image"
    value="EXT:dbi/Resources/Public/Images/generic-team.jpg"
  />
  <f:if condition="{center.stageImage}">
    <f:variable name="image" value="{center.stageImage}" />
  </f:if>

  <f:variable
    name="replacedTexts"
    value="{
    bodytext: data.bodytext,
    header: data.header
  }"
  />

  <f:comment>###key###</f:comment>
  <f:for each="{replacedTexts}" as="text" key="name" iteration="i">
    <f:variable
      name="keys"
      value="{v:variable.pregMatch(
      pattern: '/###([a-zA-Z0-9_]+)###/',
      subject: text,
      global: 1
    )}"
    />
    <f:for each="{keys}" as="key" iteration="iterator">
      {v:format.pregReplace( pattern: '/###{key.1}###/', replacement:
      "{center.{key.1}}", subject: "{text}", ) -> v:variable.set(name: 'text')}
    </f:for>
    <v:variable.set name="replacedTexts.{name}" value="{text}" />
  </f:for>

  <div class="relative lg:pb-6">
    <dbic:stage image="{image}" divider="1">
      <dbic:content.snippet
        orientation="horizontal"
        theme="base-dark"
        header="{
          headline: replacedTexts.header,
          layout: data.header_layout
        }"
        class="w-full max-w-lg"
      >
        {replacedTexts.bodytext -> f:format.html()}
      </dbic:content.snippet>
    </dbic:stage>
  </div>
</html>
