<html xmlns="http://www.w3.org/1999/xhtml"
    xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
    xmlns:dbic="http://typo3.org/ns/Mogic/Dbi/Components"
    xmlns:dbi="http://typo3.org/ns/Mogic/Dbi/ViewHelpers"
    data-namespace-typo3-fluid="true">

    <f:layout name="Default" />

    <f:section name="Main">
        <dbic:headline
            headline="Sitemap"
            layout="102"
            class="text-brand-950 mb-10"
        />

        <f:render section="ListItem" arguments="{items: pageTree}" />

        <dbic:headline
            headline="Immobiliencenter"
            layout="103"
            class="text-brand-400 underlined my-8"
        />

        <ul class="flex flex-col gap-2">
            <dbi:sitemap as="sitemapItems">
                <f:for each="{sitemapItems}" as="page">
                    <f:render section="Item" arguments="{link: page.loc}" />
                </f:for>
            </dbi:sitemap>
        </ul>
    </f:section>

    <f:section name="Item">
        <f:variable name="fullUrl" value="{f:uri.typolink(parameter: link, absolute: 1)}" />
        <li class="text-brand-400 break-all">
            <f:link.typolink parameter="{link}" title="{fullUrl}">
                <span class="mr-2.5">-</span> {fullUrl}
            </f:link.typolink>
        </li>
    </f:section>

    <f:section name="ListItem">
        <f:for each="{items}" as="page">
            <dbic:headline
            headline="{page.title}"
            layout="103"
            class="text-brand-400 underlined my-8"
            />
            <ul class="flex flex-col gap-2">
                <f:render section="Item" arguments="{link: page.link}" />
                <f:if condition="{page.children -> f:count()} > 0">
                    <f:render section="ListItemChildren" arguments="{items: page.children}" />
                </f:if>
            </ul>
        </f:for>
    </f:section>

    <f:section name="ListItemChildren">

        <f:for each="{items}" as="page">
            <f:render section="Item" arguments="{link: page.link}" />
            <f:if condition="{page.children -> f:count()} > 0">
                <f:render section="ListItemChildren" arguments="{items: page.children}" />
            </f:if>
        </f:for>
    </f:section>
</html>
