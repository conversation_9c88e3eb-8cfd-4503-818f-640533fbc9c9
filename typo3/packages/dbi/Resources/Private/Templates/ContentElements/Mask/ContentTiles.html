<html xmlns="http://www.w3.org/1999/xhtml"
  xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
  xmlns:dbic="http://typo3.org/ns/Mogic/Dbi/Components"
  data-namespace-typo3-fluid="true">

<f:layout name="Default" />

<f:section name="Main">
    <f:if condition="{data.tx_mask_items}">
        <dbic:content.tiles columns="{data.tx_mask_grid_config}">
            <f:for each="{data.tx_mask_items}" as="tile">
                <dbic:content.tile
                  orientation="{tile.tx_mask_orientation}"
                  icon="{tile.tx_mask_icon}"
                  theme="{tile.tx_mask_theme}"
                  link="{tile.tx_mask_link}"
                  text="{tile.header}"
                />
            </f:for>
        </dbic:content.tiles>
    </f:if>
</f:section>

</html>
