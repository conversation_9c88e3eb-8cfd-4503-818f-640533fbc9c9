<html xmlns="http://www.w3.org/1999/xhtml"
  xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
  xmlns:v="http://typo3.org/ns/FluidTYPO3/Vhs/ViewHelpers"
  xmlns:x-on="http://example.org/dummy-ns"
  xmlns:x-bind="http://example.org/dummy-ns"
  xmlns:x-cloak="http://example.org/dummy-ns"
  data-namespace-typo3-fluid="true"
  xmlns:dbi="http://typo3.org/ns/Mogic/Dbi/ViewHelpers"
>

  <f:layout name="Default" />

  <f:section name="Main">
    <f:if condition="{data.tx_mask_latitude}">
      <f:variable
        name="options"
        value="{
          lat: data.tx_mask_latitude,
          lng: data.tx_mask_longitude,
          zoom: 13
        }"
      />
      <f:variable name="poiDataConfig" value="{
        doctors: {
          title: 'Gesundheit',
          categories: 'healthcare',
          layer: 'medical',
          icon: 'EXT:dbi/Resources/Public/Icons/Theme/Poi/doctors.svg'
        },
        school: {
          title: 'Schulen',
          categories: 'education.school,education.university',
          layer: 'education',
          icon: 'EXT:dbi/Resources/Public/Icons/Theme/Poi/school.svg'
        },
        shop: {
          title: 'Einkaufen',
          categories: 'commercial',
          layer: 'commercial',
          icon: 'EXT:dbi/Resources/Public/Icons/Theme/Poi/shopping.svg'
        },
        transport: {
          title: 'Transport',
          categories: 'public_transport',
          layer: 'transport',
          icon: 'EXT:dbi/Resources/Public/Icons/Theme/Poi/transport.svg'
        },
        kindergarten: {
          title: 'Kindergarten',
          categories: 'childcare.kindergarten',
          layer: 'childcare',
          icon: 'EXT:dbi/Resources/Public/Icons/Theme/Poi/kindergarten.svg'
        }
      }" />

      <f:render section="map" arguments="{
        poiData: {
          config: poiDataConfig,
          fetchLimit: 30,
          radius: 5000
        },
        options: options,
        data: data
      }"/>

      <f:asset.css href="EXT:dbi/Resources/Public/Css/leaflet.css" identifier="leaflet-css" />
    </f:if>
  </f:section>

  <f:section name="map">
    <f:variable name="mapData" value="{
      options: options,
      poiData: poiData
    }" />

    <div
      class="relative w-full z-0"
      x-data="projectMap({mapData -> v:format.json.encode()})"
    >
      <div class="hidden">
        <f:variable name="mapCenterIcon"
          value="EXT:dbi/Resources/Public/Icons/Theme/Poi/center.svg" />
          <span x-ref="mapCenterIcon">
            {mapCenterIcon -> f:cObject(typoscriptObjectPath: 'lib.svgHandler')}
          </span>
      </div>
      <f:if condition="{poiData.config -> f:count()} > 0">
        <ul class="absolute top-0 flex flex-wrap gap-x-1.5 gap-y-3 items-center justify-around p-4 w-full text-xs bg-white/80 z-10" x-cloak="">
          <f:for each="{poiData.config}" as="poiDataConfig">
            <li class="flex items-center gap-1.5 text-brand-500 cursor-pointer select-none" x-on:click="toggleLayer('{poiDataConfig.layer}')" x-bind:class="!isLayerActive('{poiDataConfig.layer}') ? 'text-gray-400' : ''">
              <span x-ref="{poiDataConfig.layer}MarkerIcon" data-layer="{poiDataConfig.layer} " class="size-8">
                {poiDataConfig.icon -> f:cObject(typoscriptObjectPath: 'lib.svgHandler')}
              </span>

              {poiDataConfig.title}
            </li>
          </f:for>
        </ul>
      </f:if>

      <div class="absolute bottom-7 right-12 p-2.5 bg-white/80 text-xs text-gray-700 z-10">
        <v:tag name="b" hideIfEmpty="1">{data.tx_mask_label}</v:tag>
        <v:tag name="p" hideIfEmpty="1">{data.tx_mask_street}</v:tag>
        <p>{data.tx_mask_postalcode} {data.tx_mask_city}</p>
      </div>

      <div
        x-ref="mapElement"
        data-options='{options -> v:format.json.encode()}'
        class="aspect-square md:aspect-image-wide lg:aspect-image-stage relative z-0"
      ></div>
    </div>
  </f:section>
</html>
