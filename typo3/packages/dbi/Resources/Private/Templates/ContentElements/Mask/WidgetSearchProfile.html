<html xmlns="http://www.w3.org/1999/xhtml"
  xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
  xmlns:v="http://typo3.org/ns/FluidTYPO3/Vhs/ViewHelpers"
  data-namespace-typo3-fluid="true">

  <f:layout name="Default" />

  <f:section name="Main">
    <f:if condition="{v:context.get()} == 'Production'">
      <f:then>
        <v:variable.set name="jsUrl" value="https://property-search.maklaro.com/main.js"/>
        <v:variable.set name="widgetId" value="29952d39-efa2-4760-9df8-20704c9f225b"/>
      </f:then>
      <f:else>
        <v:variable.set name="jsUrl" value="https://staging-property-search.maklaro.com/main.js"/>
        <v:variable.set name="widgetId" value="211115b2-71b7-4cfe-98ce-d4d2c755c284"/>
      </f:else>
    </f:if>

    <f:asset.script src="{jsUrl}" priority="false" identifier="widget_search_profile" />
    <maklaro-property-search data-widget-id="{widgetId}"></maklaro-property-search>
  </f:section>
</html>
