<html xmlns="http://www.w3.org/1999/xhtml" lang="en"
  xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers"
  xmlns:dbi="http://typo3.org/ns/Mogic/Dbi/ViewHelpers"
  xmlns:v="http://typo3.org/ns/FluidTYPO3/Vhs/ViewHelpers"
  xmlns:dbic="http://typo3.org/ns/Mogic/Dbi/Components"
  data-namespace-typo3-fluid="true"
>
  {dbi:center() -> v:variable.set(name: 'center')}

  <f:variable name="replacedTexts" value="{
    bodytext : data.bodytext,
    header: data.header,
    subheader: data.subheader
  }" />

  <f:comment>###key###</f:comment>
  <f:for each="{replacedTexts}" as="text" key="name" iteration="i">
    <f:variable name="keys" value="{v:variable.pregMatch(
      pattern: '/###([a-zA-Z0-9_]+)###/',
      subject: text,
      global: 1
    )}" />
    <f:for each="{keys}" as="key" iteration="iterator">
      {v:format.pregReplace(
        pattern: '/###{key.1}###/',
        replacement: "{center.{key.1}}",
        subject: "{text}",
      ) -> v:variable.set(name: 'text')}
    </f:for>
    <v:variable.set name="replacedTexts.{name}" value="{text}" />
  </f:for>

  <f:variable name="address" value="{center.addressStreet}" />
  <f:if condition="{center.addressCity}">
    <f:variable name="address">{center.addressStreet}<br />{center.addressPostalcode} {center.addressCity}</f:variable>
  </f:if>
  <f:variable name="hasAddress" value="{address} || {center.url_route}" />

  <f:variable name="links" value="{ 0:{} }" />

  <f:if condition="{center.email}">
    <f:variable
      name="links"
      value="{v:iterator.merge(
        a: links,
        b: {
          email: {
            link: 'mailto:{center.email}',
            icon: 'mail',
            title: 'Nachricht schreiben',
            additionalAttributes: { aria-label: 'Nachricht schreiben'}
          }
        }
      )}" />
  </f:if>

  <f:if condition="{center.telephone}">
    <f:variable
      name="links"
      value="{v:iterator.merge(
        a: links,
        b: {
          telephone: {
            link: '{dbi:telUrl(number: center.telephone)}',
            icon: 'phone',
            title: '{center.telephone}',
            additionalAttributes: { aria-label: 'Anrufen'}
          }
        }
      )}"
    />
  </f:if>

  <f:if condition="{sellEstatePageUid}">
    <f:variable name="sellEstatePage" value="{v:page.info(pageUid:sellEstatePageUid)}" />
    <f:variable
      name="links"
      value="{v:iterator.merge(
        a: links,
        b: {
          sellEstate: {
            link: sellEstatePage.uid,
            icon: 'home',
            title: sellEstatePage.title,
            additionalAttributes: { aria-label: 'Immobilie verkaufen'}
          }
        }
      )}"
    />
  </f:if>

  <f:if condition="{searchRequestPageUid}">
    <f:variable name="searchRequestPage" value="{v:page.info(pageUid:searchRequestPageUid)}" />
    <f:variable
      name="links"
      value="{v:iterator.merge(
        a: links,
        b: {
          searchRequest: {
            link: searchRequestPage.uid,
            icon: 'search',
            title: searchRequestPage.title,
            additionalAttributes: { aria-label: 'Suchprofil anlegen'}
          }
        }
      )}"
    />
  </f:if>

  <f:variable name="childClasses" value="*:flex-none lg:*:w-full *:min-w-20 md:*:min-w-44" />
  <f:if condition="{links ->f:count()} > 2">
    <f:variable name="childClasses" value="*:flex-none lg:*:w-full *:grow lg:*:grow-0" />
  </f:if>

  <div class="flex justify-center w-full mt-8 md:mt-12 md:mb-8">
    <dbic:button.group
      links="{links}"
      hideLabelOnSmallScreens="1"
      class="container w-full flex lg:grid grid-cols-4 gap-3 sm:gap-6 items-center justify-center {childClasses}"
    />
  </div>

  <div class="container w-full py-8 lg:py-16 flex flex-col gap-6 lg:gap-12" data-container-nav-title="Über uns" data-container-anchor="1">
    <div class="grid lg:grid-cols-2 gap-6">
      {replacedTexts.subheader -> f:format.case(mode: 'upper') -> v:variable.set(name: 'overline')}
      <dbic:content.snippet
        orientation="horizontal"
        header="{
          headline: replacedTexts.header,
          layout: data.header_layout,
          subheader: overline
        }"
      >
        {replacedTexts.bodytext -> f:format.html()}
      </dbic:content.snippet>

      <f:variable name="awardImages" value="{center.allAwards}" />

      <f:if condition="{awardImages}">
        <div class="flex flex-wrap gap-y-5 gap-x-7 place-content-start items-start">
          <span class="uppercase w-full font-medium">AUSZEICHNUNGEN</span>
          <f:for each="{awardImages}" as="image" iteration="i">
            <dbic:image image="{image}" class="w-32" ratio="" />
          </f:for>
        </div>
      </f:if>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-10 py-8 px-6 border border-gray-300 col-span-2 max-md:divide-y xl:divide-x gap-8">
      <f:if condition="{data.tx_mask_links}">
        <div class="md:col-span-2 md:max-xl:border-b border-gray-200 md:max-xl:pb-8 xl:col-span-4">
          <dbic:headline
            headline="Services"
            layout="106"
            class="mb-4"
          />
          <ul class="flex flex-wrap gap-3">
            <f:for each="{data.tx_mask_links}" as="textlink" iteration="iterator">
              <li>
                <f:cObject typoscriptObjectPath="lib.tx_mask.content">
                  {textlink.uid}
                </f:cObject>
              </li>
            </f:for>
          </ul>
        </div>
      </f:if>

      <f:if condition="{center.contactOpeninghours}">
        <div class="max-md:pt-8 md:max-xl:border-r border-gray-200 md:max-xl:pr-8 xl:col-span-3 xl:pl-8">
          <dbic:headline
            headline="Öffnungszeiten"
            layout="106"
            class="mb-4"
          />
          <div class="text-gray-800">
            {center.contactOpeninghours -> f:format.html()}
          </div>
        </div>
      </f:if>

      <f:if condition="{hasAddress}">
        <div class="max-md:pt-8 xl:col-span-3 xl:pl-8">
          <dbic:headline
            headline="Adresse"
            layout="106"
            class="mb-4"
          />
          <f:if condition="{address}">
            <address class="text-gray-800 mb-4 not-italic">
              {address -> f:format.html()}
            </address>
          </f:if>
          <f:if condition="{center.url_route}">
            <a href="{center.url_route}" target="_blank" class="flex gap-2 items-center text-gray-800">
              <dbic:icon name="location-marker" class="size-4 text-brand-500" />
              Anfahrtsbeschreibung
            </a>
          </f:if>
        </div>
      </f:if>
    </div>
  </div>

</html>
