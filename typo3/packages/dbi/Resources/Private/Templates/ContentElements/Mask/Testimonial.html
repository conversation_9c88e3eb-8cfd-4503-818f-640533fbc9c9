<html xmlns="http://www.w3.org/1999/xhtml"
    xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
    xmlns:dbic="http://typo3.org/ns/Mogic/Dbi/Components"
    data-namespace-typo3-fluid="true">

  <f:layout name="Default" />

  <f:section name="Main">
    <dbic:testimonial
      theme="{data.tx_mask_theme}"
      personName="{data.tx_mask_label}"
      title="{data.subheader}"
      avatar="{data.assets.0}"
    >
      <f:if condition="{data.subheader}">
        <f:then>
          <span class="mb-3 text-[.75em] text-gray-600 dark:text-current">
            {data.subheader}
          </span>
          {data.bodytext}
        </f:then>
        <f:else>
          <dbic:rating
            score="{data.tx_mask_score}"
            source="{data.tx_mask_source}"
          >
            {data.bodytext}
          </dbic:rating>
        </f:else>
      </f:if>
    </dbic:testimonial>
  </f:section>
</html>
