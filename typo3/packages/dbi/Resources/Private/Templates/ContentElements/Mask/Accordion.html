<html xmlns="http://www.w3.org/1999/xhtml"
  xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
  xmlns:dbic="http://typo3.org/ns/Mogic/Dbi/Components"
  data-namespace-typo3-fluid="true">

<f:layout name="Default" />

<f:section name="Main">
    <f:if condition="{data.tx_mask_items}">
        <div class="w-full">
            <f:for each="{data.tx_mask_items}" as="item" iteration="iterator">
                <f:variable name="enable" value="0" />
                <f:if condition="{iterator.index} == 0">
                    <f:variable name="enable" value="{data.tx_mask_enable_first_item}" />
                </f:if>
                <dbic:accordion
                    header="{
                        headline: item.header,
                        layout: item.header_layout,
                    }"
                    open="{enable}"
                >
                    {item.bodytext -> f:format.html()}
                </dbic:accordion>
            </f:for>
        </div>
    </f:if>
</f:section>

</html>
