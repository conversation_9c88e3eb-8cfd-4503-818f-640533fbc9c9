
<html xmlns="http://www.w3.org/1999/xhtml" lang="en"
  xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
  xmlns:v="http://typo3.org/ns/FluidTYPO3/Vhs/ViewHelpers"
  xmlns:dbic="http://typo3.org/ns/Mogic/Dbi/Components"
  xmlns:dbi="http://typo3.org/ns/Mogic/Dbi/ViewHelpers"
  data-namespace-typo3-fluid="true"
>
    <f:variable name="pageUid" value="{v:page.info(field: 'uid')}" />

    <f:variable name="title"><v:page.info pageUid="{pageUid}" field="title" /></f:variable>
    <f:if condition="{title} == 'Centerinhalt'">
        {dbi:centerData() -> v:variable.set(name: 'centerData')}
        <f:variable name="title" value="{centerData.name2}" />
    </f:if>

    <f:variable name="customCTA" value="" />
    <f:if condition="{data.tx_mask_link}">
        <f:variable name="customCTA" value="{data.tx_mask_link}" />
    </f:if>

    <dbic:content.navigation
        title="{title}"
        customCTA="{customCTA}"
        class="z-10"
    />
</html>
