<html xmlns="http://www.w3.org/1999/xhtml"
    xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
    xmlns:v="http://typo3.org/ns/FluidTYPO3/Vhs/ViewHelpers"
    data-namespace-typo3-fluid="true"
    >

    <f:variable name="context"><v:context.get/></f:variable>
    <f:variable name="rendered">0</f:variable>
    <f:for each="{data.tx_mask_env}" as="envRow">
        <f:if condition="{envRow.tx_mask_environment} == {context} OR {envRow.tx_mask_environment} == ''">
            <f:if condition="{rendered} == 0">
                <f:format.raw>{envRow.tx_mask_html}</f:format.raw>
                <f:variable name="rendered">1</f:variable>
            </f:if>
        </f:if>
    </f:for>

</html>
