<html xmlns="http://www.w3.org/1999/xhtml" lang="en"
  xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
  xmlns:v="http://typo3.org/ns/FluidTYPO3/Vhs/ViewHelpers"
  xmlns:dbi="http://typo3.org/ns/Mogic/Dbi/ViewHelpers"
  xmlns:dbic="http://typo3.org/ns/Mogic/Dbi/Components"
  data-namespace-typo3-fluid="true"
>

  <div data-container-nav-title="Unser Team" data-container-anchor="1">
    <f:variable name="members" value="{dbi:teamMembers(recordRows: data.tx_mask_records_items)}" />
    <f:if condition="{members -> f:count()} == 1">
      <f:variable name="containerClass" value="max-w-container-indent" />
      <f:variable name="containerChildClass" value="py-6 border-y" />
    </f:if>

    <f:if condition="{data.header}">
      <f:variable name="header" value="{
        headline: data.header,
        layout: data.header_layout,
        subline: data.subheader
      }" />
    </f:if>

    <f:render section="List" arguments="{
      containerClass: containerClass,
      containerChildClass: containerChildClass,
      header: header,
      members: members,
      gridConfig: data.tx_mask_grid_config
    }"/>
  </div>

  <f:section name="List">
    <div class="container w-full py-12 lg:py-16 {containerClass}">
      <f:if condition="{header}">
        <dbic:headline
          headline="{header.headline}"
          layout="{header.layout}"
          subline="{header.subline}"
          class="text-center mb-8 md:mb-6 lg:mb-10"
        />
      </f:if>

      <f:switch expression="{gridConfig}">
        <f:case value="3">
         <f:variable name="gridColClass" value="lg:grid-cols-3 lg:divide-x lg:divide-gap-x-8 lg:divide-y-0 lg:divide-gap-y-0" />
        </f:case>
        <f:case value="2">
         <f:variable name="gridColClass" value="lg:grid-cols-2 lg:divide-x lg:divide-gap-x-8 lg:divide-y-0 lg:divide-gap-y-0" />
        </f:case>
      </f:switch>

      <div class="grid {containerChildClass} divide-y divide-gap-y-8">
        <v:iterator.chunk subject="{members}" count="{gridConfig}" as="memberRows">

          <f:for each="{memberRows}" as="memberRow">
            <div class="grid grid-cols-1 divide-y divide-gap-y-8 {gridColClass}">
              <f:for each="{memberRow}" as="member">
                <dbic:team.member.card member="{member}" />
              </f:for>
            </div>
          </f:for>
        </v:iterator.chunk>
      </div>
    </div>
  </f:section>
</html>
