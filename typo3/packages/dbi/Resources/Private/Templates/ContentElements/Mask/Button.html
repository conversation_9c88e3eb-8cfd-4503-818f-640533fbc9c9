<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
      xmlns:dbic="http://typo3.org/ns/Mogic/Dbi/Components"
      data-namespace-typo3-fluid="true"
      >
  <f:if condition="{data.header}">
    <f:then>
      <dbic:button
        link="{data.tx_mask_link}"
        theme="{data.tx_mask_theme}"
        icon="{data.tx_mask_icon}"
      >
        {data.header}
      </dbic:button>
    </f:then>
    <f:else>
      <dbic:button
        link="{data.tx_mask_link}"
        theme="{data.tx_mask_theme}"
        icon="{data.tx_mask_icon}"
      />
    </f:else>
  </f:if>
</html>
