<?xml version="1.0" encoding="utf-8"?>
<html
  xmlns="http://www.w3.org/1999/xhtml"
  lang="en"
  xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers"
  xmlns:dbic="http://typo3.org/ns/Mogic/Dbi/Components"
  data-namespace-typo3-fluid="true"
  xmlns:x-model="http://example.org/dummy-ns"
  xmlns:x-on="http://example.org/dummy-ns"
  xmlns:x-bind="http://example.org/dummy-ns"
  xmlns:x-ref="http://example.org/dummy-ns"
  xmlns:x-cloak="http://example.org/dummy-ns"
  xmlns:x-transition="http://example.org/dummy-ns"
>
  <f:layout name="EmptyP" />

  <f:section name="p">
    <f:comment>
      <f:render
        partial="RealtorSearch/Form.html"
        section="p"
        arguments="{_all}"
      />
    </f:comment>

    <dbic:content.snippet
      header="{
        headline: settings.title,
        layout: 101
      }"
      orientation="horizontal"
      theme="{settings.theme}"
    >
      <f:if condition="{settings.subtitle}">{settings.subtitle}</f:if>

      <f:variable
        name="autocompleteModulOptions"
        value="{
          query: q
        }"
      />

      <form
        method="GET"
        action="{f:uri.page(pageUid: settings.target_page)}#results"
        id="realtor-search"
        class="search-form"
        x-data="autocomplete({autocompleteModulOptions -> v:format.json.encode()})"
        x-on:submit.prevent="submit"
        x-ref="form"
      >
        <input
          type="hidden"
          x-ref="searchUrl"
          value="{f:uri.page(pageUid: settings.target_page, pageType: 5)}"
        />

        <div
          class="relative"
          x-on:click.away="active=false; cursorIndex=-1"
          x-on:keydown.escape="active=false; cursorIndex=-1"
          x-on:keydown.arrow-down=" moveCursorDown()"
          x-on:keydown.arrow-up="moveCursorUp()"
          x-on:keydown.enter="enterValue()"
        >
          <div class="flex w-full">
            <div
              class="flex border border-r-0 pl-4 pr-3 py-3.5 gap-3 rounded-tl-xs rounded-bl-xs items-center grow"
              x-bind:class="active ? 'border-brand-400 dark:border-electric-brand-500 outline outline-brand-400 dark:outline-electric-brand-500' : 'border-gray-600 dark:border-brand-200'"
            >
              <label for="realtor-search-input" class="sr-only"
                >Makler suchen</label
              >
              <input
                name="q"
                id="realtor-search-input"
                type="search"
                x-model="inputValue"
                x-on:focus="(e)=>handleFocus(e)"
                x-on:blur="(e)=>handleBlur(e)"
                x-on:input.debounce.250="$dispatch('input-change', inputValue)"
                class="border-0 bg-transparent grow text-base outline-hidden placeholder:text-brand-950! dark:placeholder:text-white! caret-brand-500 dark:caret-electric-brand-500"
                autocorrect="off"
                autocomplete="off"
                autocapitalize="off"
                maxlength="30"
                x-ref="input"
                aria-label="Makler suchen"
                placeholder="PLZ, Ort oder Name Immobilienberater*in"
              />
              <button
                type="button"
                x-on:click="clearValue"
                class="cursor-pointer flex-none size-5"
                x-show="inputValue.length > 0"
                aria-label="Eingabe löschen"
              >
                <dbic:icon name="x" class="flex-none size-full" />
              </button>
            </div>
            <button
              type="submit"
              class="flex justify-center items-center w-13.5 h-13.5 p-2 bg-brand-400 rounded-tr-xs rounded-br-xs border-1 border-brand-400 cursor-pointer"
              aria-label="Suchen"
            >
              <dbic:icon name="search" class="w-6 h-6" />
            </button>
          </div>
          <div
            class="absolute w-full z-50"
            x-show="listIsEnabled()"
            x-cloak=""
            x-on:input-change.window="active=true;cursorIndex=-1;fetch($event.detail)"
            x-transition:enter="transition ease-out duration-200"
            x-transition:enter-start="opacity-50 transform "
            x-transition:enter-end="opacity-100 transform scale-y-100"
            x-transition:leave="transition ease-in duration-100"
            x-transition:leave-start="opacity-100 transform scale-y-100"
            x-transition:leave-end="opacity-50 transform scale-y-90"
          >
            <div
              class="absolute w-full bg-brand-50 dark:bg-brand-900 py-6 px-10.5 font-medium"
            >
              <template x-for="suggestionKey in suggestionKeys">
                <ul class="flex flex-col gap-3 pb-6">
                  <template x-if="suggestionKey == 'tradingareas'">
                    <li class="pb-3 border-b border-white">
                      Ort – Postleitzahl
                    </li>
                  </template>
                  <template x-if="suggestionKey == 'members'">
                    <li class="pb-3 border-b border-white">Makler</li>
                  </template>
                  <template
                    x-for="suggestion in suggestions.filter(item => item.type == suggestionKey)"
                    x-key="suggestion.index"
                  >
                    <li
                      class="hover:underline hover:text-electric-brand-400 cursor-pointer"
                      x-bind:class="{'underline': cursorIndex == suggestion.index}"
                      x-on:click="submitValue(suggestion.query);"
                    >
                      <span x-text="suggestion.title"></span>
                    </li>
                  </template>
                </ul>
              </template>
            </div>
          </div>
        </div>
      </form>
    </dbic:content.snippet>
  </f:section>
</html>
