<?xml version="1.0" encoding="utf-8"?>
<html xmlns="http://www.w3.org/1999/xhtml" lang="en"
     xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers"
     xmlns:v="http://typo3.org/ns/FluidTYPO3/Vhs/ViewHelpers"
     xmlns:wsb="http://typo3.org/ns/Fio/Websitebuilder/ViewHelpers"
     xmlns:dbic="http://typo3.org/ns/Mogic/Dbi/Components"
     xmlns:x-on="http://example.org/dummy-ns"
>
  <f:layout name="EmptyP"/>

  <f:section name="p">
    <f:comment>
      <f:render partial="RealtorSearch/Form.html" section="p" arguments="{_all}"/>
    </f:comment>

    <f:if condition="{members -> f:count()}">
      <f:then>
        <f:render section="List" arguments="{_all}"/>
      </f:then>
      <f:else>
        <f:render section="Notification" arguments="{_all}"/>
      </f:else>
    </f:if>
  </f:section>

  <f:section name="Notification">
    <f:variable name="message"
      value="Wir haben keine Immobilienberater*in mit diesem Namen gefunden." />

    <f:if condition="{searchMode} == 'postalCode'">
      <f:variable name="message"
        value="Zu dieser Postleitzahl haben wir keine Immobilienberater*in gefunden." />
    </f:if>

    <f:if condition="{error}">
      <f:variable name="message"
        value="{error}" />
    </f:if>

    <dbic:alert type="error">
      {message}
    </dbic:alert>
  </f:section>

  <f:section name="List">
    <a name="results" class="inline-block mb-4 lg:mb-8 text-gray-800">
      {members -> f:count()} Immobilienberater*in gefunden.
    </a>

    <div class="w-full py-8 border-y divide-y divide-gap-y-8 border-gray-300 mb-4 lg:mb-8">
      <f:for each="{members}" as="teammember">
          <dbic:team.member.card
            member="{teammember}"
            hideRatingLink="1"
            showProfileLink="1" />
      </f:for>
    </div>

    <f:if condition="{hasMoreResults}">
      <span class="font-bold">
        Es gibt noch mehr Immobilienberater*innen, die zu Ihrer Sucheingabe passen.
        Präzisieren Sie ihren Suchbegriff um sie zu sehen.
      </span>
    </f:if>
  </f:section>
</html>
