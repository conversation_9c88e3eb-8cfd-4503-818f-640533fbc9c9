<html xmlns="http://www.w3.org/1999/xhtml"
  xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
  data-namespace-typo3-fluid="true">

<f:layout name="Default"/>

<f:section name="Main">

    <div class="ce-timeline">
    <f:if condition="{data.bodytext}">
        <f:format.html>{data.bodytext}</f:format.html>
    </f:if>

    <ul class="ce-timeline-list">
        <f:for each="{entries}" as="entry">
            <f:switch expression="{entry.data.typeof}">
                <f:case value="0">
                    <f:render partial="Textblock" arguments="{entry:entry.data}"/>
                </f:case>
                <f:case value="1">
                    <f:render partial="Time" arguments="{entry:entry.data}"/>
                </f:case>
                <f:case value="2">
                    <f:render partial="TextImage" arguments="{entry:entry.data,media:entry.media}"/>
                </f:case>
                <f:case value="3">
                    <f:render partial="Html" arguments="{entry:entry.data}"/>
                </f:case>
            </f:switch>
        </f:for>
    </ul>
    </div>

    <f:asset.css href="EXT:dbi/Resources/Public/Css/timeline_dbi.css"
        identifier="ce-timeline-css" />
</f:section>

</html>