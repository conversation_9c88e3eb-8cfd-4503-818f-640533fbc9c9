<html
	xmlns:vh="http://typo3.org/ns/In2code/Powermail/ViewHelpers"
	xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
  	xmlns:dbic="http://typo3.org/ns/Mogic/Dbi/Components"
  	data-namespace-typo3-fluid="true"
>
    <f:layout name="Default"/>

    <f:section name="main">
        <f:alias map="{flashMessageClass:'powermail_message_error'}">
            <f:render partial="Misc/FlashMessages" arguments="{_all}"/>
        </f:alias>

        <f:if condition="{form}">
            <f:then>
                <div class="container">
                    <f:form
                        action="{action}"
                        section="c{ttContentData.uid}"
                        name="field"
                        enctype="multipart/form-data"
                        additionalAttributes="{vh:validation.enableJavascriptValidationAndAjax(form:form)}"
                        addQueryString="{settings.misc.addQueryString}"
                        class="group/form {settings.styles.framework.formClasses} {vh:misc.morestepClass(activate:settings.main.moresteps)}">

                        <f:if condition="{ttContentData.header_layout} != 'hidden'">
                            <dbic:headline
                                headline="{ttContentData.header}"
                                subline="{ttContentData.subheader}"
                                layout="{ttContentData.header_layout}"
                                class="text-center mb-10"
                            />
                        </f:if>

                        <f:render partial="Misc/FormError" arguments="{_all}"/>

                        <f:if condition="{settings.main.moresteps}">
                            <div class="group/buttons" role="group">
                                <f:for each="{form.pages}" as="page" iteration="iterationPages">
                                    <dbic:button
                                        theme="{iterationPages.isFirst} ? 'primary' : 'secondary'"
                                        additionalAttributes="{
                                            'data-powermail-morestep-show' : iterationPages.index,
                                            'data-powermail-morestep-current' : iterationPages.index
                                        }"
                                    >
                                        {page.title}
                                    </dbic:button>
                                </f:for>
                            </div>
                        </f:if>

                        <f:for each="{form.pages}" as="page" iteration="iterationPages">
                            <f:render partial="Form/Page" arguments="{_all}"/>
                        </f:for>
                        <f:form.hidden name="mail[form]" value="{form.uid}" respectSubmittedDataValue="false" />
                        <f:render partial="Misc/HoneyPod" arguments="{form:form}"/>
                    </f:form>
                </div>
            </f:then>
            <f:else>
                <f:translate key="error_no_form"/>
            </f:else>
        </f:if>
    </f:section>
</html>
