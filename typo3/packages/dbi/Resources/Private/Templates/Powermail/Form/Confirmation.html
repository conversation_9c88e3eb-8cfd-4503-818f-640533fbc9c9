<html
	xmlns:vh="http://typo3.org/ns/In2code/Powermail/ViewHelpers"
	xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
  	xmlns:dbic="http://typo3.org/ns/Mogic/Dbi/Components"
  	data-namespace-typo3-fluid="true"
>
    <f:layout name="Default" />

    Show Confirmation Page
    {powermail_rte}					Variable is filled with values from RTE in backend (from thx page)
    {powermail_all}					Outputs all fields
    {marker1}, {firstname}, etc..	Outputs a field
    {mail}							Complete Mail Object
    {ttContentData}					All values from content element with plugin
    {uploadService}					All values from uploaded files


    <f:section name="main">
        <div class="container prose-a:underline" data-powermail-form="{mail.form.uid}">
            <dbic:headline
                headline="{f:translate(key: 'confirmation_message')}"
                layout="105"
                class="mb-5"
            />

            <f:format.raw>{powermail_all}</f:format.raw>

            <div class="group/buttons flex justify-between py-10" role="group">
                <f:comment>
                    Link: Back to form
                </f:comment>
                <f:form
                        action="form"
                        section="c{ttContentData.uid}"
                        name="field"
                        enctype="multipart/form-data"
                        addQueryString="{settings.misc.addQueryString}"
                        additionalAttributes="{vh:validation.enableJavascriptValidationAndAjax(form: mail.form)}">
                    <f:render section="HiddenFields" arguments="{_all}" />
                    <dbic:button
                        type="submit"
                        additionalAttributes="{data-powermail-form-ajax:'confirmation'}"
                        theme="secondary"
                        icon="chevron-left"
                    >
                        {f:translate(key: 'confirmation_prev')}
                    </dbic:button>
                </f:form>

                <f:comment>
                    Link: Submit form
                </f:comment>
                <f:form
                        action="checkCreate"
                        section="c{ttContentData.uid}"
                        name="field"
                        enctype="multipart/form-data"
                        addQueryString="{settings.misc.addQueryString}"
                        additionalAttributes="{vh:validation.enableJavascriptValidationAndAjax(form: mail.form)}">
                    <f:render section="HiddenFields" arguments="{_all}" />
                    <dbic:button
                        type="submit"
                        additionalAttributes="{data-powermail-form-ajax:'submit'}"
                    >
                        {f:translate(key: 'confirmation_next')}
                    </dbic:button>
                </f:form>
            </div>
        </div>
    </f:section>



    <f:section name="HiddenFields">
        <f:for each="{mail.answers}" as="answer">
            <f:if condition="{vh:condition.isArray(val:answer.value)}">
                <f:then>
                    <f:for each="{answer.value}" as="subvalue" iteration="i">
                        <f:form.hidden property="{answer.field.marker}.{i.index}" value="{subvalue}" respectSubmittedDataValue="false"  />
                    </f:for>
                </f:then>
                <f:else>
                    <f:form.hidden property="{answer.field.marker}" value="{answer.value}" respectSubmittedDataValue="false"  />
                </f:else>
            </f:if>
        </f:for>

        <f:form.hidden name="mail[form]" value="{mail.form.uid}" class="powermail_form_uid" />
    </f:section>
</html>