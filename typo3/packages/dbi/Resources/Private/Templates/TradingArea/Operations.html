<?xml version="1.0" encoding="utf-8"?>
<html xmlns="http://www.w3.org/1999/xhtml" lang="en"
     xmlns:be="http://typo3.org/ns/TYPO3/CMS/Backend/ViewHelpers"
     xmlns:core="http://typo3.org/ns/TYPO3/CMS/Core/ViewHelpers"
     xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers"
>
  <f:layout name="BackendDefault"/>

  <f:section name="Main">
    <h1><core:icon size="medium" title="" identifier="tx-dbi-tradingarea"/> Vertriebsgebietszuordnungen</h1>
    <p>
      Die
      <a class="text-decoration-underline" href="{be:moduleLink(route:'web_list', arguments:{id:0, table:'tx_dbi_tradingareas'})}">{numAreas} Zuordnungen</a>
      zwischen Vertriebsgebiets-Postleitzahlen und
      FIO-Nutzernamen können hier verwaltet werden.
    </p>

    <h2>Export</h2>
    <p>
      <a class="btn btn-default" href="{be:moduleLink(route:'dbi_tradingarea.TradingArea_export')}"><core:icon title="" identifier="actions-database-export"/> Aktuelle Zuordnungen herunterladen</a>
    </p>

    <h2>Import</h2>
    <p>
      Die Tabellendatei muss mindestens folgende Spalten enthalten:
    </p>
    <ul>
      <li>Postleitzahl</li>
      <li>FIO-Nutzername neu</li>
    </ul>

    <f:form action="operations" enctype="multipart/form-data">
      <style type="text/css">
        .error {
          color: red;
        }
      </style>

      <div class="form-group">
        <div class="form-section">
          <div class="row">
            <div class="form-group col-sm-12">
              <label for="file">Datei</label>*
              <f:if condition="{errors.file}">
                <div class="error">{errors.file}</div>
              </f:if>
              <div class="form-control-wrap">
                <input class="form-control" type="file" id="file" name="file"
                       accept=".csv,.ods,.xlsx,application/vnd.oasis.opendocument.spreadsheet,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,text/csv,"
                       required=""/>
              </div>
            </div>
          </div>
        </div>

        <div class="form-section">
          <div class="row">
            <div class="form-group col-sm-12">
              <div class="form-control-wrap">
                <label>
                  <f:form.checkbox name="deleteMissing" value="1"/>
                  Vertriebsgebiete löschen, die nicht in der Datei enthalten sind
                </label>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="form-group">
        <button class="btn btn-default" type="submit"><core:icon title="" identifier="actions-database-import"/> Vertriebsgebietszuordnungen importieren</button>
      </div>
    </f:form>
  </f:section>
 </html>
