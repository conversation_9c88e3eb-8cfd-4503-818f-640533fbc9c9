<?xml version="1.0" encoding="utf-8"?>
<html xmlns="http://www.w3.org/1999/xhtml" lang="en"
     xmlns:be="http://typo3.org/ns/TYPO3/CMS/Backend/ViewHelpers"
     xmlns:core="http://typo3.org/ns/TYPO3/CMS/Core/ViewHelpers"
     xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers"
>
  <f:layout name="BackendDefault"/>

  <f:section name="Main">
    <h1><core:icon size="medium" title="" identifier="tx-dbi-tradingarea"/> Datenprüfung Vertriebsgebietszuordnungen</h1>

    <h2 id="missing">Fe<PERSON><PERSON><PERSON>kler</h2>
    <f:if condition="{missingMembers -> f:count()}">
      <f:then>
        <p>
          {missingMembers -> f:count()} Makler fehlen:
        </p>
        <div class="table-fit">
          <table class="table table-striped table-hover table-vertical-top">
            <thead>
              <tr>
                <th>FIO-Nutzername</th>
                <th>Postleitzahlen</th>
              </tr>
            </thead>
            <tbody>
              <f:for each="{missingMembers}" as="areas" key="username">
                <tr>
                  <td style="width: 20ex;">
                    {username}
                  </td>
                  <td>
                    <f:for each="{areas}" as="area">
                      <be:link.editRecord uid="{area.uid}" table="tx_dbi_tradingareas" class="text-decoration-underline">{area.postalcode}</be:link.editRecord>
                    </f:for>
                  </td>
                </tr>
              </f:for>
            </tbody>
          </table>
        </div>
      </f:then>
      <f:else>
        <p>Alle Makler vorhanden.</p>
      </f:else>
    </f:if>


    <h2 id="disabled">Deaktivierte Makler</h2>
    <f:if condition="{disabledMembers -> f:count()}">
      <f:then>
        <p>
          {disabledMembers -> f:count()} Makler sind versteckt oder gelöscht:
        </p>
        <div class="table-fit">
          <table class="table table-striped table-hover table-vertical-top">
            <thead>
              <tr>
                <th>FIO-Nutzername</th>
                <th>Status</th>
                <th>Postleitzahlen</th>
              </tr>
            </thead>
            <f:for each="{disabledMembers}" as="member">
              <tr>
                <td style="width: 20ex; white-space: nowrap;">
                  <f:if condition="{member.deleted}">
                    <f:then>
                      <core:iconForRecord table="tx_dbi_teammember" row="{member}"/> {member.username}
                    </f:then>
                    <f:else>
                      <be:link.editRecord uid="{member.uid}" table="tx_dbi_teammember" class="text-decoration-underline">
                        <core:iconForRecord table="tx_dbi_teammember" row="{member}"/> {member.username}
                      </be:link.editRecord>
                    </f:else>
                  </f:if>
                </td>
                <td>
                  <f:if condition="{member.deleted}">
                    <f:then>gelöscht</f:then>
                    <f:else>versteckt</f:else>
                  </f:if>
                </td>
                <td>
                  <f:for each="{member.areas}" as="area">
                    <be:link.editRecord uid="{area.uid}" table="tx_dbi_tradingareas" class="text-decoration-underline">{area.postalcode}</be:link.editRecord>
                  </f:for>
                </td>
              </tr>
            </f:for>
          </table>
        </div>
      </f:then>
      <f:else>
        <p>Alle Makler vorhanden.</p>
      </f:else>
    </f:if>


    <h2 id="memberless">Vertriebsgebiete ohne Makler</h2>
    <f:if condition="{memberlessAreas -> f:count()}">
      <f:then>
        <p>
          {memberlessAreas -> f:count()} Postleitzahlen haben keinen Makler:
        </p>
        <div class="table-fit">
          <table class="table table-striped table-hover table-vertical-top">
            <thead>
              <tr>
                <th>Postleitzahlen</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>
                  <f:for each="{memberlessAreas}" as="area">
                    <be:link.editRecord uid="{area.uid}" table="tx_dbi_tradingareas" class="text-decoration-underline">{area.postalcode}</be:link.editRecord>
                  </f:for>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </f:then>
      <f:else>
        <p>Alle Vertriebsgebiete haben einen FIO-Nutzernamen gesetzt.</p>
      </f:else>
    </f:if>

  </f:section>
</html>
