<html
  xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
  xmlns:fc="http://typo3.org/ns/SMS/FluidComponents/ViewHelpers"
  xmlns:v="http://typo3.org/ns/FluidTYPO3/Vhs/ViewHelpers"
  xmlns:dbi="http://typo3.org/ns/Mogic/Dbi/ViewHelpers"
  data-namespace-typo3-fluid="true"
  >
  <fc:component>
    <fc:param name="image" type="Image" />
    <fc:param name="ratio" type="string" optional="1" default="aspect-image" />

    <f:variable name="cropJson" value="{image.properties.crop -> v:format.json.decode()}" />
    <f:variable name="focusX" value="{cropJson.default.focusArea.x}" />
    <f:variable name="focusY" value="{cropJson.default.focusArea.y}" />
    <f:variable name="objectPosition" value="{dbi:imageFocusPosition(focusX: focusX, focusY: focusY)}" />

    <fc:renderer>
      <f:variable name="ratioClass" value="{ratio}" />
      <f:if condition="{ratio}=='none'">
        <v:variable.set name="ratioClass" value="" />
      </f:if>

      <f:switch expression="{image.type}">
        <f:case value="FalImage">
          <v:variable.set name="cropSettings" value="{image.file.properties.crop -> v:format.json.decode()}" />
          <f:if condition="{cropSettings.default.selectedRatio}=='landscape'">
            <v:variable.set name="ratioClass" value="aspect-image-wide" />
          </f:if>
          <v:variable.set name="class" value="{cropClasses} {class}" />
          <f:image
            image="{image.file}"
            alt="{image.alternative}"
            title="{image.title}"
            class="{class} {objectPosition}"
          />
        </f:case>
        <f:defaultCase>
          <img
            src="{image.publicUrl}"
            alt="{image.alternative}"
            title="{image.title}"
            class="{ratioClass} {class} {objectPosition}"
          />
        </f:defaultCase>
    </f:switch>
    </fc:renderer>
  </fc:component>
</html>
