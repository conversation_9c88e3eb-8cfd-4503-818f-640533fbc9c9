<html
    data-namespace-typo3-fluid="true"
    xmlns="http://www.w3.org/1999/xhtml"
    xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers"
    xmlns:fc="http://typo3.org/ns/SMS/FluidComponents/ViewHelpers"
    xmlns:dbic="http://typo3.org/ns/Mogic/Dbi/Components"
    >
    <fc:component>
        <fc:param name="items" type="array" />

        <!-- list-none|list-dash|list-checkmark|list-decimal -->
        <fc:param name="type" type="string" optional="1" default="list-none" />

        <f:variable name="listClasses" value="space-y-4 text-gray-800 dark:text-white" />
        <f:variable name="itemClasses" value="flex items-start whitespace-normal"/>
        <f:variable name="itemTypeClasses" value=""/>
        <f:if condition="{type} == 'list-decimal'">
            <f:variable
                name="itemTypeClasses"
                value="before:flex before:items-center before:justify-center before:size-[1.575em] before:mr-2.5 before:text-[.875em] before:shrink-0 before:border before:rounded-full before:border-current"/>
        </f:if>
        <f:if condition="{type} == 'list-dash'">
            <f:variable
                name="itemTypeClasses"
                value="before:size-[1.575em] before:mr-2.5 before:text-[.875em] before:shrink-0"
                />
        </f:if>

        <fc:renderer>
            <f:if condition="{type} == 'list-decimal'">
                <f:then>
                    <ol class="{listClasses} list-counter">
                        <f:for each="{items}" as="item">
                            <li class="{itemClasses}">
                              <div>
                                {item.tx_mask_list_item -> f:format.html()}
                              </div>
                            </li>
                        </f:for>
                    </ol>
                </f:then>
                <f:else>
                    <ul class="{listClasses} {type}">
                        <f:for each="{items}" as="item">
                            <li class="{itemClasses}">
                                <f:if condition="{type} == 'list-checkmark'">
                                    <dbic:icon name="check" class="size-[1.575em] mr-2.5 text-[.875em] shrink-0" />
                                </f:if>
                                <div>
                                  {item.tx_mask_list_item -> f:format.html()}
                                </div>
                            </li>
                        </f:for>
                    </ul>
                </f:else>
            </f:if>
        </fc:renderer>
    </fc:component>
</html>
