<html
    xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
    xmlns:fc="http://typo3.org/ns/SMS/FluidComponents/ViewHelpers"
    xmlns:dbic="http://typo3.org/ns/Mogic/Dbi/Components"
    data-namespace-typo3-fluid="true"
    >
    <fc:component>
        <fc:param name="header" type="Array" />
        <fc:param name="open" type="boolean" optional="1" default="0" />

        <fc:renderer>
            <f:if condition="{content}">
                <details
                    {f:if(condition: '{open}', then: 'open', else: '')}
                    class="group text-brand-950 dark:text-white border-b border-b-gray-500 group-[.grid]/personBox:border-none {class}"
                >
                    <summary class="p-4 group-[.grid]/personBox:px-0 flex justify-start items-center gap-4
                        cursor-pointer text-lg font-medium
                        group-focus-visible::border-2 group-focus-visible:border-brand-950">
                        <dbic:icon
                            name="plus"
                            class="w-6 flex-none transition-transform duration-300 ease-in-out group-open:hidden"
                            />
                        <dbic:icon
                            name="minus"
                            class="w-6 flex-none transition-transform duration-300 ease-in-out hidden group-open:block"
                            />
                        <dbic:headline
                            headline="{header.headline}"
                            layout="{header.layout}"
                        />
                    </summary>
                    <div class="pb-4 pl-14">
                        <fc:slot />
                    </div>
                </details>
            </f:if>
        </fc:renderer>
    </fc:component>
</html>
