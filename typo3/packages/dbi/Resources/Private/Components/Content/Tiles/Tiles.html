<html
    xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
    xmlns:fc="http://typo3.org/ns/SMS/FluidComponents/ViewHelpers"
    data-namespace-typo3-fluid="true"
    >
    <fc:component>
        <fc:param name="columns" type="string" optional="1" default="5" />
        <fc:renderer>
            <f:switch expression="{columns}">
                <f:case value="2">
                    <f:variable name="childClasses" value="*:basis-[calc(50%-1em)]" />
                </f:case>

                <f:case value="3">
                    <f:variable name="childClasses" value="*:basis-[calc(33%-1em)]" />
                </f:case>

                <f:case value="4">
                    <f:variable name="childClasses" value="*:basis-[calc(25%-1em)]" />
                </f:case>

                <f:case value="5">
                    <f:variable name="childClasses" value="*:basis-[calc(20%-1em)]" />
                </f:case>

                <f:case value="1 lg:3">
                    <f:variable name="childClasses" value="*:basis-full lg:*:basis-[calc(33%-1em)]" />
                </f:case>

                <f:case value="2 lg:4">
                    <f:variable name="childClasses" value="*:basis-[calc(50%-1em)] lg:*:basis-[calc(25%-1em)]" />
                </f:case>

                <f:case value="3 lg:5">
                    <f:variable name="childClasses" value="*:basis-[calc(33%-1em)] lg:*:basis-[calc(20%-1em)]" />
                </f:case>

                <f:defaultCase>
                    <f:variable name="childClasses" value="*:basis-full" />
                </f:defaultCase>
            </f:switch>
            <f:if condition="{content}">
                <div class="flex flex-row flex-wrap gap-[1em] {childClasses} justify-center">
                    <fc:slot />
                </div>
            </f:if>
        </fc:renderer>
    </fc:component>
</html>
