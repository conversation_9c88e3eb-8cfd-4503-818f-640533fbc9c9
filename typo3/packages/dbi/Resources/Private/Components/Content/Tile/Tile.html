<html
  xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
  xmlns:fc="http://typo3.org/ns/SMS/FluidComponents/ViewHelpers"
  xmlns:dbic="http://typo3.org/ns/Mogic/Dbi/Components"
  data-namespace-typo3-fluid="true"
  >
  <fc:component>
    <fc:param name="orientation" type="string" optional="1" default="row" />
    <fc:param name="text" type="string" />
    <fc:param name="icon" type="string" optional="1" />
    <fc:param name="link" type="Typolink" optional="1" />
    <fc:param name="theme" type="string" optional="1" default="pale-brand" />

    <f:variable name="themeClass" value="bg-brand-700 hover:bg-brand-900 text-white hover:text-brand-200" />
    <f:if condition="{theme} == 'light-gray'">
      <f:variable name="themeClass" value="bg-gray-50 hover:bg-brand-100 text-brand-950 hover:text-brand-400" />
    </f:if>

    <f:variable name="layoutClasses" value="flex flex-row" />
    <f:if condition="{orientation} == 'column'">
      <f:variable name="layoutClasses" value="flex flex-col" />
    </f:if>

    <fc:renderer>
      <f:if condition="{link}">
        <f:then>
          <f:link.typolink parameter="{link}" class="group p-4 md:py-12
            {layoutClasses} items-center gap-3 md:gap-2
            {themeClass}
            font-medium text-lg
            group-hover:bg-white group-hover:text-brand-500
            group-focus:border-2 group-focus:border-brand-500
            {class}"
          >
            <f:if condition="{icon}">
              <dbic:icon name="{icon}" class="size-[2.8em]" />
            </f:if>
            <span>
              {text}
            </span>
          </f:link.typolink>
        </f:then>
        <f:else>
          <div class="group p-4 md:py-12
            {layoutClasses} items-center gap-3 md:gap-2
            {themeClass}
            font-medium text-lg
            group-hover:bg-white group-hover:text-brand-500
            group-focus:border-2 group-focus:border-brand-500
            {class}"
          >
            <f:if condition="{icon}">
              <dbic:icon name="{icon}" class="size-[2.8em]" />
            </f:if>
            <span>
              {text}
            </span>
          </div>
        </f:else>
      </f:if>
    </fc:renderer>
  </fc:component>
</html>
