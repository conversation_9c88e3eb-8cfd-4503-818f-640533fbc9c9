<html
  xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
  xmlns:fc="http://typo3.org/ns/SMS/FluidComponents/ViewHelpers"
  xmlns:dbic="http://typo3.org/ns/Mogic/Dbi/Components"
  xmlns:x-bind="http://example.org/dummy-ns"
  xmlns:x-on="http://example.org/dummy-ns"
  data-namespace-typo3-fluid="true"
  >
  <fc:component>
    <fc:param name="title" type="string" />
    <fc:param name="customCTA" type="Typolink" optional="true" />
    <fc:renderer>
        <nav aria-label="Sticky-Seitennavigation" aria-hidden="true" tabindex="-1" class="sticky top-0 left-0 md:group-has-[#subMainMenu]/page:top-0 w-full bg-white {class}">
          <div x-data="contentNavigation()"
              x-show="links.length > 1"
              class="relative container flex justify-between items-center gap-y-2.5 pt-4 md:py-4 gap-x-3 px-6 w-full text-base leading-none md:flex-row md:flex-nowrap md:text-sm md:leading-none xl:py-6 border-gray-500 md:border-b"
              x-on:click="open = !open"
              x-on:click.away="open = false"
              x-on:scroll.window.stop="handleScroll"
          >
            <div class="flex flex-col pr-6 w-full md:w-fit py-3 md:py-4 md:pr-3 md:pl-2 xl:pr-4 md:border-r md:border-gray-500 text-gray-800">
              <span class="relative">
                <div class="md:hidden absolute right-0 -top-2 h-10 border-r border-gray-500"></div>
                  {title}
              </span>
              <span
                x-show="currentIndex != undefined"
                class="w-full bg-white pt-3 md:pb-2 text-brand-500 font-medium  md:hidden"
                x-bind:class="{
                  'hidden' : open,
                }"
                x-text="subTitle">
              </span>
            </div>

            <button
                type="button"
                class="md:hidden transition absolute top-7 right-3"
                aria-controls="navigation-menu"
                aria-haspopup="true"
                aria-hidden="true"
                tabindex="-1"
                x-bind:class="{
                    'rotate-180' : open
                }"

            >
                <dbic:icon name="chevron-down" class="size-6 text-brand-500" />
            </button>
            <nav aria-label="Seitennavigation" class="grow md:w-full"
                x-bind:class="{
                    'contents' : open,
                    'hidden md:contents' : !open
                }"
                aria-hidden="true"
                tabindex="-1"
            >


                <dbic:scroller.horizontal class="md:justify-end md:ml-auto"  additionalAttributes="{'x-bind:class': 'currentIndex != undefined ? \'\' : \'\''}">
                    <template x-for="(link, index) in links">
                        <li class="text-gray-950 whitespace-nowrap pl-6 md:p-1.5 cursor-pointer hover:text-brand-400 focus:text-brand-400"
                            x-bind:class="{
                                'text-brand-500': currentIndex != undefined,
                                'md:before:absolute md:before:left-0 md:before:w-full md:before:h-full md:before:bg-white md:before:opacity-50' : isOverflowed($el)
                            }"
                            x-on:click.prevent="handleJumpTo(index)"
                        >
                            <button
                                type="button"
                                x-bind:id="'contentNavigationButton-' + index"
                                x-bind:title="link.title"
                                x-bind:class="{
                                    'text-brand-500 font-medium': index == currentIndex
                                }"
                                x-text="link.title"
                                aria-hidden="true"
                                tabindex="-1"
                            ></button>
                        </li>
                    </template>
                </dbic:scroller.horizontal>
            </nav>
            <f:if condition="{customCTA}">
                <div class="fixed bottom-0 z-50 md:static -ml-6 py-4 md:py-0 md:mx-0 w-full text-center md:w-auto bg-white">
                    <dbic:button link="{customCTA}"
                        theme="primary"
                        class="whitespace-nowrap px-8"
                        additionalAttributes="{'aria-label': '{customCTA.title}'}"
                    />
                </div>
            </f:if>
          </div>
        </nav>
    </fc:renderer>
  </fc:component>
</html>
