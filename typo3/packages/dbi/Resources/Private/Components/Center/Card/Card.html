<html xmlns="http://www.w3.org/1999/xhtml" lang="de"
     xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers"
     xmlns:fc="http://typo3.org/ns/SMS/FluidComponents/ViewHelpers"
     xmlns:dbic="http://typo3.org/ns/Mogic/Dbi/Components"
     data-namespace-typo3-fluid="true"
>
  <fc:component>
    <fc:param name="center" type="Center" />
    <fc:renderer>
      <div class="@container">
        <div class="flex flex-col gap-7 @2xl:flex-row @2xl:gap-6 group/card w-full {class} @2xl:items-center">
          <div class="flex flex-col gap-3 justify-center items-center @2xl:w-[320px] @2xl:max-w-[320px] @2xl:flex-none">
            <dbic:image
              image="{center.image}"
              class="object-cover"
            />
          </div>
          <div class="flex flex-col gap-5 pr-16 relative @2xl:flex-1">
            <dbic:headline
              headline="{center.name2}"
              layout="card"
              overline="IHRE MAKLER FÜR DIE REGION"
            />

            <f:if condition="{center.fullAddress}">
              <div class="flex gap-2">
                <dbic:icon name="location-marker" class="text-brand-950 size-5 flex-none mt-0.5" />
                <address class="text-base leading-normal not-italic text-brand-950">
                  {center.fullAddress -> f:format.html()}
                </address>
              </div>
            </f:if>

            <div class="flex flex-wrap gap-4">
              <dbic:button
                theme="secondary"
                link="{f:uri.typolink(parameter: center.pid)}"
                buttonSize="sm"
              >
                  Zum Team</dbic:button>
                <f:if condition="{center.marketPriceEstimationPid}">
                  <dbic:button buttonSize="sm" theme="secondary"
                  link="{f:uri.typolink(parameter: center.marketPriceEstimationPid)}">Verkaufsberatung anfragen</dbic:button>
                </f:if>
            </div>

            {center.allAwards -> v:iterator.slice(length: 3) -> v:variable.set(name: 'awards')}
            <f:if condition="{awards}">
              <div class="absolute top-0 right-0 flex place-content-start  gap-4 flex-wrap items-start w-1/3 md:w-12 lg:w-1/4 justify-end">
                <f:for each="{awards}" as="awardFileOrFileRef">
                  <dbic:image image="{awardFileOrFileRef}" ratio="" class="w-12" />
                </f:for>
              </div>
            </f:if>
          </div>
        </div>
      </div>
    </fc:renderer>
  </fc:component>
</html>
