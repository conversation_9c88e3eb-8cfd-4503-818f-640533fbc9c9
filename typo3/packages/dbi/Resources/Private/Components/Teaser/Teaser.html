<html
  xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
  xmlns:fc="http://typo3.org/ns/SMS/FluidComponents/ViewHelpers"
  xmlns:dbic="http://typo3.org/ns/Mogic/Dbi/Components"
  data-namespace-typo3-fluid="true"
  >
  <fc:component>
    <fc:param name="image" type="Image" optional="1" />

    <fc:param name="theme" type="string" optional="1" default="brand" />

    <!-- column|row|row-reverse-->
    <fc:param name="type" type="string" optional="1" default="column" />

    <fc:renderer>

      <f:variable name="imgOrderClass" value="" />
      <f:variable name="imageLayoutClass" value="object-cover object-bottom-right w-full" />

      <f:variable name="layoutClasses" value="flex flex-col" />
      <f:if condition="{type} != 'column'">
        <f:variable name="layoutClasses" value="grid md:grid-cols-2" />
        <f:variable name="imageLayoutClass" value="{imageLayoutClass} md:h-full" />
      </f:if>

      <f:if condition="{type} == 'row-reverse'">
        <f:variable name="imgOrderClass" value="md:order-2" />
      </f:if>

      <f:switch expression="{theme}">
        <f:case value="light-gray">
          <f:variable name="themeClass" value="bg-gray-50" />
        </f:case>
        <f:case value="light-brand">
          <f:variable name="themeClass" value="bg-brand-50" />
        </f:case>
        <f:case value="base">
          <!-- Möglichkeit für dunkleren Hintergrund -->
          <f:variable name="themeClass" value="bg-transparent" />
        </f:case>
        <f:case value="white">
          <f:variable name="themeClass" value="bg-white" />
        </f:case>
        <f:defaultCase>
          <f:variable name="themeClass" value="bg-brand-700" />
        </f:defaultCase>
      </f:switch>

      <div class="group/teaser overflow-hidden {layoutClasses} {themeClass} {class} group-[.stretched]/grid:h-full group-[.stretched]/carousel-item:h-full">
        <f:if condition="{image}">
          <dbic:image image="{image}" class="{imageLayoutClass} bg-white/30 group-hover/teaser:opacity-80 {imgOrderClass}" />
        </f:if>
        <fc:slot />
      </div>
    </fc:renderer>
  </fc:component>
</html>
