<html
  xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
  xmlns:fc="http://typo3.org/ns/SMS/FluidComponents/ViewHelpers"
  xmlns:dbic="http://typo3.org/ns/Mogic/Dbi/Components"
  data-namespace-typo3-fluid="true"
>
  <fc:component>
    <fc:param name="image" type="Image" />
    <fc:param name="divider" type="boolean" optional="1" default="0" />

    <fc:renderer>
      <f:variable name="contentWrapperClass" value="max-w-[850px] mx-auto" />

      <f:variable name="contentClasses" value="md:my-6" />

      <f:variable
        name="imageClasses"
        value="object-cover w-full h-full left-0 md:max-h-[650px]"
      />

      <f:variable
        name="contentClasses"
        value="max-w-container-indent mx-auto"
      />

      <f:if condition="{divider}">
        <f:variable
          name="imageClasses"
          value="right-0 object-cover"
        />
        <f:variable name="imageSizeClasses" value="max-w-[50%] w-full h-full" />

        <f:variable
          name="contentClasses"
          value="grid md:grid-cols-2 place-items-center gap-6 md:gap-0 dark"
        />

        <f:variable name="contentColClasses" value="max-md:px-6 pb-6" />

        <f:variable name="backgroundClasses" value="bg-brand-800" />

        <f:variable
          name="backgroundWrapperClass"
          value="absolute top-0 left-0 bottom-0 right-0 -z-10
          {backgroundClasses}"
        />

        <f:if condition="{image}">
          <f:variable
            name="contentColClasses"
            value="max-md:px-6 pb-6 md:pl-[calc(40px-var(--spacing-page))] md:pr-10 lg:pl-0 xl:pr-20"
          />
          <f:variable
            name="backgroundWrapperClass"
            value="{backgroundWrapperClass}
             md:clip-path-separator-half"
          />
        </f:if>
      </f:if>

      <section class="relative w-full page-padding {class}" id="stage">
        <div class="max-w-container bleeding-content md:mx-auto">
          <f:if condition="{image}">
            <dbic:image
              image="{image}"
              class="max-md:hidden absolute top-0 {imageClasses} {imageSizeClasses} -z-20"
              ratio=""
            />
          </f:if>
          <f:if condition="{divider}">
            <div class="{backgroundWrapperClass}"></div>
          </f:if>
          <div class="w-full h-full">
            <div class="{contentClasses} md:min-h-[450px]">
              <f:if condition="{image}">
                <dbic:image
                  image="{image}"
                  class="md:hidden aspect-4/3 object-cover w-full"
                  ratio=""
                />
              </f:if>
              <div
                class="flex flex-col {contentColClasses} {backgroundClasses} bg-transparent prose-headings:text-3xl prose-headings:lg:text-6xl md:pt-6 gap-6"
              >
                <fc:slot />
              </div>
            </div>
          </div>
        </div>
      </section>
    </fc:renderer>
  </fc:component>
</html>
