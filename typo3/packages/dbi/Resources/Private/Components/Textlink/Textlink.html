<html
  xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
  xmlns:fc="http://typo3.org/ns/SMS/FluidComponents/ViewHelpers"
  xmlns:v="http://typo3.org/ns/FluidTYPO3/Vhs/ViewHelpers"
  xmlns:dbic="http://typo3.org/ns/Mogic/Dbi/Components"
  data-namespace-typo3-fluid="true"
  >
  <fc:component>
    <fc:param name="link" type="Typolink" />
    <fc:param name="icon" type="string" optional="1" default="" />
    <fc:param name="theme" type="string" optional="1" default="" />

    <fc:renderer>
      <v:variable.set name="linkTitle" value="{link.title}" />
      <f:if condition="{content}">
          <v:variable.set name="linkTitle" value="{content}" />
      </f:if>

      <v:variable.set name="themeClass" value="" />
      <v:variable.set
        name="iconClass"
        value="text-brand-400
          group-hover:text-brand-400 dark:group-hover:text-current
          group-focus:text-brand-400 dark:group-focus:text-current
          group-active:text-brand-400 dark:group-active:text-current"
        />
      <f:switch expression="{theme}">
        <f:case value="tag_style">
          <v:variable.set
            name="themeClass"
            value="border-0 py-2 px-4 bg-gray-100 rounded text-gray-800"
          />
        </f:case>
        <f:case value="gray">
          <v:variable.set
            name="themeClass"
            value="text-gray-500 active:text-gray-400"
            />
          <v:variable.set
            name="iconClass"
            value="text-current"
            />
        </f:case>
        <f:defaultCase>
          <v:variable.set
            name="themeClass"
            value="text-brand-400 dark:text-white
              hover:text-brand-400 dark:hover:text-brand-500 hover:underline hover:bg-brand-100
              focus:text-brand-400 dark:focus:text-white focus:underline
              active:text-brand-400 dark:active:text-brand-500"
          />
        </f:defaultCase>
      </f:switch>

      <a href="{link.uri}"
        target="{link.target}"
        title="{link.title}"
        class="group p-1 flex items-center justify-center gap-2 rounded-sm border-2 border-transparent transition-all duration-200
          focus:border-brand-400
          active:border-transparent
          {themeClass} {link.class} {class}">
          <f:if condition="{icon}">
            <dbic:icon name="{icon}" class="w-[1.25em] h-[1.25em] {iconClass}" />
          </f:if>
          {linkTitle}
      </a>
    </fc:renderer>
  </fc:component>
</html>
