<html
  xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
  xmlns:fc="http://typo3.org/ns/SMS/FluidComponents/ViewHelpers"
  xmlns:dbic="http://typo3.org/ns/Mogic/Dbi/Components"
  xmlns:dbi="http://typo3.org/ns/Mogic/Dbi/ViewHelpers"
  data-namespace-typo3-fluid="true"
  xmlns:x-on="http://example.org/dummy-ns"
  xmlns:x-show="http://example.org/dummy-ns"
>
  <fc:component>
    <fc:param name="type" type="string" optional="error" />
    <fc:param name="title" type="string" optional="1" />
    <fc:param name="closeable" type="boolean" optional="1" default="0" />
    <fc:param name="cta" type="Array" optional="1" />

    <fc:renderer>
      <f:variable name="contentThemeClass" value="" />

      <f:switch expression="{type}">
        <f:case value="success">
          <f:variable name="themeClass" value="bg-green-200 text-green-700" />
          <f:variable name="iconName" value="check-circle" />
          <f:variable
            name="buttonThemeClass"
            value="text-white
           bg-green-600 hover:bg-green-800 focus:bg-green-600"
          />
        </f:case>
        <f:case value="info">
          <f:variable
            name="themeClass"
            value="bg-electric-brand-100 text-brand-500"
          />
          <f:variable name="iconName" value="information-circle" />
          <f:variable
            name="buttonThemeClass"
            value="text-white
           bg-brand-400 hover:bg-brand-800 focus:bg-brand-500"
          />
          <f:variable name="contentThemeClass" value="text-brand-950" />
        </f:case>
        <f:case value="warning">
          <f:variable name="themeClass" value="bg-yellow-100 text-yellow-700" />
          <f:variable name="iconName" value="warning" />
          <f:variable
            name="buttonThemeClass"
            value="text-white
           bg-yellow-700 hover:bg-yellow-800 focus:bg-yellow-700"
          />
        </f:case>
        <f:defaultCase>
          <f:variable name="themeClass" value="bg-red-100 text-red-600" />
          <f:variable name="iconName" value="circle-x" />
          <f:variable
            name="buttonThemeClass"
            value="text-white
           bg-red-500 hover:bg-red-800 focus:bg-red-500"
          />
        </f:defaultCase>
      </f:switch>

      <div
        x-data="{show:true}"
        x-show="show"
        class="group/alert {themeClass} px-4 py-6 flex flex-col gap-2 overflow-hidden {class}"
      >
        <div class="flex items-center justify-between gap-3">
          <dbic:icon name="{iconName}" class="size-8 flex-none" />
          <f:if condition="{title}">
            <f:then>
              <span class="grow font-medium text-xl">{title}</span>
            </f:then>
            <f:else>
              <div class="grow">
                <fc:slot />
              </div>
              <f:if condition="{cta}">
                <dbic:button
                  link="{cta.link}"
                  theme="base"
                  class="{buttonThemeClass} hidden md:block shrink-0 max-w-[25%] text-ellipsis whitespace-nowrap overflow-hidden"
                >
                  <f:if condition="{cta.title}"> {cta.title} </f:if>
                </dbic:button>
              </f:if>
            </f:else>
          </f:if>
          <f:if condition="{closeable}">
            <div
              x-on:click="show = false;$dispatch('hidealert')"
              class="flex-none cursor-pointer"
            >
              <dbic:icon
                name="x"
                class="size-6 cursor-pointer text-brand-950"
              />
            </div>
          </f:if>
        </div>

        <f:if condition="{title}">
          <p class="{contentThemeClass}">
            <fc:slot />
          </p>
        </f:if>

        <f:if condition="{cta}">
          <dbic:button
            link="{cta.link}"
            theme="base"
            class="text-ellipsis overflow-hidden whitespace-nowrap block max-w-[90%] {buttonThemeClass} {f:if(condition: title, then: '', else: 'md:hidden ml-11')}"
          >
            <f:if condition="{cta.title}"> {cta.title} </f:if>
          </dbic:button>
        </f:if>
      </div>
    </fc:renderer>
  </fc:component>
</html>
