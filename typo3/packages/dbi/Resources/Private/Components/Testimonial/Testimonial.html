<html
  xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
  xmlns:fc="http://typo3.org/ns/SMS/FluidComponents/ViewHelpers"
  xmlns:dbic="http://typo3.org/ns/Mogic/Dbi/Components"
  data-namespace-typo3-fluid="true"
  >

  <fc:component>
      <fc:param name="personName" type="string" />
      <fc:param name="title" type="string" optional="1" />
      <fc:param name="avatar" type="Image" optional="1" />
      <fc:param name="theme" type="string" optional="1" default="default" />

      <fc:renderer>
        <f:switch expression="{theme}">
          <f:case value="light-gray">
            <f:variable name="themeClasses" value="bg-gray-50" />
          </f:case>
          <f:case value="white">
            <f:variable name="themeClasses" value="bg-white" />
          </f:case>
        </f:switch>
        <div class="group/testimonial text-center p-6 {themeClasses} {class} group-[.stretched]/grid:h-full group-[.stretched]/carousel-item:h-full">
          <div class="group-[.dark]/testimonial:text-white text-brand-950 flex flex-col items-center">
            <dbic:avatar image="{avatar}" personName="{personName}" class="mb-2" />
            <span class="mb-3">{personName}</span>
            <fc:slot />
          </div>
        </div>
      </fc:renderer>
  </fc:component>
</html>
