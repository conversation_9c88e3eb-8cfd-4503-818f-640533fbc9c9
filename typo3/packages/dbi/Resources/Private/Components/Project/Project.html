<html xmlns="http://www.w3.org/1999/xhtml" lang="en"
      xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers"
      xmlns:v="http://typo3.org/ns/FluidTYPO3/Vhs/ViewHelpers"
      xmlns:fc="http://typo3.org/ns/SMS/FluidComponents/ViewHelpers"
      xmlns:dbic="http://typo3.org/ns/Mogic/Dbi/Components"
      data-namespace-typo3-fluid="true"
>
<fc:component>
  <fc:param name="project" type="array" />

  <fc:renderer>
    <div class="@container border-solid border border-gray-300 rounded flex flex-col">
      <f:if condition="{project.media}">
        {v:resource.record.fal(table: 'pages', field: 'media', uid: project.uid)
        -> v:iterator.first()
        -> v:variable.set(name: 'image')}
        <f:if condition="{image}">
          <f:then>
            <div class="relative">
              <f:image treatIdAsReference="1" src="{image.id}" crop="{image.crop}"
                       title="{image.title}" alt="{image.alternative}" class="aspect-image object-cover w-full"/>
              <div class="absolute inset-x-3 bottom-3 rounded-lg">
                <span class="inline-block rounded px-2 py-1 bg-gray-800 opacity-85 text-white text-2xs">© {image.description}</span>
              </div>
            </div>
          </f:then>
        </f:if>
      </f:if>

      <f:if condition="{project.price}">
        <div class="font-medium py-4 px-6 flex flex-wrap items-center gap-3 text-sm border-b border-gray-300">
          <span class="px-2 py-1 text-brand-950 bg-gray-100 text-xs">Kaufpreise</span>
          <span>{project.price}</span>
        </div>
      </f:if>
      <div class="p-6">
        <h2 class="font-medium text-xl text-brand-950">{project.title}</h2>
        <div class="pt-4 pb-6 grid grid-cols-1 @lg:grid-cols-2 gap-4">
          <f:for each="{project.keyfacts}" key="key" as="value">
            <dl class="flex flex-col gap-1">
              <dt class="font-medium text-xs">{key}</dt>
              <dd class="text-sm">{value}</dd>
            </dl>
          </f:for>
        </div>

        <dbic:button
          link="{f:uri.typolink(parameter: project.uid)}"
          theme="secondary"
          class="px-8"
        >
          Projekt ansehen
        </dbic:button>
      </div>
    </div>

  </fc:renderer>
</fc:component>
</html>
