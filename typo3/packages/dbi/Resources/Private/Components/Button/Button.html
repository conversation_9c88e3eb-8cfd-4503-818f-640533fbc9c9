<html
    xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
    xmlns:fc="http://typo3.org/ns/SMS/FluidComponents/ViewHelpers"
    xmlns:dbic="http://typo3.org/ns/Mogic/Dbi/Components"
    data-namespace-typo3-fluid="true"
    >
    <fc:component>
        <fc:param name="type" type="string" optional="1" default="anchor" />
        <fc:param name="theme" type="string" optional="1" default="primary" />
        <fc:param name="link" type="Typolink" optional="1" />
        <fc:param name="icon" type="string" optional="1" default="" />
        <fc:param name="additionalAttributes" type="Array" optional="1" />
        <fc:param name="buttonSize" type="string" optional="1" default="base" />

        <fc:renderer>
            <f:variable name="themeClass" value="" />
            <f:switch expression="{theme}">
                <f:case value="base">
                    <f:variable name="themeClass" value="" />
                </f:case>
                <f:case value="primary">
                    <f:variable name="themeClass" value="
                        bg-brand-400 text-white
                        hover:bg-brand-500
                        focus:bg-brand-400
                        active:bg-brand-600 active:border-brand-600"
                    />
                </f:case>
                <f:case value="primary-dark">
                    <f:variable name="themeClass" value="
                        text-white bg-brand-400
                        hover:bg-brand-500
                        focus:text-white focus:bg-brand-400 focus:border-white
                        active:bg-brand-600"
                    />
                </f:case>
                <f:case value="secondary">
                    <f:variable name="themeClass" value="
                        text-brand-400 dark:text-brand-200 border-brand-500 dark:border-brand-200
                        hover:bg-brand-100 dark:hover:bg-brand-800 hover:border-brand-100
                        hover:border-brand-500 hover:text-brand-400
                        active:bg-brand-200 active:text-brand-500"
                    />
                </f:case>
                <f:case value="secondary-dark">
                    <f:variable name="themeClass" value="
                        text-brand-200 border-brand-200
                        hover:bg-brand-800
                        focus:bg-brand-800 focus:border-white
                        active:bg-brand-800 active:border-brand-800"
                    />
                </f:case>
                <f:case value="mobile-header-button">
                    <f:variable name="themeClass" value=""
                    />
                </f:case>
                <f:defaultCase>
                    <f:variable name="themeClass" value="
                        bg-brand-500 dark:bg-white
                        text-white dark:text-brand-500
                        dark:hover:text-white dark:hover:border-brand-800
                        dark:focus:text-white dark:focus:bg-brand-800 dark:focus:border-brand-800
                        active:bg-brand-800 active:border-brand-800"
                    />
                </f:defaultCase>
            </f:switch>

            <f:variable name="buttonPadding" value="p-3 md:px-6"/>
            <f:if condition="{buttonSize} == 'sm'">
                <f:variable name="buttonPadding" value="p-3 md:px-4"/>
            </f:if>
            <f:if condition="{buttonSize} == 'no-padding'">
                <f:variable name="buttonPadding" value=""/>
            </f:if>

            <f:if condition="{type} == 'anchor' AND !!{link}">
                <f:then>
                    <f:link.typolink
                        parameter="{link}"
                        target="{link.target}"
                        title="{link.title}"
                        additionalAttributes="{additionalAttributes}"
                        class="group/button button {buttonPadding} {themeClass} {link.class} {class}"
                    >
                        <f:if condition="!!{icon}">
                            <dbic:icon name="{icon}" />
                        </f:if>
                        <f:if condition="{content}">
                            <f:then>
                              <fc:slot />
                            </f:then>
                            <f:else>
                              {link.title}
                            </f:else>
                        </f:if>
                    </f:link.typolink>
                </f:then>
                <f:else>
                    <f:form.button
                        type="{f:if(condition: '{type} == \'anchor\'', then: 'button', else: type)}"
                        additionalAttributes="{additionalAttributes}"
                        class="group/button button {buttonPadding} {themeClass} {class}"
                    >
                        <f:if condition="!!{icon}">
                            <dbic:icon name="{icon}" />
                        </f:if>
                        <fc:slot />
                    </f:form.button>
                </f:else>
            </f:if>
        </fc:renderer>
    </fc:component>
</html>
