<html
    xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
    xmlns:fc="http://typo3.org/ns/SMS/FluidComponents/ViewHelpers"
    xmlns:dbic="http://typo3.org/ns/Mogic/Dbi/Components"
    data-namespace-typo3-fluid="true"
    >
    <fc:component>
        <fc:param name="links" type="array" />
        <fc:param name="hideLabelOnSmallScreens" type="boolean" optional="true" default="0" />

        <f:variable name="labelClasses" value="" />
        <f:if condition="{hideLabelOnSmallScreens}">
            <f:variable name="labelClasses" value="hidden lg:inline" />
        </f:if>
        <fc:renderer>
            <div class="{class}" role="group">
                <f:for each="{links}" as="link" iteration="iterator">
                    <dbic:button
                        type="{link.type}"
                        link="{link.link}"
                        icon="{link.icon}"
                        additionalAttributes="{link.additionalAttributes}"
                        class="{link.class}">
                        <span class="{labelClasses}">
                            {link.title}
                        </span>
                    </dbic:button>
                </f:for>
            </div>
        </fc:renderer>
    </fc:component>
</html>
