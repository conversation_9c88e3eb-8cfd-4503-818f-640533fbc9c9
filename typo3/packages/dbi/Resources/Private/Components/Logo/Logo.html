<?xml version="1.0" encoding="utf-8"?>
<html
  data-namespace-typo3-fluid="true"
  xmlns="http://www.w3.org/1999/xhtml"
  xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers"
  xmlns:x-bind="http://example.org/dummy-ns"
  xmlns:fc="http://typo3.org/ns/SMS/FluidComponents/ViewHelpers"
>

  <fc:component>
    <fc:param name="hide-logomark" type="Boolean" optional="1" default="0" />
    <fc:param name="hide-wordmark" type="Boolean" optional="1" default="0" />
    <fc:param name="hide-wordmark-large-screen" type="Boolean" optional="1" default="0" />

    <fc:renderer>

      <f:variable name="wordmarkClass" value="" />
      <f:if condition="{hide-wordmark}">
        <f:variable name="wordmarkClass" value="hidden" />
      </f:if>
      <f:if condition="{hide-wordmark-large-screen}">
        <f:variable name="wordmarkClass" value="{wordmarkClass} hidden" />
      </f:if>

      <f:variable name="logomarkClass" value="" />
      <f:if condition="{hide-logomark}">
        <f:variable name="logomarkClass" value="hidden" />
      </f:if>
      <svg
        class="{logomarkClass} {class}"
        viewBox="0 0 64 64"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        aria-hidden="true"
      >
        <path
          d="M8.961 8.957v46.084h46.08V8.957H8.961zM0 0h64v64H0V0zm14.08 48.64L38.4 15.363h11.52L25.6 48.642H14.08z"
          fill="currentColor"
        ></path>
      </svg>
      <span
        class="{wordmarkClass} {class}"
      >
        Deutsche Bank<br />
        Immobilien
      </span>
    </fc:renderer>
  </fc:component>
</html>
