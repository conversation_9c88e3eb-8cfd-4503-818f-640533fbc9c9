<html
    xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
    xmlns:fc="http://typo3.org/ns/SMS/FluidComponents/ViewHelpers"
    xmlns:dbic="http://typo3.org/ns/Mogic/Dbi/Components"
    xmlns:x-bind="http://example.org/dummy-ns"
    xmlns:x-on="http://example.org/dummy-ns"
    data-namespace-typo3-fluid="true"
    >
    <fc:component>
        <fc:param name="items" type="Array" optional="1" />
        <fc:param name="itemsPerSlide" type="string" optional="1" default="2 lg:3" />
        <fc:param name="type" type="string" optional="1" />

        <f:variable
            name="navigationClasses"
            value="group lg:absolute lg:top-[calc(50%-2.75rem)]
                flex items-center justify-center p-2.5 rounded-xs
                hover:bg-brand-100 dark:hover:bg-electric-brand-200 disabled:bg-transparent dark:disabled:bg-transparent"
        />

        <f:variable name="navigationIconClass"
            value="size-8 text-brand-400 dark:text-white
            group-hover:text-brand-950 dark:group-hover:text-brand-400
            group-disabled:text-gray-600 group-disabled:hover:bg-transparent
            dark:group-disabled:text-gray-500"/>

        <f:switch expression="{itemsPerSlide}">
            <f:case value="1">
                <f:variable name="itemClasses" value="basis-full" />
                <f:variable name="additionalIndicatorClasses" value="" />
            </f:case>
            <f:case value="2">
                <f:variable name="itemClasses" value="basis-full md:basis-1/2" />
                <f:variable name="additionalIndicatorClasses" value="md:even:hidden" />
            </f:case>
            <f:case value="3">
                <f:variable name="itemClasses" value="basis-full md:basis-1/3" />
                <f:variable name="additionalIndicatorClasses" value="md:nth-[3n-1]:hidden md:nth-[3n]:hidden" />
            </f:case>
            <f:case value="1 lg:2">
                <f:variable name="itemClasses" value="basis-full lg:basis-1/2" />
                <f:variable name="additionalIndicatorClasses" value="lg:even:hidden" />
            </f:case>
            <f:case value="1 lg:3">
                <f:variable name="itemClasses" value="basis-full lg:basis-1/3" />
                <f:variable name="additionalIndicatorClasses" value="lg:nth-[3n-1]:hidden lg:nth-[3n]:hidden" />
            </f:case>
            <f:defaultCase>
                <f:variable name="itemClasses" value="basis-full md:basis-1/2 lg:basis-1/3" />
                <f:variable name="additionalIndicatorClasses" value="md:even:hidden lg:nth-[3n-1]:hidden lg:nth-[3n]:hidden lg:nth-[3n+1]:block" />
            </f:defaultCase>
        </f:switch>

      <f:switch expression="{type}">
        <f:case value="project">
          <f:variable name="carouselMarginClasses" value="-mx-2 lg:-mx-3" />
          <f:variable name="paddingGapClasses" value="px-2 lg:px-3" />
        </f:case>
        <f:defaultCase>
          <f:variable name="carouselMarginClasses" value="md:-mx-4" />
          <f:variable name="paddingGapClasses" value="px-3 md:px-4" />
        </f:defaultCase>
      </f:switch>

        <fc:renderer>
            <div class="relative w-full flex flex-wrap items-center justify-center gap-6 lg:gap-8 lg:px-[72px] {class}" x-data="carousel()">
                <div class="relative w-full">
                    <div
                        x-ref="carousel"
                        class="flex overflow-x-auto scrollbar-hidden focus:outline-2 focus:outline-brand-400 {carouselMarginClasses}"
                        style="scroll-snap-type: x mandatory"
                        x-on:scroll.debounce.100="scrollHandler"
                        tabindex="0"
                        role="region"
                        aria-label="Karussell"
                    >
                      <f:for each="{items}" as="item" iteration="iterator">
                        <div class="flex-none snap-start {paddingGapClasses} {itemClasses} group/carousel-item stretched *:h-full" aria-atomic="false" style="overflow-wrap: anywhere;">
                          <f:switch expression="{type}">
                            <f:case value="project">
                              <dbic:project project="{item}"/>
                            </f:case>
                            <f:defaultCase>
                              <f:cObject typoscriptObjectPath="lib.tx_mask.content">
                                {item.uid}
                              </f:cObject>
                            </f:defaultCase>
                          </f:switch>
                        </div>
                      </f:for>
                    </div>
                </div>

                <button
                    type="button"
                    x-show="indicatorLength > 1"
                    class="{navigationClasses} lg:left-0"
                    x-on:click="mainActiveIndex = (indicatorActiveIndex - 1) * itemsPerSlide"
                    x-bind:disabled="!hasPrev"
                    >
                    <span class="sr-only">Vorheriges Elementt</span>
                    <dbic:icon name="chevron-left" class="{navigationIconClass}" />
                </button>

                <div x-show="indicatorLength > 1" x-ref="indicators" class="flex justify-center flex-wrap gap-3 items-center lg:grow max-w-[calc(100%-9rem)] md:max-w-none">
                    <f:for each="{items}" as="item" iteration="iterator">
                        <div
                            role="button"
                            tabindex="-1"
                            class="size-3 rounded-full cursor-pointer hover:bg-brand-600 dark:hover:bg-electric-brand-200 {additionalIndicatorClasses}"
                            x-bind:class="mainActiveIndex == {iterator.index} ? 'bg-brand-400 dark:bg-white' : 'bg-gray-500'"
                            x-on:click="mainActiveIndex = {iterator.index}"
                            x-bind:aria-current="mainActiveIndex == {iterator.index} ? 'true' : 'false'"
                            aria-label="Zur Folie {iterator.index + 1}"
                            >
                        </div>
                    </f:for>
                </div>

                <button
                    type="button"
                    x-show="indicatorLength > 1"
                    class="{navigationClasses} lg:right-0"
                    x-on:click="mainActiveIndex = (indicatorActiveIndex + 1) * itemsPerSlide"
                    x-bind:disabled="!hasNext"
                    >
                    <span class="sr-only">Nächstes Element</span>
                    <dbic:icon name="chevron-right" class="{navigationIconClass}" />
                </button>
            </div>
        </fc:renderer>
    </fc:component>
</html>
