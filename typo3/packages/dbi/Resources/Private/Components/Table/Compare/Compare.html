<html
  xmlns="http://www.w3.org/1999/xhtml"
  lang="en"
  xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
  xmlns:v="http://typo3.org/ns/FluidTYPO3/Vhs/ViewHelpers"
  xmlns:fc="http://typo3.org/ns/SMS/FluidComponents/ViewHelpers"
  xmlns:dbi="http://typo3.org/ns/Mogic/Dbi/ViewHelpers"
  xmlns:dbic="http://typo3.org/ns/Mogic/Dbi/Components"
  xmlns:x-bind="http://example.org/dummy-ns"
  xmlns:x-on="http://example.org/dummy-ns"
  data-namespace-typo3-fluid="true"
>
  <fc:component>
    <fc:param name="type" type="string" optional="1" default="default" />
    <fc:param name="data" type="string" />
    <fc:param name="exposePageUid" type="string" optional="1" />
    <f:variable name="bgColorClass" value="bg-white group-[.bg-gray-50]/light-gray:bg-gray-50 group-[.bg-brand-50]/light-brand:bg-brand-50 dark:bg-brand-700" />

    <fc:renderer>
      {dbi:compareTable(table: data) -> v:variable.set(name: 'compareTable')}

      <f:variable name="numberOfColumns" value="{compareTable.0 -> f:count()}" />
      <f:variable name="navButtonTopClass" value="top-20 -translate-y-1/2 text-brand-400 dark:text-white
            hover:text-brand-950 dark:hover:text-brand-400 hover:bg-brand-100 dark:hover:bg-electric-brand-200
            disabled:text-gray-500" />
      <f:variable name="tableMarginClass" value="ml-0 md:ml-[15px]" />
      <f:if condition="{numberOfColumns} > 4">
        <f:then>
          <f:variable name="navButtonTopClass" value="{navButtonTopClass}" />
        </f:then>
        <f:else>
          <f:variable name="tableMarginClass" value="ml-0 md:ml-[15px] lg:ml-0" />
          <f:variable name="navButtonTopClass" value="lg:hidden {navButtonTopClass}" />
        </f:else>
      </f:if>
      <div
        class="relative max-w-full overflow-hidden {bgColorClass}"
        x-data="compareTable()"
        x-cloak=""
      >
        <!-- Navigation buttons -->
        <button
          x-show="isScrollable"
          class="text-brand-950 disabled:hidden {navButtonTopClass} left-0 md:left-[12rem] absolute rounded-sm z-20"
          x-bind:disabled="!hasPrev"
          x-on:click.prevent="currentPage--"
          aria-label="Vorherige Seite"
        >
          <dbic:icon name="chevron-left" class="w-10 h-10" />
        </button>
        <span x-ref="spaceBetweenColumns" class="hidden md:block absolute top-0 left-0 {bgColorClass} h-full"></span>
        <table
          class="hidden md:block w-auto absolute top-0 left-0 border-separate [border-spacing:0_16px] table-auto z-20 {bgColorClass}"
          role="table"
          aria-hidden="true"
          x-ref="firstColumnOverlapTable"
        >
          <thead>
            <f:for each="{compareTable}" as="rowData" iteration="iterator">
              <f:variable name="headerRowClass" value="bg-white"/>
              <f:if condition="{iterator.isFirst}">
                <f:variable name="headerRowClass" value="group/light-brand:bg-brand-50 group/light-gray:bg-gray-50 dark:bg-brand-700"/>
              </f:if>
              <tr class="{bgColorClass}">
                <f:for each="{rowData}" as="cell" iteration="colIterator">
                  <f:if condition="{colIterator.isFirst}">
                    <th
                      class="py-2 px-2.5 text-right align-middle text-brand-950 font-normal sticky left-0 z-10 {headerRowClass}"
                      scope="row"
                    >
                      {cell.value}
                    </th>
                  </f:if>
                </f:for>
              </tr>
            </f:for>
          </thead>
        </table>

        <!-- Accessible table with horizontal scroll -->
        <div class="overflow-hidden max-w-full {bgColorClass}" x-ref="list">
          <table
            class="border-separate [border-spacing:0_16px] table-auto {tableMarginClass}"
            role="table"
            aria-label="Immobilienvergleichstabelle"
            style="width: 100%;"
            >
            <thead>
              <f:for each="{compareTable}" as="rowData" iteration="rowIterator">
              <f:if condition="{rowIterator.isFirst}">
                <f:then>
                  <!-- Header row -->
                    <tr class="{bgColorClass}">
                      <f:for each="{rowData}" as="cell" iteration="colIterator">
                        <f:if condition="{colIterator.isFirst}">
                          <f:then>
                            <!-- Empty corner cell -->
                            <th
                              class="hidden md:block p-4 text-left font-medium sticky left-0 z-10 {bgColorClass}"
                              scope="col"
                              id="col-{colIterator.index}"
                            >
                            </th>
                          </f:then>
                          <f:else>
                            <th
                              class="p-4 text-center text-lg font-medium  max-w-0 overflow-hidden {bgColorClass}"
                              scope="col"
                              id="col-{colIterator.index}"
                            >
                              <f:if condition="{cell.type} == 'expose'">
                                <f:then>
                                  <div class="flex flex-col gap-2 items-center">
                                    <span aria-hidden="true" class="text-brand-950 dark:text-brand-200">WE {cell.value}</span>
                                    <f:variable name="exposeId" value="FID-{cell.value}" />
                                    <f:variable name="exposePageLink">
                                      <f:uri.page
                                        pageUid="{exposePageUid}"
                                        additionalParams="{
                                        tx_dbi_fioimmoproxyexpose: {
                                          expose_id: exposeId
                                        }
                                      }"
                                      />
                                    </f:variable>
                                    <dbic:button
                                      link="{exposePageLink}"
                                      theme="secondary"
                                      additionalAttributes="{'aria-label': 'Expose {cell.value}'}"
                                    >
                                      Expose
                                    </dbic:button>
                                  </div>
                                </f:then>
                                <f:else>
                                  <div class="text-brand-950 dark:text-brand-200">
                                    {cell.value}
                                  </div>
                                </f:else>
                              </f:if>
                            </th>
                          </f:else>
                        </f:if>
                      </f:for>
                    </tr>
                  </f:then>
                </f:if>
              </f:for>
            </thead>
            <tbody>
            <f:for each="{compareTable}" as="rowData" iteration="rowIterator">
              <f:if condition="!{rowIterator.isFirst}">
                  <!-- Data rows -->
                  <tr class="{bgColorClass}">
                    <f:variable name="rowHeader" value="{rowData.0.value}"/>
                    <f:for each="{rowData}" as="cell" iteration="colIterator">
                      <f:if condition="{colIterator.isFirst}">
                        <f:then>
                          <!-- Row header (feature name) -->
                          <th
                            class="hidden md:block text-transparent py-2 px-2.5 text-right align-middle font-medium sticky left-0 z-10 {bgColorClass}"
                            scope="row"
                            id="row-{rowIterator.index}"
                          >
                            {cell.value}
                          </th>
                        </f:then>
                        <f:else>
                          <f:variable name="borderClass" value="border-l border-t border-b border-gray-200" />
                          <f:if condition="{colIterator.isLast}">
                            <f:variable name="borderClass" value="border border-gray-200" />
                          </f:if>
                          <!-- Data cell -->
                          <td headers="col-{colIterator.index} row-{rowIterator.index}" class="box-border py-2 px-2.5 text-brand-950 text-center align-middle {borderClass} bg-white max-w-0 overflow-hidden">
                            <span class="sr-only md:hidden">{rowHeader} {cell.value}</span>
                            <p aria-hidden="true" class="block md:hidden text-sm mb-2">{rowHeader}</p>
                            <p class="text-base">
                            <f:if condition="{cell.value} == ''">
                              <f:then>
                                <span aria-hidden="true">-</span>
                                <span class="sr-only">Kein Wert angegeben</span>
                              </f:then>
                              <f:else>
                                <span>{cell.value}</span>
                              </f:else>
                            </f:if>
                            </p>
                          </td>
                        </f:else>
                      </f:if>
                    </f:for>
                  </tr>
              </f:if>
            </f:for>
            </tbody>
          </table>
        </div>
        <button
          x-show="isScrollable"
          class=" text-brand-950 disabled:hidden {navButtonTopClass} right-0 absolute rounded-sm z-20"
          x-bind:disabled="!hasNext"
          x-on:click.prevent="currentPage++"
          aria-label="Nächste Seite"
        >
          <dbic:icon name="chevron-right" class="w-10 h-10" />
        </button>
      </div>
    </fc:renderer>
  </fc:component>
</html>
