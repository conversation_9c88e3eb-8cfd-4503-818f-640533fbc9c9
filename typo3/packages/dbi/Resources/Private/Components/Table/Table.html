<html
  xmlns="http://www.w3.org/1999/xhtml"
  lang="en"
  xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
  xmlns:v="http://typo3.org/ns/FluidTYPO3/Vhs/ViewHelpers"
  xmlns:fc="http://typo3.org/ns/SMS/FluidComponents/ViewHelpers"
  xmlns:dbi="http://typo3.org/ns/Mogic/Dbi/ViewHelpers"
  xmlns:dbic="http://typo3.org/ns/Mogic/Dbi/Components"
  xmlns:x-bind="http://example.org/dummy-ns"
  xmlns:x-on="http://example.org/dummy-ns"
  xmlns:x-data="http://example.org/dummy-ns"
  xmlns:x-for="http://example.org/dummy-ns"
  xmlns:x-text="http://example.org/dummy-ns"
  data-namespace-typo3-fluid="true"
>
  <fc:component>
    <fc:param name="data" type="string" />

    <fc:renderer>
      {dbi:table(table: data) -> v:variable.set(name: 'tableData')}

      <table
        x-data="table({tableData -> v:format.json.encode()})"
        role="table"
        x-bind="tableBinding"
        class="grid grid-cols-1 md:gap-y-4 w-full md:overflow-x-auto"
      >
        <thead role="rowgroup" class="hidden md:contents">
          <tr role="row" class="contents">
            <f:for each="{tableData.0}" as="headerData">
              <th
                role="columnheader"
                class="flex flex-col p-0 md:inline md:p-4 md:border md:not-last:border-r-0 font-normal before:content-[attr(data-label)] before:text-xs md:before:content-none bg-gray-100 border-gray-200 text-brand-950"
                scope="col"
              >
                {headerData}
              </th>
            </f:for>
          </tr>
        </thead>

        <tbody role="rowgroup" class="flex flex-col gap-4 md:contents">
          <f:for each="{tableData}" as="rowData" iteration="rowIterator">
            <f:if condition="{rowIterator.isFirst}">
              <f:else>
                <tr
                  role="row"
                  class="flex flex-col gap-3 md:contents bg-white border border-gray-300 p-4"
                >
                  <f:for
                    each="{rowData}"
                    as="cellData"
                    iteration="cellIterator"
                  >
                    <td
                      role="cell"
                      class="flex flex-col justify-center p-0 gap-1 md:p-4 md:border md:not-last:border-r-0 font-normal before:content-[attr(data-label)] before:text-xs md:before:content-none border-gray-200 md:wrap-break-word"
                      data-label="{tableData.0.{cellIterator.index}}"
                    >
                      {cellData}
                    </td>
                  </f:for>
                </tr>
              </f:else>
            </f:if>
          </f:for>
        </tbody>
      </table>
    </fc:renderer>
  </fc:component>
</html>
