<html
  xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
  xmlns:v="http://typo3.org/ns/TYPO3/CMS/Vhs/ViewHelpers"
  xmlns:fc="http://typo3.org/ns/SMS/FluidComponents/ViewHelpers"
  xmlns:dbic="http://typo3.org/ns/Mogic/Dbi/Components"
  xmlns:x-bind="http://example.org/dummy-ns"
  xmlns:x-on="http://example.org/dummy-ns"
  data-namespace-typo3-fluid="true"
>
  <fc:component>
    <fc:param name="pageCountForce" type="boolean" optional="1" default="0" />
    <fc:param name="innerClasses" type="string" optional="1" default="flex-col md:flex-row gap-3 md:gap-6 xl:gap-8" />
    <fc:param name="navigatorClasses" type="string" optional="1" default="hidden md:block bg-white text-brand-400 hover:text-brand-500 disabled:text-gray-300 *:text-current *:size-5" />
    <fc:param name="additionalAttributes" type="Array" optional="1" default="" />

    <v:variable.set name="defaultAttributes" value="{x-data: 'horizontalScroller({pageCountForce})'}" />
    <fc:renderer>
      <v:tag
        name="div"
        additionalAttributes="{v:iterator.merge(
          a: additionalAttributes,
          b: defaultAttributes
        )}"
        class="absolute top-14 z-99 left-0 bg-white md:top-0 pb-3 md:pb-0 w-full md:w-auto md:relative flex overflow-hidden {class}"

      >
      <button
        type="button"
        x-show="isScrollable"
        class="{navigatorClasses}"
        x-bind:disabled="!hasPrev"
        x-on:click.prevent="currentPage--"
        aria-hidden="true"
        tabindex="-1"
        >
        <dbic:icon name="chevron-left" class="" />
      </button>
      <ul id="navigation-menu" role="menu" x-ref="scroller" style="scroll-snap-type: x mandatory"
        class="relative bg-white flex w-full overflow-x-auto scrollbar-hidden *:relative *:snap-start {innerClasses}"
        x-bind:class="{
          'md:mx-3' : !isScrollable
        }"
        aria-hidden="true"
        tabindex="-1"
      >
        <fc:slot />
      </ul>
      <button
        type="button"
        x-show="isScrollable"
        x-ref="navigator"
        class="{navigatorClasses}"
        x-bind:disabled="!hasNext"
        x-on:click.prevent="currentPage++"
        aria-hidden="true"
        tabindex="-1"
      >
        <dbic:icon name="chevron-right" class="" />
      </button>
      </v:tag>
    </fc:renderer>
  </fc:component>
</html>
