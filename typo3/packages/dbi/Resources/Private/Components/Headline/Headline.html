<html
  xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
  xmlns:fc="http://typo3.org/ns/SMS/FluidComponents/ViewHelpers"
  xmlns:v="http://typo3.org/ns/FluidTYPO3/Vhs/ViewHelpers"
  xmlns:dbi="http://typo3.org/ns/Mogic/Dbi/ViewHelpers"
  data-namespace-typo3-fluid="true"
>
  <fc:component>
    <fc:param name="headline" type="string" />
    <fc:param name="layout" type="string" />
    <fc:param name="subline" type="string" optional="1" />
    <fc:param name="overline" type="string" optional="1" />

    <fc:renderer>
      <f:variable name="sublineClasses" value="text-lg mt-3" />
      <f:variable name="overlineClasses" value="mb-2" />
      <f:variable name="headlineTypeClass" value="font-medium" />
      <f:switch expression="{layout}">
        <f:case value="101">
          <f:variable name="headlineTypeTag" value="h1" />
          <f:variable name="headlineTypeClass" value="{headlineTypeClass} heading-1" />
        </f:case>
        <f:case value="102">
          <f:variable name="headlineTypeTag" value="h1" />
          <f:variable name="headlineTypeClass" value="{headlineTypeClass} heading-2" />
        </f:case>
        <f:case value="103">
          <f:variable name="headlineTypeTag" value="h2" />
          <f:variable name="headlineTypeClass" value="{headlineTypeClass} heading-2" />
        </f:case>
        <f:case value="104">
          <f:variable name="headlineTypeTag" value="h3" />
          <f:variable name="headlineTypeClass" value="{headlineTypeClass} heading-3" />
        </f:case>
        <f:case value="105">
          <f:variable name="headlineTypeTag" value="h4" />
          <f:variable name="headlineTypeClass" value="{headlineTypeClass} heading-4" />
        </f:case>
        <f:case value="106">
          <f:variable name="headlineTypeTag" value="h5" />
          <f:variable name="headlineTypeClass" value="{headlineTypeClass} heading-5" />
        </f:case>
        <f:case value="107">
          <f:variable name="headlineTypeTag" value="h6" />
          <f:variable name="headlineTypeClass" value="heading-6 group-[.grid]/personBox:font-medium" />
        </f:case>
        <f:case value="card">
          <f:variable name="headlineTypeTag" value="span" />
          <f:variable
            name="headlineTypeClass"
            value="text-xl text-brand-950 font-medium"
          />
          <f:variable
            name="sublineClasses"
            value="text-sm mt-2 text-brand-950"
          />
          <f:variable
            name="overlineClasses"
            value="text-sm mb-2 uppercase font-normal text-brand-950"
          />
        </f:case>
        <f:defaultCase>
          <f:variable name="headlineTypeTag" value="span" />
          <f:variable name="class" value="{class} hidden" />
        </f:defaultCase>
      </f:switch>

      <div class="group/header flex flex-col {class}">
        <f:if condition="{overline}">
          <span
            class="block font-normal group-[.dark]/snippet:text-brand-200 {overlineClasses}"
            >{overline}</span
          >
        </f:if>
        <dbi:dynamicTag
          as="{headlineTypeTag}"
          class="{headlineTypeClass} group-[.dark]/header:text-white"
        >
          <f:format.raw>
            {v:format.pregReplace( pattern: "/break\;/", replacement: "<br />",
            subject: headline )}
          </f:format.raw>
        </dbi:dynamicTag>
        <f:if condition="{subline}">
          <span
            class="mb-6 md:mb-3 group-[.dark]/header:text-white {sublineClasses}"
            >{subline}</span
          >
        </f:if>
      </div>
    </fc:renderer>
  </fc:component>
</html>
