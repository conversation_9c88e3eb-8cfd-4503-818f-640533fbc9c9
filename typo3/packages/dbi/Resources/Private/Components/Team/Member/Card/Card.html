<html xmlns="http://www.w3.org/1999/xhtml" lang="de"
     xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers"
     xmlns:fc="http://typo3.org/ns/SMS/FluidComponents/ViewHelpers"
     xmlns:v="http://typo3.org/ns/FluidTYPO3/Vhs/ViewHelpers"
     xmlns:dbi="http://typo3.org/ns/Mogic/Dbi/ViewHelpers"
     xmlns:dbic="http://typo3.org/ns/Mogic/Dbi/Components"
     data-namespace-typo3-fluid="true"
>
  <fc:component>
    <fc:param name="member" type="Teammember" />
    <fc:param name="hideRatingLink" type="boolean" optional="1" default="0" />
    <fc:param name="showProfileLink" type="boolean" optional="1" default="0" />

    <fc:renderer>
      <div class="@container">
        <div class="flex flex-col gap-7 @2xl:flex-row @2xl:gap-6 group/card w-full text-gray-800 {class}">
          <div class="flex flex-col gap-3 justify-center items-center @2xl:max-w-[125px]">
            <f:variable name="srcset">
              <f:uri.image image="{member.avatar}" width="250c" height="250c-100" cropVariant="teammember"/> 2x,
              <f:uri.image image="{member.avatar}" width="375c" height="375c-100" cropVariant="teammember"/> 3x,
            </f:variable>
            <f:image image="{member.avatar}" width="125c" height="125c-100" cropVariant="teammember"
              class="aspect-square object-contain rounded-full size-[125px]"
              additionalAttributes="{srcset: srcset}"
              />
            <f:if condition="{member.languageSvgSources}">
              <div class="flex flex-wrap justify-center gap-2 items-center mb-4 max-w-[75%]">
                <f:for each="{member.languageSvgSources}" as="languageSource">
                  <span class="size-6 rounded-full" style="contain: paint;" data-language="{languageSource.key}" title="{languageSource.title}">
                    {languageSource.path -> f:cObject(typoscriptObjectPath: 'lib.svgHandler')}
                  </span>
                </f:for>
              </div>
            </f:if>
          </div>
          <div class="flex flex-col gap-5 pr-16 relative 2xl:flex-1">
            <dbic:headline
              headline="{member.fullName}"
              layout="card"
              overline="{member.function}"
              subline="{member.identification}"
              class="text-sm"
            />

            <f:if condition="{member.contactInfos}">
              <div class="grid grid-cols-1 @2xl:grid-cols-2 gap-y-3 gap-x-4">
                <f:for each="{member.contactInfos}" as="contactInfo">
                  <f:switch expression="{contactInfo.type}">
                    <f:case value="email">
                      <f:variable name="link" value="mailto:{contactInfo.info}" />
                      <f:variable name="label" value="E-Mail senden" />
                      <f:variable name="icon" value="mail" />
                    </f:case>
                    <f:case value="telephone">
                      <f:variable name="link" value="{dbi:telUrl(number: contactInfo.info)}" />
                      <f:variable name="label" value="{contactInfo.info}" />
                      <f:variable name="icon" value="phone" />
                    </f:case>
                    <f:case value="mobile">
                      <f:variable name="link" value="{dbi:telUrl(number: contactInfo.info)}" />
                      <f:variable name="label" value="{contactInfo.info}" />
                      <f:variable name="icon" value="device-mobile" />
                    </f:case>
                    <f:case value="fax">
                      <f:variable name="link" value="" />
                      <f:variable name="icon" value="fax" />
                    </f:case>
                  </f:switch>

                  <f:if condition="{link}">
                    <f:then>
                      <a href="{link}" class="inline-flex gap-2 items-center text-sm font-medium">
                        <dbic:icon name="{icon}" class="size-5 text-brand-950" />
                        {label}
                      </a>
                    </f:then>
                    <f:else>
                      <span class="inline-flex gap-0.5 items-center text-sm font-medium">
                        <dbic:icon name="{icon}" class="size-5 text-brand-950" />
                        {contactInfo.info}
                      </span>
                    </f:else>
                  </f:if>
                </f:for>
              </div>
            </f:if>

            <f:if condition="{showProfileLink} AND {member.center.pid}">
              <dbic:button buttonSize="sm" theme="secondary" link="{f:uri.typolink(parameter: member.center.pid)}">Zum Profil</dbic:button>
            </f:if>

            <f:if condition="!{hideRatingLink} AND {member.awardsMaklerempfehlungId} AND !{member.awardsMaklerempfehlungHide}">
              <dbic:button buttonSize="sm" link="https://www.makler-empfehlung.de/immobilienmakler/{member.awardsMaklerempfehlungId} _blank" theme="secondary">
                Bewertung anschauen
              </dbic:button>
            </f:if>

            {member.awards -> v:iterator.slice(length: 3) -> v:variable.set(name: 'awards')}
            <f:if condition="{awards}">
              <div class="absolute top-0 right-0 flex flex-col w-12">
                <f:for each="{awards}" as="awardFileOrFileRef">
                  <dbic:image image="{awardFileOrFileRef}" ratio="" />
                </f:for>
              </div>
            </f:if>
          </div>
        </div>
      </div>
    </fc:renderer>
  </fc:component>
</html>
