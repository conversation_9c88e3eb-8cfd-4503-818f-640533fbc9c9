<html xmlns="http://www.w3.org/1999/xhtml" lang="de"
     xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers"
     xmlns:fc="http://typo3.org/ns/SMS/FluidComponents/ViewHelpers"
     xmlns:dbic="http://typo3.org/ns/Mogic/Dbi/Components"
     data-namespace-typo3-fluid="true"
>
  <fc:component>
    <fc:renderer>
      <div class="@container">
        <div class="flex flex-col gap-7 @2xl:flex-row @2xl:gap-6 group/card w-full text-gray-800 {class}">
          <div class="flex flex-col gap-3 justify-center items-center @2xl:w-[320px]">
            Bild
          </div>
          <div class="flex flex-col gap-5 pr-16 relative">
            <dbic:headline
              headline="Headline"
              layout="card"
              overline="Overline"
            />

            <address>
              <f:comment>Addresse</f:comment>
            </address>

            <f:comment>Button Group</f:comment>

            <div class="absolute top-0 right-0 flex place-content-start  gap-4 flex-wrap items-start w-1/3 md:w-12 lg:w-1/4 justify-end">
              <f:comment>Awards</f:comment>
            </div>
          </div>
        </div>
      </div>
    </fc:renderer>
  </fc:component>
</html>
