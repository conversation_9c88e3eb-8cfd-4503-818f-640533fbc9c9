<html xmlns="http://www.w3.org/1999/xhtml"
    xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
    xmlns:fc="http://typo3.org/ns/SMS/FluidComponents/ViewHelpers"
    xmlns:dbic="http://typo3.org/ns/Mogic/Dbi/Components"
    data-namespace-typo3-fluid="true"
    >

<fc:component>
  <fc:param name="pagination" type="TYPO3\CMS\Core\Pagination\SlidingWindowPagination" />

  <fc:renderer>
    <f:variable name="currentPageNumber">{pagination.paginator.currentPageNumber}</f:variable>
    <ul class="pagination flex justify-center align-center gap-2 my-12">
      <li class="w-9 h-9 flex justify-center items-center mr-2">
        <f:if condition="{pagination.previousPageNumber}">
          <f:then>
            <f:link.page rel="prev" additionalParams="{dbipage: pagination.previousPageNumber}" class="text-brand-600">
              <dbic:icon name="chevron-left" class="size-5" />
            </f:link.page>
          </f:then>
          <f:else>
            <span class="disabled text-gray-300">
              <dbic:icon name="chevron-left" class="size-5" />
            </span>
          </f:else>
        </f:if>
      </li>

      <f:if condition="{pagination.displayRangeStart} > 1">
        <li class="w-9 h-9 flex justify-center items-center text-brand-600">
          <f:link.page additionalParams="{dbipage: 1}">1</f:link.page>
        </li>
      </f:if>

      <f:if condition="{pagination.displayRangeStart} > 2">
        <f:if condition="{pagination.displayRangeStart} == 3">
          <f:then>
            <li class="w-9 h-9 flex justify-center items-center text-brand-600">
              <f:link.page additionalParams="{dbipage: 2}">2</f:link.page>
            </li>
          </f:then>
          <f:else>
            <li class="w-9 h-9 flex justify-center items-center text-brand-600 ">
              ...
            </li>
          </f:else>
        </f:if>
      </f:if>

      <f:for each="{pagination.allPageNumbers}" as="page">
        <li class="w-9 h-9 rounded-full focus:shadow-outline flex justify-center items-center text-brand-600 {f:if(condition:'{currentPageNumber}=={page}',then:'text-white border-brand-600 bg-brand-600')}">
          <f:link.page
            additionalParams="{dbipage: page}"
            class="{f:if(condition:'{currentPageNumber}=={page}',then:'active')}">
            {page}
          </f:link.page>
        </li>
      </f:for>

      <f:if condition="{v:math.subtract(a:pagination.lastPageNumber, b:1)} > {pagination.displayRangeEnd}">
        <f:if condition="{v:math.subtract(a:pagination.lastPageNumber, b:2)} == {pagination.displayRangeEnd}">
          <f:then>
            <f:variable name="preLastPageNumber">{v:math.subtract(a:pagination.lastPageNumber, b:1)}</f:variable>
            <li class="w-9 h-9 flex justify-center items-center text-brand-600">
              <f:link.page additionalParams="{dbipage: preLastPageNumber}">{preLastPageNumber}</f:link.page>
            </li>
          </f:then>
          <f:else>
            <li class="w-9 h-9 flex justify-center items-center text-brand-600">
              ...
            </li>
          </f:else>
        </f:if>
      </f:if>

      <f:if condition="{pagination.lastPageNumber} > {pagination.displayRangeEnd}">
        <li class="w-9 h-9 flex justify-center items-center text-brand-600">
          <f:link.page additionalParams="{dbipage: pagination.lastPageNumber}">{pagination.lastPageNumber}</f:link.page>
        </li>
      </f:if>

      <li class="w-9 h-9 flex justify-center items-center ml-2">
        <f:if condition="{pagination.nextPageNumber}">
          <f:then>
            <f:link.page rel="next" additionalParams="{dbipage: pagination.nextPageNumber}" class="text-brand-600">
              <dbic:icon name="chevron-right" class="size-5" />
            </f:link.page>
          </f:then>
          <f:else>
            <span class="disabled text-gray-300">
              <dbic:icon name="chevron-right" class="size-5" />
            </span>
          </f:else>
        </f:if>
      </li>
    </ul>
  </fc:renderer>
</fc:component>
</html>
