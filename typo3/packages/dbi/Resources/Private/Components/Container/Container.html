<html
  xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
  xmlns:fc="http://typo3.org/ns/SMS/FluidComponents/ViewHelpers"
  xmlns:dbic="http://typo3.org/ns/Mogic/Dbi/Components"
  data-namespace-typo3-fluid="true"
  >
  <fc:component>
    <fc:param name="inset" type="boolean"  default="0" optional="1" />
    <fc:param name="divider" type="boolean" default="0" optional="1" />
    <fc:param name="intoPrevContainer" type="boolean" default="0" optional="1" />
    <fc:param name="gridTypeClass" type="string" default="" optional="1" />
    <fc:param name="background" type="string" />
    <fc:param name="margin" type="string" />
    <fc:param name="columns" type="Array" />
    <fc:param name="navTitle" type="string" default="" optional="1" />
    <fc:param name="cta" type="Typolink" optional="1" />

    <fc:renderer>
      <f:variable name="ctaTheme" value="brand" />

      <f:switch expression="{background}">
        <f:case value="light-brand">
          <f:variable name="backgroundClasses" value="bg-brand-50 group/light-brand" />
          <f:variable name="ctaTheme" value="brand" />
        </f:case>
        <f:case value="brand">
          <f:variable name="backgroundClasses" value="bg-brand-700 dark" />
        </f:case>
        <f:case value="light-gray">
          <f:variable name="backgroundClasses" value="bg-gray-50 group/light-gray" />
        </f:case>
        <f:defaultCase>
          <f:variable name="backgroundClasses" value="" />
        </f:defaultCase>
      </f:switch>

      <f:variable name="marginClass" value="" />

      <f:switch expression="{margin}">
        <f:case value="lg">
          <f:variable name="marginClass" value="{marginClass} my-8 lg:my-12 xl:my-16" />
        </f:case>
        <f:case value="xs">
          <f:variable name="marginClass" value="{marginClass} my-3 lg:my-4 xl:my-6" />
        </f:case>
        <f:case value="base">
          <f:variable name="marginClass" value="{marginClass} my-4 lg:my-6 xl:my-8" />
        </f:case>
      </f:switch>

      <div class="group {backgroundClasses} {marginClass} {class}">
        <f:if condition="{navTitle}">
          <a name="{navTitle}" data-container-anchor="1"></a>
        </f:if>

        <f:variable name="childClasses" value="py-12 lg:py-16" />
        <f:if condition="{intoPrevContainer}">
          <f:variable name="childClasses" value="pt-6 pb-12 -mt-12 lg:-mt-16" />
        </f:if>

        <f:variable name="childClasses" value="container {childClasses}" />
        <f:if condition="{inset}">
          <f:variable name="childClasses" value="{childClasses} max-w-container-indent" />
        </f:if>

        <div class="{childClasses}">
          <fc:slot />
          <f:if condition="{columns -> f:count()} > 1">
            <f:then>
              <dbic:container.grid columns="{columns}" divider="{divider}" class="{gridTypeClass}" />
            </f:then>
            <f:else>
              <dbic:container.grid.column contents="{columns.0}" />
            </f:else>
          </f:if>
          <f:if condition="{cta}">
            <div class="px-6 mt-6 flex justify-center">
              <dbic:button link="{cta}" theme="{ctaTheme}" />
            </div>
          </f:if>
        </div>
      </div>
    </fc:renderer>
  </fc:component>
</html>
