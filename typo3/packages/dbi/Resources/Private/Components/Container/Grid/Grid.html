<html
  xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
  xmlns:fc="http://typo3.org/ns/SMS/FluidComponents/ViewHelpers"
  xmlns:dbic="http://typo3.org/ns/Mogic/Dbi/Components"
  data-namespace-typo3-fluid="true"
  >
  <fc:component>
    <fc:param name="columns" type="Array" />
    <fc:param name="divider" type="boolean" default="0" optional="1" />
    <fc:renderer>
      <f:variable name="gapClasses" value="gap-6" />
      <f:if condition="{divider}">
        <f:variable name="gapClasses" value="divide-y divide-gap-y-6 lg:divide-x lg:divide-gap-x-6 lg:divide-gap-y-0 lg:divide-y-0" />
      </f:if>
      <div class="group/grid grid {gapClasses} {class}">
        <f:for each="{columns}" as="column">
          <dbic:container.grid.column contents="{column}" />
        </f:for>
      </div>
    </fc:renderer>
  </fc:component>
</html>
