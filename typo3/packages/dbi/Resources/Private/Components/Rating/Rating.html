<html
    xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
    xmlns:fc="http://typo3.org/ns/SMS/FluidComponents/ViewHelpers"
    xmlns:dbic="http://typo3.org/ns/Mogic/Dbi/Components"
    xmlns:dbi="http://typo3.org/ns/Mogic/Dbi/ViewHelpers"
    data-namespace-typo3-fluid="true"
    >

    <fc:component>
        <fc:param name="score" type="number" />
        <fc:param name="source" type="string" />
        <fc:param name="showTextScore" type="boolean" optional="1" default="1" />

        <f:variable name="percent" value="{dbi:math.percent(value: score, of: 5)}%" />
        <f:variable name="starContainerClasses" value="flex gap-1" />
        <f:variable name="starClasses" value="size-[1.25em] text-green-500 shrink-0" />

        <fc:renderer>
            <div class="flex flex-col items-center {class}">
                <div class="relative mb-2">
                    <div class="{starContainerClasses}">
                        <f:for each="{0: 0, 1:1, 2:2, 3:3, 4:4}" as="value">
                            <dbic:icon name="star" class="{starClasses}" />
                        </f:for>
                    </div>
                    <div class="{starContainerClasses} absolute top-0 overflow-hidden" style="width: {percent}">
                        <f:for each="{0: 0, 1:1, 2:2, 3:3, 4:4}" as="value">
                            <dbic:icon name="star-filled" class="{starClasses}" />
                        </f:for>
                    </div>
                </div>
                <f:if condition="{showTextScore}">
                    <span class="mb-3 text-brand-950 group-[.dark]/testimonial:text-white text-[.75em]">
                        {score} von 5 Sternen auf {source}
                    </span>
                    <fc:slot />
                </f:if>
            </div>
        </fc:renderer>
    </fc:component>
</html>
