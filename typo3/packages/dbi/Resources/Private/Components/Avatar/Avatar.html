<html
    xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
    xmlns:fc="http://typo3.org/ns/SMS/FluidComponents/ViewHelpers"
    xmlns:dbic="http://typo3.org/ns/Mogic/Dbi/Components"
    xmlns:dbi="http://typo3.org/ns/Mogic/Dbi/ViewHelpers"
    data-namespace-typo3-fluid="true"
    >
    <fc:component>
        <fc:param name="personName" type="string" />
        <fc:param name="image" type="Image" optional="1" />

        <fc:renderer>
            <span
                class="flex items-center justify-center rounded-full size-[3em] overflow-hidden uppercase bg-brand-950 text-white {class}">
                <f:if condition="{image}">
                    <f:then>
                        <dbic:image image="{image}" ratio="aspect-square" class="w-full h-full object-cover" />
                    </f:then>
                    <f:else>
                        <dbi:initials value="{personName}" />
                    </f:else>
                </f:if>
            </span>
        </fc:renderer>
    </fc:component>
</html>
