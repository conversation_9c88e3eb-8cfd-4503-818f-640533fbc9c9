<html
  xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
  xmlns:fc="http://typo3.org/ns/SMS/FluidComponents/ViewHelpers"
  xmlns:dbic="http://typo3.org/ns/Mogic/Dbi/Components"
  data-namespace-typo3-fluid="true"
>
  <fc:component>
    <fc:param name="id" type="string" />
    <fc:param name="name" type="string" optional="1" />
    <fc:param name="value" type="string" />
    <fc:param name="property" type="string" optional="1" />
    <fc:param name="checked" type="Boolean" optional="1" default="ture" />
    <fc:param name="additionalAttributes" type="Array" optional="1" />
    <fc:param name="radioClasses" type="string" optional="1" />
    <fc:param name="style" type="string" optional="1" default="" />

    <fc:renderer>
      <label for="{id}" class="flex items-center gap-3 text-gray-800 text-sm leading-normal peer-disabled:text-gray-500 {class}" style="{style}">
        <f:form.radio
          id="{id}"
          name="{name}"
          value="{value}"
          property="{property}"
          checked="{checked}"
          additionalAttributes="{additionalAttributes}"
          class="appearance-none peer relative
            w-6 h-6 shrink-0 rounded-full border border-gray-300 bg-gray-100
            focus:border-brand-500
            disabled:bg-gray-100 disabled:border-gray-300
            checked:focus:ring-1 checked:focus:ring-brand-200
            {radioClasses}"
        />
        <div class="absolute left-[5px] w-3.5 h-3.5 rounded-full bg-transparent pointer-events-none hidden peer-checked:block peer-checked:bg-brand-500 peer-disabled:bg-gray-500"></div>
        <fc:slot />
      </label>
    </fc:renderer>
  </fc:component>
</html>
