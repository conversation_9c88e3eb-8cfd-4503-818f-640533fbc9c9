<html
  xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
  xmlns:fc="http://typo3.org/ns/SMS/FluidComponents/ViewHelpers"
  xmlns:dbic="http://typo3.org/ns/Mogic/Dbi/Components"
  data-namespace-typo3-fluid="true"
>
  <fc:component>
    <fc:param name="id" type="string" />
    <fc:param name="value" type="string" optional="1" />
    <fc:param name="name" type="string" optional="1" />
    <fc:param name="property" type="string" optional="1" />
    <fc:param name="multiple" type="string" optional="1" default="true" />
    <fc:param name="checked" type="Boolean" optional="1" default="ture" />
    <fc:param name="required" type="Boolean" optional="1" default="ture" />
    <fc:param name="additionalAttributes" type="Array" optional="1" />
    <fc:param name="checkboxClasses" type="string" optional="1" />
    <fc:param name="style" type="string" optional="1" default="" />

    <fc:renderer>
      <label for="{id}" class="flex items-start gap-1 text-gray-800 leading-6 peer-disabled:text-gray-500 {class}" style="{style}">
        <f:form.checkbox
          id="{id}"
          name="{name}"
          value="{value}"
          property="{property}"
          multiple="{multiple}"
          checked="{checked}"
          additionalAttributes="{additionalAttributes}"
          class="appearance-none peer relative
            mr-2 w-5 h-5 shrink-0 border border-gray-500 rounded-sm bg-white
            focus:border-brand-500
            disabled:bg-gray-100 disabled:border-gray-500
            checked:bg-brand-500 checked:border-brand-500 checked:focus:ring-1 checked:focus:ring-brand-200
            {checkboxClasses}"
        />
        <dbic:icon name="check" class="absolute w-5 h-5 pointer-events-none hidden
          peer-checked:block peer-checked:text-white
          peer-disabled:text-gray-500" />
        <f:if condition="{required}">* </f:if>
        <fc:slot />
      </label>
    </fc:renderer>
  </fc:component>
</html>
