<html
  xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
  xmlns:fc="http://typo3.org/ns/SMS/FluidComponents/ViewHelpers"
  data-namespace-typo3-fluid="true"
>
  <fc:component>
    <fc:param name="id" type="string" />
    <fc:param name="name" type="string" optional="1" />
    <fc:param name="type" type="string" optional="1" default="text" />
    <fc:param name="property" type="string" optional="1" />
    <fc:param name="placeholder" type="string" optional="1" default="" />
    <fc:param name="value" type="string" optional="1" />
    <fc:param name="additionalAttributes" type="Array" optional="1" />
    <fc:param name="inputClasses" type="string" optional="1" />
    <fc:param name="style" type="string" optional="1" default="" />

    <fc:renderer>
      <div class="@container relative flex flex-col gap-1 {class}" style="{style}">
      <fc:slot />
        <f:form.textfield
          type="{type}"
          property="{property}"
          id="{id}"
          name="{name}"
          value="{value}"
          placeholder="{placeholder}"
          additionalAttributes="{additionalAttributes}"
          class="input {inputClasses}"
        />
      </div>
    </fc:renderer>
  </fc:component>
</html>
