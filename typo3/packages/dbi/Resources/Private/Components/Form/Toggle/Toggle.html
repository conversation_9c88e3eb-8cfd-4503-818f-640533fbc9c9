<html
  xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
  xmlns:fc="http://typo3.org/ns/SMS/FluidComponents/ViewHelpers"
  xmlns:dbic="http://typo3.org/ns/Mogic/Dbi/Components"
  data-namespace-typo3-fluid="true"
  >
  <fc:component>
    <fc:param name="id" type="string" />
    <fc:param name="name" type="string" optional="1" />
    <fc:param name="property" type="string" optional="1" />
    <fc:param name="value" type="Boolean" optional="1" default="false" />
    <fc:param name="label" type="string" optional="1" />
    <fc:param name="checked" type="Boolean" optional="1" default="ture" />
    <fc:param name="required" type="Boolean" optional="1" default="ture" />
    <fc:param name="additionalAttributes" type="Array" optional="1" />
    <fc:param name="style" type="string" optional="1" default="" />

    <fc:renderer>
      <div class="@container">
        <label class="flex items-center gap-2.5 mb-7 @xs:mb-5 cursor-pointer text-gray-800 leading-6 disabled:text-gray-500 {class}" style="{style}">
          <f:form.checkbox
            id="{id}"
            value="{value}"
            name="{name}"
            property="{property}"
            multiple="false"
            checked="{checked}"
            additionalAttributes="{additionalAttributes}"
            class="sr-only peer"
          />
          <f:if condition="!{label}">
            <span class="text-sm">Off</span>
          </f:if>
          <div class="relative w-12 h-7 bg-gray-100 border border-gray-300 rounded-full peer
            after:absolute after:top-1/2 after:-translate-y-1/2 after:start-0.5 after:h-6 after:w-6 after:rounded-full after:bg-white after:transition-all
            peer-focus:outline-hidden peer-focus:ring-1 peer-focus:ring-brand-500
            peer-checked:bg-brand-500 peer-checked:after:translate-x-[75%]
            "
          ></div>
          <f:if condition="!{label}">
            <span class="text-sm">On</span>
          </f:if>
          <f:if condition="{label}">
            <span>{label}</span>
          </f:if>
        </label>
      </div>
    </fc:renderer>
  </fc:component>
</html>
