<html
  xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
  xmlns:v="http://typo3.org/ns/TYPO3/CMS/vhs/ViewHelpers"
  xmlns:fc="http://typo3.org/ns/SMS/FluidComponents/ViewHelpers"
  xmlns:dbic="http://typo3.org/ns/Mogic/Dbi/Components"
  xmlns:x-bind="http://example.org/dummy-ns"
  xmlns:x-on="http://example.org/dummy-ns"
  data-namespace-typo3-fluid="true"
>
  <fc:component>
    <fc:param name="options" type="Object"  />
    <fc:param name="id" type="string" />
    <fc:param name="name" type="string" optional="1" />
    <fc:param name="property" type="string" optional="1" />
    <fc:param name="value" type="string" optional="1" default="" />
    <fc:param name="placeholder" type="string" optional="1" default="Bitte wählen Sie eine Option" />
    <fc:param name="multiple" type="Boolean" optional="1" default="true" />
    <fc:param name="additionalAttributes" type="Array" optional="1" />
    <fc:param name="selectboxClasses" type="string" optional="1" />
    <fc:param name="style" type="string" optional="1" default="" />

    <fc:renderer>
      <f:variable name="data" value="{
          options: options,
          value: value
        }" />
      <f:variable name="inputBindClasses" value="!!open ? 'border-brand-500' : ''" />
      <f:variable name="inputDefaultAttributes" value="{
        'x-ref': 'input',
        'x-bind:value': 'currentOptionLabel',
        'x-on:click.stop': 'toggle($el)',
        'x-bind:class': inputBindClasses
      }" />

      <div
        aria-label="select-{id}"
        role="group"
        x-data="selectbox({data -> v:format.json.encode()})"
        class="relative flex flex-col gap-1 {class}"
        x-on:keydown.escape="close"
        x-on:keydown.up.prevent="handleKeyUp"
        x-on:keydown.down.prevent="handleKeyDown"
        x-on:keydown.enter.prevent="close()"
        style="{style}"
      >
        <fc:slot />
        <f:form.hidden
          id="select-{id}"
          name="{name}"
          property="{property}"
          additionalAttributes="{'x-bind:value' : 'currentOptionValue'}"
        />
        <div class="relative w-full flex justify-between" role="combobox" aria-label="Select list" x-bind:aria-expanded="open">
          <dbic:form.input
            type="text"
            id="{id}"
            placeholder="{placeholder}"
            class="w-full"
            inputClasses="pr-2 z-20 {selectboxClasses}"
            additionalAttributes="{v:iterator.merge(
                a: additionalAttributes,
                b: inputDefaultAttributes
            )}"
          />
          <div class="absolute top-3 right-3 z-20 transition-all duration-300 ease-in-out pointer-events-none" x-bind:class="{
            'rotate-180': !!open
          }">
            <dbic:icon name="chevron-down" class="w-4 h-4 shrink-0" />
          </div>

          <ul class="pt-10 absolute top-0 z-10 w-full bg-white border border-gray-500 cursor-pointer transition-transform ease-linear duration-300"
            x-ref="list"
            x-id="['items']"
            x-show="open"
            x-on:click.outside="close"
            x-bind:class="open ? 'translate-y-0' : '-translate-y-full'"
          >
            <f:for each="{options}" as="option" iteration="iterator">
              <li
                x-bind:id="$id('items')"
                class="w-full px-3 py-2 flex justify-between items-center hover:bg-gray-100"
                x-bind:class="currentIndex == {iterator.index} ? 'bg-gray-100' : ''"
                x-on:mouseover="currentIndex = {iterator.index}"
                x-on:click.prevent.stop="currentIndex == {iterator.index}; close();"
              >
                {option.label}
                <span x-show="(currentOptionValue == '{option.value}')">
                  <dbic:icon name="check" class="w-4 h-4 text-brand-500" />
                </span>
              </li>
            </f:for>
          </ul>
        </div>
      </div>
    </fc:renderer>
  </fc:component>
</html>
