<html
  xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
  xmlns:fc="http://typo3.org/ns/SMS/FluidComponents/ViewHelpers"
  data-namespace-typo3-fluid="true"
  >
  <fc:component>
    <fc:param name="title" type="string" optional="1" default="" />
    <fc:param name="for" type="string" optional="1" default="" />
    <fc:param name="mandatory" type="Boolean" optional="1" default="false" />
    <fc:param name="mandatoryPosition" type="string" optional="1" default="order-last" />

    <fc:renderer>
        <label
            for="{for}"
            title="{title}"
            class="flex gap-1 text-current {class}"
            >
            <fc:slot />
            <f:if condition="{mandatory}">
              <span class="{mandatoryPosition}">*</span>
            </f:if>
        </label>
    </fc:renderer>
  </fc:component>
</html>