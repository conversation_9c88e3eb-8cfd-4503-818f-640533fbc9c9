<html
  xmlns:dbi="http://typo3.org/ns/Mogic/Dbi/ViewHelpers"
  xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
  xmlns:fc="http://typo3.org/ns/SMS/FluidComponents/ViewHelpers"
  xmlns:v="http://typo3.org/ns/FluidTYPO3/Vhs/ViewHelpers"
  xmlns:xlink="http://www.w3.org/1999/xlink"
  data-namespace-typo3-fluid="true"
>
  <fc:component>
    <fc:param name="name" type="string" />
    <fc:param
      name="hiddenFromScreenreaders"
      type="boolean"
      default="1"
      optional="1"
    />

    <f:variable
      name="spritemapPath"
      value="{dbi:uri.resource(versionNumber: 1, path:'EXT:dbi/Resources/Public/Icons/icon-spritemap.svg')}"
    />
    <f:variable name="iconName"
      >{v:format.pregReplace( pattern: "/.svg/", replacement: "",
      subject:"{name} ")}</f:variable
    >
    <f:variable name="label"><f:translate extensionName="dbi" key="icon-{iconName}"/></f:variable>
    <f:if condition="{label}"><f:variable name="hiddenFromScreenreaders" value="0"/></f:if>
    <fc:renderer>
      <f:if condition="{hiddenFromScreenreaders} == 1">
        <f:then>
          <svg class="{class}" viewBox="0 0 20 20" aria-hidden="true">
            <use xlink:href="{spritemapPath}#{iconName -> f:format.trim()}" />
          </svg>
        </f:then>
        <f:else>
          <svg class="{class}" viewBox="0 0 20 20" aria-label="{label}">
            <use xlink:href="{spritemapPath}#{iconName -> f:format.trim()}" />
            </svg>
        </f:else>
      </f:if>
    </fc:renderer>
  </fc:component>
</html>
