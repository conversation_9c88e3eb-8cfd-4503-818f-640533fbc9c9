<html
    xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
    xmlns:fc="http://typo3.org/ns/SMS/FluidComponents/ViewHelpers"
    xmlns:dbic="http://typo3.org/ns/Mogic/Dbi/Components"
    xmlns:x-bind="http://example.org/dummy-ns"
    xmlns:x-on="http://example.org/dummy-ns"
    xmlns:x-cloak="http://example.org/dummy-ns"
    data-namespace-typo3-fluid="true"
>
    <fc:component>
        <fc:renderer>
            <f:variable name="paginationButtonClasses" value="bg-brand-500 text-white p-2.5 disabled:hidden *:text-current *:size-3 lg:*:size-5 rounded-sm absolute -translate-y-1/2 top-1/2" />

            <button
                class="{paginationButtonClasses} left-0 translate-x-1/2"
                x-on:click="prev"
            >
                <dbic:icon name="chevron-left" class="pointer-events-none" />
            </button>
            <button
                class="{paginationButtonClasses} right-0 -translate-x-1/2"
                x-on:click="next"
            >
                <dbic:icon name="chevron-right" class="pointer-events-none" />
            </button>

            <div
                class="absolute p-1 lg:p-2 text-xs bg-black-opaque right-0 text-white"
                x-html="pagination"
                x-cloak=""
                x-bind:class="{'w-full text-center bottom-0': !!fullscreen, 'bottom-10': !fullscreen}">
            </div>
        </fc:renderer>
    </fc:component>
</html>
