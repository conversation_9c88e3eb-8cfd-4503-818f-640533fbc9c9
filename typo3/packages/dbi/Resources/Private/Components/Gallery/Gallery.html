<html
    xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
    xmlns:v="http://typo3.org/ns/FluidTYPO3/Vhs/ViewHelpers"
    xmlns:fc="http://typo3.org/ns/SMS/FluidComponents/ViewHelpers"
    xmlns:dbic="http://typo3.org/ns/Mogic/Dbi/Components"
    xmlns:x-bind="http://example.org/dummy-ns"
    xmlns:x-on="http://example.org/dummy-ns"
    xmlns:x-id="http://example.org/dummy-ns"
    xmlns:x-cloak="http://example.org/dummy-ns"
    data-namespace-typo3-fluid="true"
>
    <fc:component>
        <fc:param name="thumbs" type="boolean" optional="1" default="0" />
        <fc:param name="collection" type="Array" />

        <fc:renderer>
            <f:variable name="itemClasses" value="flex flex-col gap-y-4 transition-transform justify-center items-center shrink-0 swiper-slide" />
            <f:variable name="itemsCount" value="{collection -> f:count()}" />

            <f:variable name="galleryData" value="{
                length: itemsCount,
                thumbSlider: thumbs
            }" />

            <f:variable name="cycleCount" value="3" />
            <f:if condition="{itemsCount} > 4">
                <f:variable name="cycleCount" value="1" />
            </f:if>

            <fc:slot />

            <div
                class="group/gallery relative"
                x-data="gallery({galleryData -> v:format.json.encode()})"
                x-id="['gallery']"
            >
                <div
                    class="overflow-hidden w-full swiper group/swiper" x-bind:data-gallery-main="identifier"
                    x-on:click="toggleFullscreen"
                    x-bind:class="{'relative': !fullscreen, 'h-screen fixed top-0 left-0 z-100 bg-black': !!fullscreen}"
                >
                    <dbic:gallery.list
                        class="flex transition-transform w-full h-full"
                        cycleCount="{cycleCount}"
                        assets="{collection}"
                        withTitle="1"
                        itemClass="{itemClasses} w-full"
                        assetClass="w-full h-full object-contain bg-black-opaque"
                        itemClickHandler="toggleFullscreen" />

                    <dbic:gallery.pagination />

                    <button
                        class="text-white disabled:hidden *:text-current *:size-6 lg:*:size-8 p-1
                        absolute top-0 right-0 translate-y-1/2 -translate-x-1/2"
                        x-on:click="fullscreen = false"
                        x-cloak=""
                        x-bind:class="{'hidden': !fullscreen}"
                    >
                        <dbic:icon name="x" class="pointer-events-none" />
                    </button>

                    <div
                        class="absolute bottom-10 p-1 lg:p-2 bg-black-opaque left-0 text-xs items-center cursor-pointer text-white flex gap-2 select-none"
                        x-cloak=""
                        x-bind:class="{'hidden': !!fullscreen}"
                    >
                        <dbic:icon name="fullscreen" class="size-3" />
                        Vollbild
                    </div>
                </div>

                <f:if condition="{thumbs}">
                    <div
                        class="relative overflow-hidden swiper w-full group/swiper py-4"
                        x-bind:data-gallery-thumb="identifier"
                    >
                        <dbic:gallery.list
                            class="flex transition-transform w-full h-full"
                            cycleCount="{cycleCount}"
                            itemClass="{itemClasses} w-1/4 opacity-50"
                            assets="{collection}"
                            itemClickHandler="gotoFromThumb"
                        />
                    </div>
                </f:if>
            </div>
        </fc:renderer>
    </fc:component>
</html>
