<html
  xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
  xmlns:fc="http://typo3.org/ns/SMS/FluidComponents/ViewHelpers"
  xmlns:dbic="http://typo3.org/ns/Mogic/Dbi/Components"
  xmlns:x-on="http://example.org/dummy-ns"
  xmlns:x-bind="http://example.org/dummy-ns"
  data-namespace-typo3-fluid="true"
  >
  <fc:component>
    <fc:param name="item" type="Image" />
    <fc:param name="cycleIndex" type="integer" />
    <fc:param name="index" type="integer" />
    <fc:param name="assetClass" type="string" optional="1" default="" />
    <fc:param name="clickHandler" type="string" />

    <fc:renderer>
        <div
            class="{class} cursor-pointer"
            data-slide-index="{index}"
            data-copy-index="{cycleIndex}"
            x-on:click.stop="{clickHandler}"
            x-bind:class="{'*:max-h-[90vh] *:max-w-[80vw]': fullscreen}"
        >
            <dbic:image image="{item}" class="{assetClass}" />
            <fc:slot />
        </div>
    </fc:renderer>
  </fc:component>
</html>
