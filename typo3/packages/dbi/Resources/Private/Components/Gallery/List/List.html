<html
    xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
    xmlns:v="http://typo3.org/ns/TYPO3/CMS/Vhs/ViewHelpers"
    xmlns:dbi="http://typo3.org/ns/Mogic/Dbi/ViewHelpers"
    xmlns:fc="http://typo3.org/ns/SMS/FluidComponents/ViewHelpers"
    xmlns:dbic="http://typo3.org/ns/Mogic/Dbi/Components"
    xmlns:x-bind="http://example.org/dummy-ns"
    data-namespace-typo3-fluid="true"
>
    <fc:component>
        <fc:param name="assets" type="Array" />
        <fc:param name="withTitle" type="boolean" optional="1" default="0" />
        <fc:param name="cycleCount" type="integer" optional="1" default="" />
        <fc:param name="assetClass" type="string" optional="1" default="" />
        <fc:param name="itemClass" type="string" optional="1" default="" />
        <fc:param name="itemClickHandler" type="string" optional="1" default="()=> {}" />
        <fc:renderer>
            <div class="{class} swiper-wrapper">
                <v:iterator.loop count="{cycleCount}" iteration="cycleIteration">
                    <f:for each="{assets}" as="asset" iteration="assetIterator">
                        <dbic:gallery.item
                            item="{asset}"
                            class="{itemClass} relative"
                            assetClass="{assetClass}"
                            cycleIndex="{cycleIteration.index}"
                            index="{assetIterator.index}"
                            clickHandler="{itemClickHandler}"
                        >
                            <f:if condition="{withTitle}">
                                <span class="h-6 text-xs lg:text-sm flex" x-bind:class="{'absolute top-0 w-full': fullscreen}">
                                    <f:if condition="{asset.title}">
                                        <span class="flex items-center justify-center w-full text-center" x-bind:class="{'bg-black-opaque text-white': fullscreen}">{asset.title}</span>
                                    </f:if>
                                </span>
                            </f:if>
                        </dbic:gallery.item>
                    </f:for>
                </v:iterator.loop>
            </div>
        </fc:renderer>
    </fc:component>
</html>