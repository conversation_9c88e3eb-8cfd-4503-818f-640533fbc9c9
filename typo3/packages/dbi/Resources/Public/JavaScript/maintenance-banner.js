function showkMaintenanceBanner() {
    var bannerElem = document.getElementById('maintenance-banner');
    if (bannerElem === null) {
        return;
    }

    var hash = bannerElem.dataset['hash'];
    if (document.cookie.split(";").some((item) => item.includes("seenMaintenance=" + hash))) {
        return true;
    } else {
        bannerElem.classList.remove("hidden");
    }
}

function setMaintenanceCookie() {
    var bannerElem = document.getElementById('maintenance-banner');
    if (bannerElem === null) {
        return;
    }

    var hash = bannerElem.dataset['hash'];
    var expires = (new Date(Date.now()+ 30*86400*1000)).toUTCString();
    document.cookie = "seenMaintenance=" + hash + "; expires=" + expires + ";Path=/;SameSite=Lax";

    bannerElem.classList.add("hidden");
}

showkMaintenanceBanner();
