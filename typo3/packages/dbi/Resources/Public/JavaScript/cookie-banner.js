/**
 * Show the cookie banner that asks for approval, via yes / no buttons
 *
 * "consentCookie" states:
 * - accepted: "yes"
 * - declined: "no"
 * - "do not track" from server: "dnt" (will be changed to "no")
*/

// false disables the Cookie, allowing you to style the banner
var setCookie = true;
// Number of days before the cookie expires, and the banner reappears
var cookieDuration = 180;
// Name of our cookie
var cookieName = 'consentCookie';

window.addEventListener(
    'load',
    function() {
        var cookieVal = getCookieValue(window.cookieName);
        if (cookieVal == 'dnt') {
            cookiebannerShowDnt();
            createCookie(window.cookieName, 'no', window.cookieDuration);
        } else if (cookieVal != 'yes' && cookieVal != 'no') {
            cookiebannerShow();
        }
    }
);

function createCookie(name, value, days) {
    if (days) {
        var date = new Date();
        date.setTime(date.getTime() + (days*24*60*60*1000));
        // toGMTString nicht mehr benutzen
        var expires = "; expires="+date.toGMTString();
    } else {
        var expires = "";
    }

    //cookie is set: expected output is:
    // "consentCookie=on; expires=Mon, 18 Dec 1995 17:28:35 GMT; path=/""
    if (window.setCookie) {
        document.cookie = name + "=" + value + expires + "; path=/; SameSite=Lax";
    }
}

function getCookieValue(name) {
    var nameEQ = name + "=";
    var ca = document.cookie.split(';');
    for (var i=0; i < ca.length; i++) {
        var c = ca[i];
        while (c.charAt(0) == ' ') {
            c = c.substring(1, c.length);
        }
        if (c.indexOf(nameEQ) == 0) {
            return c.substring(nameEQ.length,c.length);
        }
    }

    return null;
}

function cookiebannerShow() {
    var cbElem = document.getElementById('cookiebanner');
    if (!cbElem) {
        return;
    }
    cbElem.style.display = 'flex';
    document.body.classList.add('cookiebanner-active', 'fixed');
}

function cookiebannerShowDnt() {
    var cbElem = document.getElementById('cookiebannerdnt');
    var cbDuration = 10 * 1000;

    if (!cbElem) {
        return;
    }

    cbElem.style.display = 'block';

    window.setTimeout(
        function () {
            cbElem.querySelector("[data-progressbar]").classList.add('translate-x-0');
        },
        100
    );

    window.setTimeout(
        function () {
            cbElem.style.display = 'none';
        },
        cbDuration
    );
}

function cookiebannerHide() {
    var cbElem = document.getElementById('cookiebanner');
    if (!cbElem) {
        return;
    }
    cbElem.style.display = 'none';
    document.body.classList.remove('cookiebanner-active', 'fixed');
}

function cookiebannerAllow() {
    createCookie(window.cookieName, 'yes', window.cookieDuration);
    cookiebannerAllowEtracker();
    cookiebannerHide();
}
function cookiebannerAllowEtracker()
{
    if (typeof _etracker != 'undefined') {
        _etracker.enableCookies();
        _etracker.enableTracking();
    }
}

function cookiebannerDecline() {
    createCookie(window.cookieName, 'no', window.cookieDuration);
    cookiebannerDeclineEtracker();
    cookiebannerHide();
}
function cookiebannerDeclineEtracker() {
    if (typeof _etracker != 'undefined') {
        _etracker.disableCookies();
        _etracker.disableTracking();
    }
}
