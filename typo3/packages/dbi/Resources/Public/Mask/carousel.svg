<?xml version="1.0" encoding="UTF-8" standalone="no" ?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="1080" height="1080" viewBox="0 0 1080 1080" xml:space="preserve">
<desc>Created with Fabric.js 5.2.4</desc>
<defs>
</defs>
<rect x="0" y="0" width="100%" height="100%" fill="transparent"></rect>
<g transform="matrix(1 0 0 1 540 540)" id="a70fc861-9596-4406-9b6a-e8309e86c534"  >
</g>
<g transform="matrix(1 0 0 1 540 540)" id="8f0b776d-4033-4916-8f58-05f990a3eefb"  >
<rect style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,255,255); fill-rule: nonzero; opacity: 1; visibility: hidden;" vector-effect="non-scaling-stroke"  x="-540" y="-540" rx="0" ry="0" width="1080" height="1080" />
</g>
<g transform="matrix(2.05 0 0 2.28 544 468.95)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;"  transform=" translate(-251.13, -229.33)" d="M 385.008 105.748 L 117.257 105.748 C 100.194 105.748 86.363 119.58 86.363 136.642 L 86.363 322.009 C 86.363 339.073 100.194 352.904 117.257 352.904 L 385.008 352.904 C 402.072 352.904 415.903 339.073 415.903 322.009 L 415.903 136.642 C 415.903 119.58 402.072 105.748 385.008 105.748 Z M 381.147 322.009 L 121.119 322.009 C 118.986 322.009 117.257 320.28 117.257 318.147 L 117.257 140.505 C 117.257 138.372 118.986 136.642 121.119 136.642 L 381.147 136.642 C 383.28 136.642 385.008 138.372 385.008 140.505 L 385.008 318.147 C 385.008 320.28 383.28 322.009 381.147 322.009 Z M 168.748 162.388 C 154.529 162.388 143.002 173.915 143.002 188.134 C 143.002 202.352 154.529 213.879 168.748 213.879 C 182.966 213.879 194.493 202.352 194.493 188.134 C 194.493 173.915 182.966 162.388 168.748 162.388 Z M 148.151 291.115 L 354.114 291.115 L 354.114 239.624 L 297.787 183.297 C 294.771 180.281 289.88 180.281 286.864 183.297 L 209.94 260.22 L 184.507 234.787 C 181.491 231.772 176.601 231.772 173.584 234.787 L 148.151 260.22 L 148.151 291.115 Z" stroke-linecap="round" />
</g>
<g transform="matrix(0 -3.74 2.94 0 91.25 454.93)"  >
<path style="stroke: rgb(0,0,0); stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;"  transform=" translate(-51.82, -184.76)" d="M 51.385 156.653 L 78.925 212.875 L 24.713 212.875 L 51.385 156.653 Z" stroke-linecap="round" />
</g>
<g transform="matrix(0 3.67 -2.72 0 990.8 453.91)"  >
<path style="stroke: rgb(0,0,0); stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;"  transform=" translate(-51.82, -184.76)" d="M 51.385 156.653 L 78.925 212.875 L 24.713 212.875 L 51.385 156.653 Z" stroke-linecap="round" />
</g>
</svg>
