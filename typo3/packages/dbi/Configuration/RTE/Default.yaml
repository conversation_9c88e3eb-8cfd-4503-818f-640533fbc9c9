imports:
    - { resource: 'EXT:dbi/Configuration/RTE/Processing.yaml' }
    - { resource: 'EXT:dbi/Configuration/RTE/Editor/Base.yaml' }
    # - { resource: 'EXT:dbi/Configuration/RTE/LinkClasses.yaml' }
    - { resource: 'EXT:dbi/Configuration/RTE/Editor/Plugins.yaml' }

editor:
  config:
    allowTags:
      - a
      # - abbr
      # - acronym deprecated
      - address
      - article
      # - big deprecated
      - blockquote
      - br
      - caption
      # - cite
      # - code
      # - col
      # - colgroup
      # - dd
      # - del
      # - dfn
      # - dl
      - div
      # - dt
      - em
      - figure
      - figcaption
      # - footer
      # - header
      - h1
      - h2
      - h3
      - h4
      - h5
      - h6
      - hr
      - i
      - img
      # - ins
      # - kbd
      # - label
      - li
      # - nav
      - ol
      - p
      - pre
      - q
      - s
      # - samp
      # - section
      - small
      - span
      - strong
      - sub
      - sup
      - table
      - thead
      - tbody
      - tfoot
      - td
      - th
      - tr
      # - tt deprecated
      # - u
      - ul
      # - var

    allowTagsOutside:
      - address
      # - article
      - blockquote
      - figure
      - figcaption
      - hr
      - div
      - a

    formatTags:
      - p
      - h1
      - h2
      - h3
      - h4
      - h5
      - h6

    toolbar:
      items:
        - style
        - heading
        # grouping separator
        - '|'
        # - bold
        - italic
        - subscript
        - superscript
        - softhyphen
        - '|'
        - bulletedList
        - numberedList
        - '|'
        - blockQuote
        - alignment
        - '|'
        - findAndReplace
        - link
        - '|'
        - removeFormat
        - undo
        - redo
        - '|'
        - insertTable
        - '|'
        - specialCharacters
        - horizontalLine
        - sourceEditing

    heading:
      options:
        - {
            model: 'paragraph',
            view: {
              name: 'p',
            },
            title: 'Paragraph',
        }
        - {
            model: 'heading1',
            view: {
              name: 'h1',
              classes: 'heading-1'
            },
            title: 'Headline Typ 1 (H1)',
        }
        - {
            model: 'heading2',
            view: {
              name: 'h1',
              classes: 'heading-2'
            },
            title: 'Headline Typ 2 (H1)',
        }
        - {
            model: 'heading3',
            view: {
              name: 'h2',
              classes: 'heading-2'
            },
            title: 'Headline Typ 3 (H2)',
        }
        - {
            model: 'heading4',
            view: {
              name: 'h3',
              classes: 'heading-3'
            },
            title: 'Headline Typ 4 (H3)',
        }
        - {
            model: 'heading5',
            view: {
              name: 'h4',
              classes: 'heading-4'
            },
            title: 'Headline Typ 5 (H4)',
        }
        - {
            model: 'heading6',
            view: {
              name: 'h5',
              classes: 'heading-5'
            },
            title: 'Headline Typ 6 (H5)',
        }
        - {
            model: 'heading7',
            view: {
              name: 'h6',
              classes: 'heading-6'
            },
            title: 'Headline Typ 7 (H6)',
        }

    style:
      definitions:
        - { name: "Lead", element: "p", classes: ['lead'] }
        - { name: "Small", element: "small", classes: [] }
        - { name: "Muted", element: "span", classes: ['text-muted'] }
        - { name: "Checkliste", element: "ul", classes: ['list-check'] }
        - { name: "DBI Dash List", element: "ul", classes: ['list-dash'] }
        - { name: "DBI Timeline", element: "ul", classes: ['list-timeline'] }
        - { name: "DBI Aufzählung", element: "ol", classes: ['list-counter'] }
        - { name: "DBI Tabelle", element: "table", classes: ['odd-row:bg-gray-100'] }

    alignment:
      options:
        - { name: 'left', className: 'text-left' }
        - { name: 'center', className: 'text-center' }
        - { name: 'right', className: 'text-right' }
        - { name: 'justify', className: 'text-justify' }

    table:
      defaultHeadings: { rows: 1 }
      contentToolbar:
        - tableColumn
        - tableRow
        - mergeTableCells
        - tableProperties
        - tableCellProperties
        - toggleTableCaption

