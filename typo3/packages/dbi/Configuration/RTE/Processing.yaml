
imports:
    - { resource: 'EXT:rte_ckeditor/Configuration/RTE/Processing.yaml' }


processing:
  HTMLparser_db:
      ## STRIP ALL ATTRIBUTES FROM THESE TAGS
      ## If this list of tags is not set, it will default to: b,i,u,br,center,hr,sub,sup,strong,em,li,ul,ol,blockquote,strike.
      ## However, we want to keep xml:lang attribute on most tags and tags from the default list were cleaned on entry.
      # noAttrib: br
      # Can be disabled if you trust ckeditor (If Automatic Content Formatting is enabled, this should be OK)
      # allowTags: %default%
      # denyTags: img
      tags:
        ul:
          allowedAttribs:
            - class
        ol:
          allowedAttribs:
            - class
