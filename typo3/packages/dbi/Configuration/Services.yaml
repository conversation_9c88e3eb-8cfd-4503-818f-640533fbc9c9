services:
  # general settings
  _defaults:
    autowire: true
    autoconfigure: false
    public: false

  Mogic\Dbi\:
    resource: '../Classes/*'
    exclude: '../Classes/Validator/*'

  Mogic\Dbi\Backend\ContextMenuProvider:
    tags:
      - name: backend.contextmenu.itemprovider

  Mogic\Dbi\Backend\ContextMenuProviderFio:
    tags:
      - name: backend.contextmenu.itemprovider

  Mogic\Dbi\Backend\Controller\FioApiDataController:
    tags: ['backend.controller']
    public: true

  Mogic\Dbi\Backend\Controller\TradingAreaController:
    tags: ['backend.controller']
    public: true

  Mogic\Dbi\Backend\EventListener\PageLayoutButtonBarEventListener:
    tags:
      - name: event.listener
        identifier: 'dbi/backend/modify-button-bar'

  Mogic\Dbi\Backend\EventListener\PageLayoutEventListener:
    tags:
      - name: event.listener
        identifier: 'dbi/backend/modify-page-module-content'

  Mogic\Dbi\Command\CenterImportCommand:
    tags:
      - name: console.command
        command: 'dbi:centerimport'
        description: 'Import PBI centers from .csv files'

  Mogic\Dbi\Command\RealtorSyncCommand:
    tags:
      - name: console.command
        command: 'dbi:realtorsync'
        description: 'Sync realtor person data from FIO webmakler API'

  Mogic\Dbi\Command\CenterSyncCommand:
    tags:
      - name: console.command
        command: 'dbi:centersync'
        description: 'Sync center data from FIO webmakler API'

  Mogic\Dbi\Controller\Backend\CreateCenterWizardController:
    tags: ['backend.controller']
    public: true

  Mogic\Dbi\Controller\CookieManagementController:
    public: true

  Mogic\Dbi\Controller\FioImmoProxyController:
    public: true

  Mogic\Dbi\Controller\FioSearchProfileController:
    public: true

  Mogic\Dbi\Controller\MarketPriceAssessmentController:
    public: true

  Mogic\Dbi\Controller\ProjectListController:
    public: true

  Mogic\Dbi\Controller\RealtorCenterListController:
    public: true

  Mogic\Dbi\Controller\RealtorSearchController:
    public: true

  Mogic\Dbi\Domain\Repository\CenterRepository:
    public: true

  Mogic\Dbi\Domain\Repository\TeammemberRepository:
    public: true

  Mogic\Dbi\Domain\Repository\WebmaklerUserRepository:
    public: true

  Mogic\Dbi\Domain\Repository\WebmaklerUserRepositoryCached:
    public: true

  Mogic\Dbi\Domain\Repository\WebGroupRepository:
    public: true

  Mogic\Dbi\Domain\Repository\WebGroupRepositoryCached:
    public: true

  Mogic\Dbi\Frontend\Tracking:
    public: true

  Mogic\Dbi\Frontend\EventListener\HiddenMountPointEventListener:
    tags:
      - name: event.listener
        identifier: 'dbi/hidden-mount-points'

  Mogic\Dbi\Frontend\EventListener\SitemapLinkModifier:
    tags:
      - name: event.listener
        identifier: 'dbi/modify-page-link-configuration'

  Mogic\Dbi\Frontend\EventListener\MaskAllowedFieldsEventListener:
    tags:
      - name: event.listener
        identifier: 'customizeAllowedFields'
        event: MASK\Mask\Event\MaskAllowedFieldsEvent
