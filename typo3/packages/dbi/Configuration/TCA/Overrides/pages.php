<?php

defined('TYPO3') or die();

//page type: Immobilienprojekt
\TYPO3\CMS\Core\Utility\ExtensionManagementUtility::addTcaSelectItem(
    'pages',
    'doktype',
    [
        'label' => 'Immobilienprojekt',
        'value' => 11,
        'icon'  => 'tx-dbi-project-page',
        'group' => 'default',
    ],
);
$GLOBALS['TCA']['pages']['ctrl']['typeicon_classes'][11] = 'tx-dbi-project-page';

//custom project data field in project pages
$GLOBALS['TCA']['pages']['columns']['projectdata'] = [
    'label' => 'Projektdaten',
    'description' => 'Das Bild wird in Ressourcen > Dateien > Media gepflegt.',
    'config' => [
        'type' => 'flex',
        'ds' => [
            'default' => 'FILE:EXT:dbi/Configuration/FlexForms/PageProjectdata.xml',
        ],
    ],
];
$GLOBALS['TCA']['pages']['types'][11]['showitem']
    = $GLOBALS['TCA']['pages']['types'][1]['showitem'];
\TYPO3\CMS\Core\Utility\ExtensionManagementUtility::addToAllTCAtypes(
    'pages',
    '--div--;Projekt, projectdata;Projektdaten',
    11,
    'after:subtitle'
);

//limit page categories to project types
$GLOBALS['TCA']['pages']['types'][11]['columnsOverrides'] = [
    'categories' => [
        'config' => [
            'treeConfig' => [
                'startingPoints' => '###SITE:settings.categories.projectTypes###',
                'appearance' => [
                    'showHeader' => 0,
                    'nonSelectableLevels' => 0,
                ],
            ]
        ]
    ],
];

$GLOBALS['TCA']['tx_cetimeline_domain_model_entry']['columns']['media']['config']['maxitems'] = 1;

// Allow tx_dbi_headerlink table on pages (done in TCA ctrl configuration)

// Add your custom fields first
\TYPO3\CMS\Core\Utility\ExtensionManagementUtility::addTCAcolumns('pages', [
    'tx_headerlinks' => [
        'exclude' => 1,
        'label' => 'Header Links',
        'config' => [
            'type' => 'inline',
            'foreign_table' => 'tx_dbi_headerlink',
            'foreign_field' => 'pid',
            'maxitems' => 8,
            'appearance' => [
                'collapseAll' => 0,
                'levelLinksPosition' => 'top',
                'showSynchronizationLink' => 1,
                'showPossibleLocalizationRecords' => 1,
                'showAllLocalizationLink' => 1
            ],
        ],
        'displayCond' => 'FIELD:backend_layout:=:pagets__dbiConfigurableHeaderLinks',
    ]
]);

// Add a new tab to the page type "Standard"
\TYPO3\CMS\Core\Utility\ExtensionManagementUtility::addToAllTCAtypes(
    'pages',
    '--div--;Headerlinks, tx_headerlinks',
    '1'
);
