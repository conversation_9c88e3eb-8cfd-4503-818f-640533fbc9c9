<?php

use TYPO3\CMS\Core\Utility\ExtensionManagementUtility;
use TYPO3\CMS\Extbase\Utility\ExtensionUtility;

defined('TYPO3') or die();

ExtensionUtility::registerPlugin(
    'Dbi',
    'CookieManagement',
    'Cookieverwaltung (Datenschutzseite)',
    'EXT:dbi/Resources/Public/Icons/Content/CookieManagement.svg'
);
$GLOBALS['TCA']['tt_content']['types']['list']['subtypes_excludelist']['dbi_cookiemanagement'] = 'recursive,select_key,pages';
$GLOBALS['TCA']['tt_content']['types']['list']['subtypes_addlist']['dbi_cookiemanagement'] = 'pi_flexform';
ExtensionManagementUtility::addPiFlexFormValue(
    'dbi_cookiemanagement',
    'FILE:EXT:dbi/Configuration/FlexForms/CookieManagement.xml'
);

ExtensionUtility::registerPlugin(
    'Dbi',
    'FioImmoProxyExpose',
    'FIO Immobilieneinbindung: Expose-Details',
    'EXT:dbi/Resources/Public/Icons/Content/FioImmoProxy.png'
);
$GLOBALS['TCA']['tt_content']['types']['list']['subtypes_excludelist']['dbi_fioimmoproxyexpose'] = 'recursive,select_key,pages';
$GLOBALS['TCA']['tt_content']['types']['list']['subtypes_addlist']['dbi_fioimmoproxyexpose'] = 'pi_flexform';
ExtensionManagementUtility::addPiFlexFormValue(
    'dbi_fioimmoproxyexpose',
    'FILE:EXT:dbi/Configuration/FlexForms/FioImmoProxyExpose.xml'
);

ExtensionUtility::registerPlugin(
    'Dbi',
    'FioImmoProxyList',
    'FIO Immobilieneinbindung: Ergebnisliste',
    'EXT:dbi/Resources/Public/Icons/Content/FioImmoProxy.png'
);
$GLOBALS['TCA']['tt_content']['types']['list']['subtypes_excludelist']['dbi_fioimmoproxylist'] = 'recursive,select_key,pages';
$GLOBALS['TCA']['tt_content']['types']['list']['subtypes_addlist']['dbi_fioimmoproxylist'] = 'pi_flexform';
ExtensionManagementUtility::addPiFlexFormValue(
    'dbi_fioimmoproxylist',
    'FILE:EXT:dbi/Configuration/FlexForms/FioImmoProxyList.xml'
);

ExtensionUtility::registerPlugin(
    'Dbi',
    'FioImmoProxySearch',
    'FIO Immobilieneinbindung: Suchformular',
    'EXT:dbi/Resources/Public/Icons/Content/FioImmoProxy.png'
);
$GLOBALS['TCA']['tt_content']['types']['list']['subtypes_excludelist']['dbi_fioimmoproxysearch'] = 'recursive,select_key,pages';
$GLOBALS['TCA']['tt_content']['types']['list']['subtypes_addlist']['dbi_fioimmoproxysearch'] = 'pi_flexform';
ExtensionManagementUtility::addPiFlexFormValue(
    'dbi_fioimmoproxysearch',
    'FILE:EXT:dbi/Configuration/FlexForms/FioImmoProxySearch.xml'
);

ExtensionUtility::registerPlugin(
    'Dbi',
    'FioSearchProfileDelete',
    'FIO Suchprofil: Löschen',
    'EXT:dbi/Resources/Public/Icons/Content/FioSearchProfile.svg'
);
$GLOBALS['TCA']['tt_content']['types']['list']['subtypes_excludelist']['dbi_fiosearchprofiledelete'] = 'recursive,select_key,pages';

TYPO3\CMS\Extbase\Utility\ExtensionUtility::registerPlugin(
    'Dbi',
    'MarketPriceAssessment',
    'Marktpreiseinschätzung (Maklaro)',
    'EXT:dbi/Resources/Public/Icons/Content/Maklaro.png'
);
$GLOBALS['TCA']['tt_content']['types']['list']['subtypes_excludelist']['dbi_marketpriceassessment'] = 'select_key,pages,recursive';
$GLOBALS['TCA']['tt_content']['types']['list']['subtypes_addlist']['dbi_marketpriceassessment'] = 'pi_flexform';
TYPO3\CMS\Core\Utility\ExtensionManagementUtility::addPiFlexFormValue(
    'dbi_marketpriceassessment',
    'FILE:EXT:dbi/Configuration/FlexForms/MarketPriceAssessment.xml'
);

TYPO3\CMS\Extbase\Utility\ExtensionUtility::registerPlugin(
    'Dbi',
    'ProjectList',
    'Projektliste',
    'EXT:dbi/Resources/Public/Icons/Content/Project.svg'
);
$GLOBALS['TCA']['tt_content']['types']['list']['subtypes_excludelist']['dbi_projectlist'] = 'select_key,pages,recursive';
$GLOBALS['TCA']['tt_content']['types']['list']['subtypes_addlist']['dbi_projectlist'] = 'pi_flexform';
TYPO3\CMS\Core\Utility\ExtensionManagementUtility::addPiFlexFormValue(
    'dbi_projectlist',
    'FILE:EXT:dbi/Configuration/FlexForms/ProjectList.xml'
);

TYPO3\CMS\Extbase\Utility\ExtensionUtility::registerPlugin(
    'Dbi',
    'ProjectSlider',
    'Projektkarussell',
    'EXT:dbi/Resources/Public/Icons/Content/Project.svg'
);
$GLOBALS['TCA']['tt_content']['types']['list']['subtypes_excludelist']['dbi_projectslider'] = 'select_key,pages,recursive';
$GLOBALS['TCA']['tt_content']['types']['list']['subtypes_addlist']['dbi_projectslider'] = 'pi_flexform';
TYPO3\CMS\Core\Utility\ExtensionManagementUtility::addPiFlexFormValue(
    'dbi_projectslider',
    'FILE:EXT:dbi/Configuration/FlexForms/ProjectSlider.xml'
);

TYPO3\CMS\Extbase\Utility\ExtensionUtility::registerPlugin(
    'Dbi',
    'RealtorCenterList',
    'Immobiliencenterliste (manuell)',
    'EXT:dbi/Resources/Public/Icons/Content/Center.svg'
);
$GLOBALS['TCA']['tt_content']['types']['list']['subtypes_excludelist']['dbi_realtorcenterlist'] = 'select_key,pages,recursive';
$GLOBALS['TCA']['tt_content']['types']['list']['subtypes_addlist']['dbi_realtorcenterlist'] = 'pi_flexform';
TYPO3\CMS\Core\Utility\ExtensionManagementUtility::addPiFlexFormValue(
    'dbi_realtorcenterlist',
    'FILE:EXT:dbi/Configuration/FlexForms/RealtorCenterList.xml'
);

TYPO3\CMS\Extbase\Utility\ExtensionUtility::registerPlugin(
    'Dbi',
    'RealtorSearchResults',
    'Maklersuche: Ergebnisse',
    'EXT:dbi/Resources/Public/Icons/Content/Teammember.svg'
);
$GLOBALS['TCA']['tt_content']['types']['list']['subtypes_excludelist']['dbi_realtorsearchresults'] = 'select_key,pages,recursive';
$GLOBALS['TCA']['tt_content']['types']['list']['subtypes_addlist']['dbi_realtorsearchresults'] = 'pi_flexform';
TYPO3\CMS\Core\Utility\ExtensionManagementUtility::addPiFlexFormValue(
    'dbi_realtorsearchresults',
    'FILE:EXT:dbi/Configuration/FlexForms/RealtorSearchResults.xml'
);

TYPO3\CMS\Extbase\Utility\ExtensionUtility::registerPlugin(
    'Dbi',
    'RealtorSearchSearchbar',
    'Maklersuche: Suchformular',
    'EXT:dbi/Resources/Public/Icons/Content/Teammember.svg'
);
$GLOBALS['TCA']['tt_content']['types']['list']['subtypes_excludelist']['dbi_realtorsearchsearchbar'] = 'select_key,pages,recursive';
$GLOBALS['TCA']['tt_content']['types']['list']['subtypes_addlist']['dbi_realtorsearchsearchbar'] = 'pi_flexform';
TYPO3\CMS\Core\Utility\ExtensionManagementUtility::addPiFlexFormValue(
    'dbi_realtorsearchsearchbar',
    'FILE:EXT:dbi/Configuration/FlexForms/RealtorSearchSearchbar.xml'
);

[

  ["light-gray", "Helles Grau (gray-100)"],
  ["white", "Weiss (white)"]
];

$standardThemeItems = [
    [
        "label" => "Transparent",
        "value" => "base",
        "icon"  => "EXT:dbi/Resources/Public/Icons/Theme/Color/base.svg"
    ],
    [
        "label" => "Transparent (für dunklen Hintegrund)",
        "value" => "base-dark",
        "icon"  => "EXT:dbi/Resources/Public/Icons/Theme/Color/base.svg"
    ],
    [
        "label" => "Helle Markenfarbe (brand-50)",
        "value" => "light-brand",
        "icon"  => "EXT:dbi/Resources/Public/Icons/Theme/Color/light-brand.svg"
    ],
    [
        "label" => "Markenfarbe (brand-700)",
        "value" => "brand",
        "icon"  => "EXT:dbi/Resources/Public/Icons/Theme/Color/brand.svg"
    ],
    [
        "label" => "Helles Grau (gray-50)",
        "value" => "light-gray",
        "icon"  => "EXT:dbi/Resources/Public/Icons/Theme/Color/light-gray.svg"
    ],
    [
        "label" => "Weiss (white)",
        "value" => "white",
        "icon"  => "EXT:dbi/Resources/Public/Icons/Theme/Color/white.svg"
    ]
];


// MASK
call_user_func(static function () use ($standardThemeItems) {
    $GLOBALS['TYPO3_CONF_VARS']['SYS']['features']['overrideSharedFields'] =
        true;

    // Standard The
    $GLOBALS['TCA']['tt_content']['columns']['tx_mask_theme']['config']['items'] = $standardThemeItems;
});

// CONTAINER
call_user_func(static function () use ($standardThemeItems) {
    $additionalFields = [
        "tx_dbi_container_background_color" => [
            'exclude' => 1,
            'label' => 'Hintergrundfarbe des Containers',
            'config' => [
                'type' => 'select',
                'renderType' => 'selectSingle',
                'default' => 'base',
                'items' => array_filter($standardThemeItems, function ($item) {
                    return in_array(
                        $item['value'],
                        [
                            "base",
                            "light-brand",
                            "light-gray",
                            'brand'
                        ]
                    );
                })
            ],
        ],
        "tx_dbi_container_into_prev_container" => [
            'exclude' => 1,
            'label' => 'Soll dieser Container in den vorhergehenden Container hineinragen?',
            'config' => [
                'type' => 'check',
                'renderType' => 'checkboxToggle',
                'items' => [
                    [
                        'label' => 'Ja',
                    ],
                ],
            ],
        ],
        "tx_dbi_container_inset" => [
            'exclude' => 1,
            'label' => 'kleinerer Inhaltsbereich des Container (66%)',
            'config' => [
                'type' => 'check',
                'renderType' => 'checkboxToggle',
                'items' => [
                    [
                        'label' => 'Ja',
                    ],
                ],
            ],
        ],
        "tx_dbi_container_stretched" => [
            'exclude' => 1,
            'label' => 'Inhaltselemente in den Spalten nehmen den gesamten Raum ein',
            'config' => [
                'type' => 'check',
                'renderType' => 'checkboxToggle',
                'items' => [
                    [
                        'label' => 'Ja',
                    ],
                ],
            ],
        ],
        "tx_dbi_container_divider" => [
            'exclude' => 1,
            'label' => 'Zwischen den Inhaltsspalten eine Trennlinie anzeigen?',
            'config' => [
                'type' => 'check',
                'renderType' => 'checkboxToggle',
                'items' => [
                    [
                        'label' => 'Ja',
                    ],
                ],
            ],
        ],
        "tx_dbi_container_margin" => [
            'exclude' => 1,
            'label' => 'Abstand zwischen den Containern',
            'config' => [
                'type' => 'select',
                'renderType' => 'selectSingle',
                'default' => 'lg',
                'items' => [
                    [
                        'label' => 'Groß (lg)',
                        'value' => 'lg'
                    ],
                    [
                        'label' => 'Standard (base)',
                        'value' => 'base'
                    ],
                    [
                        'label' => 'Klein (xs)',
                        'value' => 'xs'
                    ],
                    [
                        'label' => 'Kein Abstand',
                        'value' => 'no_margin'
                    ],
                ],
            ],
        ],
        "tx_dbi_container_nav_title" => [
            'exclude' => 1,
            'label' => 'Navigationstitel innerhalb der Seite',
            'config' => [
                'type' => 'input',
                'nullable' => true,
                'default' => null,
                'mode' => 'useOrOverridePlaceholder',
            ]
        ],
        'tx_dbi_container_cta' => [
            'exclude' => 1,
            'label' => 'Button am Ende des Containers',
            'config' => [
                'type' => 'link',
                'allowedTypes' => ['page', 'url', 'record'],
                'nullable' => true,
                'default' => null,
                'mode' => 'useOrOverridePlaceholder',
            ],
        ],
        'tx_dbi_container_header_align' => [
            'exclude' => 1,
            'label' => 'Position der Überschrift',
            'config' => [
                'type' => 'select',
                'renderType' => 'selectSingle',
                'default' => 'base',
                'items' => [
                    [
                        'label' => 'Center (base)',
                        'value' => 'base'
                    ],
                    [
                        'label' => 'Links',
                        'value' => 'left'
                    ]
                ],
            ],
        ],

    ];

    \TYPO3\CMS\Core\Utility\ExtensionManagementUtility::addTCAcolumns(
        'tt_content',
        $additionalFields
    );

    $containerConfigurations = [
        'tx_dbi_container_one_column' => [
            1,
            ['Container (1 Spalte)', 'Standard Container mit Inhaltsspalte'],
            []
        ],
        'tx_dbi_container_two_columns' => [
            2,
            ['Container (2 Spalte je 50%)', 'Standard Container mit 2 Inhaltsspalten (50%)', 2],
            ['tx_dbi_container_divider', 'tx_dbi_container_stretched']
        ],
        'tx_dbi_container_two_columns_1_2' => [
            2,
            ['Container (2 Spalte [33|66])', 'Standard Container mit 2 Inhaltsspalten (Aufteilung 33% - 66%)', 2],
            ['tx_dbi_container_stretched']
        ],
        'tx_dbi_container_two_columns_2_1' => [
            2,
            ['Container (2 Spalten [66|33])', 'Standard Container mit 2 Inhaltsspalten (Aufteilung 66% - 33%)'],
            ['tx_dbi_container_stretched']
        ],
        'tx_dbi_container_three_columns' => [
            3,
            ['Container (3 Spalten je 33%])', 'Standard Container mit Inhaltsspalten (33%)'],
            ['tx_dbi_container_divider', 'tx_dbi_container_stretched']
        ],
        'tx_dbi_container_four_columns' => [
            4,
            ['Container (4 Spalten je 25%])', 'Standard Container mit Inhaltsspalten (25%)'],
            ['tx_dbi_container_divider', 'tx_dbi_container_stretched']
        ]
    ];

    $standardFieldTypesForContainer = [
        'CType',
        'header',
        'header_layout',
        'subheader',
        'tx_dbi_container_header_align',
        'tx_dbi_container_background_color',
        'tx_dbi_container_into_prev_container',
        'tx_dbi_container_inset',
        'tx_dbi_container_margin',
        'tx_dbi_container_nav_title',
        'tx_dbi_container_cta'
    ];

    $containerConfigurationKeys = array_keys($containerConfigurations);

    $standardColumnConf = [
        'name' => 'Spalteninhalt ',
        'label' => "Test",
        'colPos' => 100,
        'disallowed' => [
            'CType' => implode(",", $containerConfigurationKeys)
        ]
    ];

    $containerConfigurations = array_map(
        function ($key, $conf) use ($standardColumnConf, $standardFieldTypesForContainer) {
            $columnsConf = array_fill(0, $conf[0], $standardColumnConf);
            $columns = array_map(
                function ($index, $column) {
                    $column['name'] .= " #" . $index + 1;
                    $column['colPos'] = $column['colPos'] + $index + 1;
                    return $column;
                },
                array_keys($columnsConf),
                $columnsConf
            );
            return (object) [
                "key" => $key,
                "icon" => str_replace("tx_dbi_container_", "", $key),
                "label" => $conf[1][0],
                "description" => $conf[1][1],
                "grid" => [$columns],
                'showitems' => array_merge($standardFieldTypesForContainer, $conf[2])
            ];
        },
        $containerConfigurationKeys,
        $containerConfigurations
    );

    $containerRegistry = \TYPO3\CMS\Core\Utility\GeneralUtility::makeInstance(
        \B13\Container\Tca\Registry::class
    );

    foreach ($containerConfigurations as $container) {
        $containerRegistry->configureContainer(
            (
                new \B13\Container\Tca\ContainerConfiguration(
                    $container->key,
                    $container->label,
                    $container->description,
                    $container->grid
                )
            )->setIcon(
                "EXT:dbi/Resources/Public/Icons/Theme/Container/{$container->icon}.svg"
            )->setSaveAndCloseInNewContentElementWizard(true)
        );

        $GLOBALS['TCA']['tt_content']['types'][$container->key]['showitem'] = implode(",", $container->showitems);
    }
});

// Stage Container
call_user_func(static function () {
    $additionalFields = [
        "tx_dbi_stage_divider" => [
            'exclude' => 1,
            'label' => 'Hintergrundbild und Inhalt trennen',
            'config' => [
                'type' => 'check',
                'renderType' => 'checkboxToggle',
                'items' => [
                    [
                        'label' => 'Ja',
                    ],
                ],
            ],
            'onChange' => 'reload'
        ],
        'tx_dbi_stage_image' => [
            'exclude' => 1,
            'label' => 'Stagebild',
            'config' => \TYPO3\CMS\Core\Utility\ExtensionManagementUtility::getFileFieldTCAConfig(
                'image',
                [
                    'maxitems' => 1,
                ],
                $GLOBALS['TYPO3_CONF_VARS']['GFX']['imagefile_ext']
            ),
        ],
        "tx_dbi_stage_placeholder_widget_show" => [
            'exclude' => 1,
            'label' => 'Platzhalter Widget anzeigen',
            'config' => [
                'type' => 'check',
                'renderType' => 'checkboxToggle',
                'items' => [
                    [
                        'label' => 'Ja',
                    ],
                ],
            ],
            'onChange' => 'reload'
        ],
        "tx_dbi_stage_placeholder_widget_headline" => [
            'exclude' => 1,
            'label' => 'Platzhalter Widget Überschrift',
            'config' => [
                'type' => 'input',
            ],
            'displayCond' => 'FIELD:tx_dbi_stage_placeholder_widget_show:=:1'
        ],
        "tx_dbi_stage_placeholder_widget_subline" => [
            'exclude' => 1,
            'label' => 'Platzhalter Widget Subline',
            'config' => [
                'type' => 'input',
            ],
            'displayCond' => 'FIELD:tx_dbi_stage_placeholder_widget_show:=:1'
        ]
    ];

    \TYPO3\CMS\Core\Utility\ExtensionManagementUtility::addTCAcolumns(
        'tt_content',
        $additionalFields
    );

    $containerConfigurationKeys = [
        'tx_dbi_container_one_column',
        'tx_dbi_container_two_columns',
        'tx_dbi_container_two_columns_1_2',
        'tx_dbi_container_two_columns_2_1',
        'tx_dbi_container_three_columns',
        'tx_dbi_stage_container'
    ];

    $stageColumnConf = [
        'name' => 'Spalteninhalt ',
        'label' => "Stage Inhalt",
        'colPos' => 100,
        'disallowed' => [
            'CType' => implode(",", $containerConfigurationKeys)
        ]
    ];

    $containerRegistry = \TYPO3\CMS\Core\Utility\GeneralUtility::makeInstance(
        \B13\Container\Tca\Registry::class
    );

    // Stage Container
    $containerRegistry->configureContainer(
        (
            new \B13\Container\Tca\ContainerConfiguration(
                "tx_dbi_stage_container",
                "Stage Container",
                "",
                [
                    [
                        $stageColumnConf
                    ]
                ]
            )
        )->setIcon(
            "EXT:dbi/Resources/Public/Icons/Theme/Container/one_column.svg"
        )->setSaveAndCloseInNewContentElementWizard(true)
    );

    $standardFieldTypesForStage = [
        "CType",
        "tx_dbi_stage_image",
        "tx_dbi_stage_divider",
        "tx_dbi_stage_divide_slant",
        "tx_dbi_stage_placeholder_widget_show",
        "tx_dbi_stage_placeholder_widget_headline",
        "tx_dbi_stage_placeholder_widget_subline"
    ];

    $GLOBALS['TCA']['tt_content']['types']['tx_dbi_stage_container']['showitem'] = implode(",", $standardFieldTypesForStage);
});

// Table element
// Mask does not support table fields: https://github.com/Gernott/mask/issues/565
// the rendertype is removed everytime the mask element is saved
$GLOBALS['TCA']['tt_content']['columns']['tx_mask_table']['config']['renderType'] = 'textTable';

// CE Timeline
call_user_func(static function () {
    $GLOBALS['TCA']['tx_cetimeline_domain_model_entry']['types'][1]['showitem'] = str_replace(
        '--palette--;;lightbox,',
        '',
        $GLOBALS['TCA']['tx_cetimeline_domain_model_entry']['types'][1]['showitem']
    );

    $GLOBALS['TCA']['tx_cetimeline_domain_model_entry']['types'][1]['showitem'] = str_replace(
        '--palette--;;dimensions,',
        'media,',
        $GLOBALS['TCA']['tx_cetimeline_domain_model_entry']['types'][1]['showitem']
    );
});
