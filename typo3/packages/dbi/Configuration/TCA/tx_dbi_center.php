<?php

/**
 * Real estate center data
 * Immobiliencenter
 *
 * @link https://docs.typo3.org/m/typo3/reference-tca/12.4/en-us/
 */

return [
    'ctrl' => [
        'title' => 'Immobiliencenter',
        'label' => 'name2',
        'tstamp' => 'tstamp',
        'crdate' => 'crdate',

        'versioningWS' => true,

        'languageField' => 'sys_language_uid',
        'delete' => 'deleted',
        'enablecolumns' => [
            'disabled' => 'hidden',
            'starttime' => 'starttime',
            'endtime' => 'endtime',
        ],
        'rootLevel' => 0,

        'searchFields' => 'name2,fio_group_ids'
            . ',address_street,address_postalcode,address_city',
        'iconfile' => 'EXT:dbi/Resources/Public/Icons/Content/Center.svg',
        'security' => [
            'ignorePageTypeRestriction' => true,
        ],
    ],
    'types' => [
        '1' => [
            'showitem' => ''
                . ', name2'
                . ', --palette--;Adresse;address'
                . ', --palette--;Kontakt;contact'

                . ', --div--;Bilder'
                . ', image_pageheader'
                . ', image_mainpage'
                . ', image_search'
                . ', image_awards'

                . ', --div--;FIO Webmakler'
                . ', fio_group_ids'
                . ', fio_group_name'
                . ', fio_id'

                . ', --div--;Systemkonfiguration'
                . ', search_radius'
                . ', hide_in_search'
                . ', redirect'
                . ', is_headquarter'
                . ', --palette--;Geokoordinaten;geocoords'

                . ', --div--;Zugriff'
                . ', hidden, starttime, endtime'
        ],
    ],
    'palettes' => [
        '1' => ['showitem' => ''],
        'address' => [
            'showitem' => 'address_street'
                . ',--linebreak--, address_postalcode, address_city'
                . ',--linebreak--, address_state'
        ],
        'contact' => [
            'showitem' => 'telephone, fax, email'
                . ',--linebreak--, contact_openinghours'
        ],
        'geocoords' => [
            'showitem' => 'loc_lat, loc_long'
        ],
    ],

    'columns' => [
        't3ver_label' => [
            'label' => 'LLL:EXT:core/Resources/Private/Language/locallang_general.xlf:LGL.versionLabel',
            'config' => [
                'type' => 'input',
                'size' => 30,
                'max' => 255,
            ]
        ],

        'hidden' => [
            'exclude' => 1,
            'label' => 'LLL:EXT:core/Resources/Private/Language/locallang_general.xlf:LGL.hidden',
            'config' => [
                'type' => 'check',
            ],
        ],
        'starttime' => [
            'exclude' => 1,
            'label' => 'LLL:EXT:core/Resources/Private/Language/locallang_general.xlf:LGL.starttime',
            'config' => [
                'type' => 'datetime',
                'default' => '0',
                'behavior' => [
                    'allowLanguageSynchronization' => true,
                ],
            ],
        ],
        'endtime' => [
            'exclude' => 1,
            'label' => 'LLL:EXT:core/Resources/Private/Language/locallang_general.xlf:LGL.endtime',
            'config' => [
                'type' => 'datetime',
                'eval' => 'datetime',
                'default' => '0',
                'behavior' => [
                    'allowLanguageSynchronization' => true,
                ],
            ],
        ],

        'name2' => [
            'exclude' => 1,
            'label' => 'Name (Ort/Region)',
            'config' => [
                'type' => 'input',
                'required' => true,
                'size' => 30,
                'eval' => 'trim'
            ],
        ],
        'fio_id' => [
            'exclude' => 1,
            'label' => 'FIO-ID (alt, nicht mehr genutzt)',
            'config' => [
                'type' => 'input',
                'required' => false,
                'size' => 30,
                'eval' => 'trim'
            ],
        ],
        'fio_group_ids' => [
            'exclude' => 1,
            'label' => 'FIO Vertriebseinheit: Node IDs',
            'description' => "Kommagetrennte Nummern möglich.\nWird zur automatischen Zuordnung genutzt.\nAdressaktualisierung erfolgt nur von Teamleitern der ersten ID.",
            'config' => [
                'type' => 'input',
                'required' => true,
                'size' => 50,
                'max' => 64,
                'eval' => 'nospace,trim',
            ],
        ],
        'fio_group_name' => [
            'exclude' => 1,
            'label' => 'FIO Vertriebseinheit: Kurzname',
            'description' => 'Nur zur Info',
            'config' => [
                'type' => 'input',
                'size' => 30,
                'eval' => 'trim'
            ],
        ],

        'address_street' => [
            'exclude' => 1,
            'label' => 'Straße',
            'config' => [
                'type' => 'input',
                'size' => 30,
                'eval' => 'trim'
            ],
        ],
        'address_postalcode' => [
            'exclude' => 1,
            'label' => 'Postleitzahl',
            'config' => [
                'type' => 'input',
                'size' => 5,
                'eval' => 'trim'
            ],
        ],
        'address_city' => [
            'exclude' => 1,
            'label' => 'Ort',
            'config' => [
                'type' => 'input',
                'size' => 30,
                'eval' => 'trim'
            ],
        ],
        'address_state' => [
            'exclude' => 1,
            'label' => 'Bundesland',
            'config' => [
                'type' => 'select',
                'renderType' => 'selectSingle',
                'size' => 1,
                'minitems' => 0,
                'maxitems' => 1,
                'items' => [
                    ['label' => '- unbekannt -', 'value' => ''],
                    ['label' => 'Baden-Württemberg', 'value' => 'BW'],
                    ['label' => 'Bayern', 'value' => 'BY'],
                    ['label' => 'Berlin', 'value' => 'BE'],
                    ['label' => 'Brandenburg', 'value' => 'BB'],
                    ['label' => 'Bremen', 'value' => 'HB'],
                    ['label' => 'Hamburg', 'value' => 'HH'],
                    ['label' => 'Hessen', 'value' => 'HE'],
                    ['label' => 'Mecklenburg-Vorpommern', 'value' => 'MV'],
                    ['label' => 'Niedersachsen', 'value' => 'NI'],
                    ['label' => 'Nordrhein-Westfalen', 'value' => 'NW'],
                    ['label' => 'Rheinland-Pfalz', 'value' => 'RP'],
                    ['label' => 'Saarland', 'value' => 'SL'],
                    ['label' => 'Sachsen', 'value' => 'SN'],
                    ['label' => 'Sachsen-Anhalt', 'value' => 'ST'],
                    ['label' => 'Schleswig-Holstein', 'value' => 'SH'],
                    ['label' => 'Thüringen', 'value' => 'TH'],
                ],
            ],
        ],
        'telephone' => [
            'label' => 'Telefon',
            'config' => [
                'type' => 'input',
                'size' => 30,
                'eval' => 'trim'
            ],
        ],
        'fax' => [
            'label' => 'Fax',
            'config' => [
                'type' => 'input',
                'size' => 30,
                'eval' => 'trim'
            ],
        ],
        'email' => [
            'label' => 'E-Mail',
            'config' => [
                'type' => 'input',
                'size' => 30,
                'eval' => 'trim'
            ],
        ],
        'contact_openinghours' => [
            'label' => 'Öffnungszeiten',
            'config' => [
                'type' => 'text',
                'cols' => 40,
                'rows' => 5,
                'eval' => 'trim'
            ],
        ],

        'loc_lat' => [
            'label' => 'Breitengrad',
            'config' => [
                //"number:decimal" only supports 2 decimal places
                'type' => 'input',
                'eval' => 'trim,'
                    . \Mogic\Dbi\Evaluation\EmptyToNullEvaluation::class,
            ]
        ],
        'loc_long' => [
            'label' => 'Längengrad',
            'config' => [
                //"number:decimal" only supports 2 decimal places
                'type' => 'input',
                'eval' => 'trim,'
                    . \Mogic\Dbi\Evaluation\EmptyToNullEvaluation::class,
            ]
        ],

        'image_pageheader' => [
            'label' => 'Seitenkopfbild',
            'config' => [
                'type' => 'file',
                'allowed' => 'common-image-types',
                'maxitems' => 1,
            ],
        ],
        'image_mainpage' => [
            'label' => 'Bild für Hauptseite ("Kontakt")',
            'config' => [
                'type' => 'file',
                'allowed' => 'common-image-types',
                'maxitems' => 1,
            ],
        ],
        'image_search' => [
            'label' => 'Bild für Suchergebnisse',
            'config' => [
                'type' => 'file',
                'allowed' => 'common-image-types',
                'maxitems' => 1,
            ],
        ],
        'image_awards' => [
            'label' => 'Siegel-Logos / Auszeichnungen',
            'config' => [
                'type' => 'file',
                'allowed' => 'common-image-types',
                'maxitems' => 3,
            ],
        ],

        'search_radius' => [
            'label' => 'Umkreis Immobiliensuche in km',
            'config' => [
                'type' => 'number',
                'nullable' => true,
            ]
        ],

        'is_headquarter' => [
            'exclude' => 1,
            'label' => 'Ist dieses Center der Firmensitz?',
            'config' => [
                'type' => 'check',
                'default' => '0',
            ]
        ],

        'redirect' => [
            'exclude' => 1,
            'label' => 'Umleitung auf anderes Center',
            'config' => [
                'type' => 'select',
                'renderType' => 'selectSingle',
                'foreign_table' => 'tx_dbi_center',
                'foreign_table_where' => 'AND tx_dbi_center.uid != ###THIS_UID###'
                    . ' AND tx_dbi_center.is_headquarter = 0'
                    . ' ORDER BY tx_dbi_center.name2',
                'items' => [
                    ['label' => '- keine Umleitung -', 'value' => 0],
                ],
                'size' => 1,
                'minitems' => 0,
                'maxitems' => 1,
            ]
        ],

        'hide_in_search' => [
            'exclude' => 1,
            'label' => 'Nicht in der Maklersuche anzeigen',
            'config' => [
                'type' => 'check',
                'default' => '0',
            ]
        ],

    ],
];
