<?php

/**
 * Center team member
 *
 * @link https://docs.typo3.org/typo3cms/TCAReference/
 */

return [
    'ctrl' => [
        'title'     => 'Teammitglied',
        'label'     => 'name_lastname',
        'label_alt' => 'name_firstname',
        'label_alt_force' => true,
        'sortby' => 'sorting',
        'tstamp' => 'tstamp',
        'crdate' => 'crdate',

        'delete' => 'deleted',
        'enablecolumns' => [
            'disabled' => 'hidden',
            'starttime' => 'starttime',
            'endtime' => 'endtime',
        ],
        'rootLevel' => 0,

        'searchFields' => 'name_lastname,name_firstname,fio_username',
        'iconfile' => 'EXT:dbi/Resources/Public/Icons/Content/Teammember.svg',
        'security' => [
            'ignorePageTypeRestriction' => true,
        ],
    ],
    'types' => [
        '1' => [
            'showitem' => '--palette--;;intro'
                . ', --palette--;;name'
                . ', --palette--;Kontakt;contact'
                . ', --palette--;;jobdesc'
                . ', --palette--;;fio'
                . ', languages'
                . ', image'
                . ', email'
                . ', awards_maklerempfehlung_id'
                . ', awards_maklerempfehlung_hide'
                . ', awards_capital_image'
                . ', awards_capital_hide'
                . ', image_awards'

                . ', --div--;LLL:EXT:frontend/Resources/Private/Language/locallang_ttc.xlf:tabs.access'
                . ', hidden, starttime, endtime'
        ],
    ],
    'palettes' => [
        '1' => ['showitem' => ''],
        'intro' => [
            'showitem' => 'name_salutation, name_degree',
        ],
        'name' => [
            'showitem' => ''
                . ', name_firstname'
                . ', name_lastname'
        ],
        'contact' => [
            'showitem' => ''
                . ', contact_telephone'
                . ', contact_mobile'
                . ', contact_fax'
                . ', --linebreak--'
                . ', contact_email'
        ],
        'awards' => [
            'showitem' => 'awards_maklerempfehlung_id,awards_maklerempfehlung_hide,'
                . ' awards_capital_image, awards_capital_hide'
                . ', image_awards'
        ],
        'fio' => [
            'showitem' => 'fio_username'
                . ', fio_user_id'
                . ', fio_group_id'
        ],
        'jobdesc' => [
            'showitem' => 'function, identification',
        ],
    ],
    'columns' => [
        'hidden' => [
            'exclude' => 1,
            'label' => 'LLL:EXT:core/Resources/Private/Language/locallang_general.xlf:LGL.hidden',
            'config' => [
                'type' => 'check',
            ]
        ],
        'starttime' => [
            'label' => 'LLL:EXT:core/Resources/Private/Language/locallang_general.xlf:LGL.starttime',
            'config' => [
                'type' => 'datetime',
                'default' => 0,
                'behavior' => [
                    'allowLanguageSynchronization' => true,
                ],
            ],
        ],
        'endtime' => [
            'label' => 'LLL:EXT:core/Resources/Private/Language/locallang_general.xlf:LGL.endtime',
            'config' => [
                'type' => 'datetime',
                'default' => 0,
                'behavior' => [
                    'allowLanguageSynchronization' => true,
                ],
            ]
        ],

        'name_salutation' => [
            'label' => 'Anrede',
            'config' => [
                'type' => 'select',
                'renderType' => 'selectSingle',
                'items' => [
                    ['label' => 'Herr', 'value' => 'herr'],
                    ['label' => 'Frau', 'value' => 'frau']
                ],
                'required' => false,
                'default' => '',
                'size' => 1,
            ]
        ],
        'name_degree' => [
            'label' => 'Akademischer Grad',
            'config' => [
                'type' => 'select',
                'renderType' => 'selectSingle',
                'size' => 1,
                'items' => [
                    [
                        'value' => '',
                        'label' => '',
                    ],
                    [
                        'value' => 'dr',
                        'label' => 'LLL:EXT:dbi/Resources/Private/Language/locallang.xlf:dr',
                    ],
                    [
                        'value' => 'prof',
                        'label' => 'LLL:EXT:dbi/Resources/Private/Language/locallang.xlf:prof',
                    ],
                    [
                        'value' => 'profdr',
                        'label' => 'LLL:EXT:dbi/Resources/Private/Language/locallang.xlf:profdr',
                    ],
                ],
                'required' => false,
                'default' => '',
            ]
        ],
        'name_lastname' => [
            'label' => 'Nachname',
            'config' => [
                'type' => 'input',
                'required' => true,
                'size' => 30,
                'eval' => 'trim'
            ]
        ],
        'name_firstname' => [
            'label' => 'Vorname',
            'config' => [
                'type' => 'input',
                'required' => true,
                'size' => 30,
                'eval' => 'trim'
            ]
        ],
        'contact_telephone' => [
            'label' => 'Telefonnummer',
            'config' => [
                'type' => 'input',
                'required' => false,
                'size' => 30,
                'eval' => 'trim'
            ]
        ],
        'contact_mobile' => [
            'label' => 'Handy',
            'config' => [
                'type' => 'input',
                'size' => 30,
                'eval' => 'trim'
            ]
        ],
        'contact_fax' => [
            'label' => 'Fax',
            'config' => [
                'type' => 'input',
                'size' => 30,
                'eval' => 'trim'
            ]
        ],
        'contact_email' => [
            'label' => 'E-Mail',
            'config' => [
                'type' => 'email',
                'required' => false,
            ]
        ],
        'image' => [
            'label' => 'Bild',
            'config' => [
                'type' => 'file',
                'allowed' => 'common-image-types',
                'maxitems' => 1,
                'overrideChildTca' => [
                    'columns' => [
                        'crop' => [
                            'config' => [
                                'cropVariants' => [
                                    'default' => [
                                        'disabled' => true,
                                    ],
                                    'teammember' => [
                                        'title' => 'Teammitglied',
                                        'allowedAspectRatios' => [
                                            '1:1' => [
                                                'title' => 'LLL:EXT:core/Resources/Private/Language/locallang_wizards.xlf:imwizard.ratio.1_1',
                                                'value' => 1.0
                                            ],
                                        ],
                                        'selectedRatio' => '1:1',
                                    ],
                                ],
                            ],
                        ],
                    ],
                ],
            ],
        ],
        'function' => [
            'label' => 'Funktion',
            'config' => [
                'type' => 'input',
                'size' => 30,
                'eval' => 'trim'
            ]
        ],
        'identification' => [
            'label' => 'Selbständigkeit',
            'config' => [
                'type' => 'input',
                'size' => 30,
                'eval' => 'trim'
            ]
        ],
        'fio_username' => [
            'label' => 'FIO-Nutzername',
            'config' => [
                'type' => 'input',
                'size' => 30,
                'eval' => 'trim'
            ]
        ],
        'languages' => [
            'label' => 'Sprachen',
            'config' => [
                'type' => 'select',
                'renderType' => 'selectCheckBox',
                'multiple' => 1,
                'size' => 5,
                'items' => [
                    [
                        'value' => 'de',
                        'label' => 'LLL:EXT:dbi/Resources/Private/Language/locallang.xlf:de',
                        'icon'  => 'EXT:dbi/Resources/Public/Icons/Language/de.svg',
                    ],

                    [
                        'value' => 'sq',
                        'label' => 'LLL:EXT:dbi/Resources/Private/Language/locallang.xlf:sq',
                        'icon'  => 'EXT:dbi/Resources/Public/Icons/Language/sq.svg',
                    ],
                    [
                        'value' => 'ar',
                        'label' => 'LLL:EXT:dbi/Resources/Private/Language/locallang.xlf:ar',
                        'icon'  => 'EXT:dbi/Resources/Public/Icons/Language/ar.svg',
                    ],
                    [
                        'value' => 'bs',
                        'label' => 'LLL:EXT:dbi/Resources/Private/Language/locallang.xlf:bs',
                        'icon'  => 'EXT:dbi/Resources/Public/Icons/Language/bs.svg',
                    ],
                    [
                        'value' => 'zh',
                        'label' => 'LLL:EXT:dbi/Resources/Private/Language/locallang.xlf:zh',
                        'icon'  => 'EXT:dbi/Resources/Public/Icons/Language/zh.svg',
                    ],
                    [
                        'value' => 'dk',
                        'label' => 'LLL:EXT:dbi/Resources/Private/Language/locallang.xlf:dk',
                        'icon'  => 'EXT:dbi/Resources/Public/Icons/Language/dk.svg',
                    ],
                    [
                        'value' => 'en',
                        'label' => 'LLL:EXT:dbi/Resources/Private/Language/locallang.xlf:en',
                        'icon'  => 'EXT:dbi/Resources/Public/Icons/Language/en.svg',
                    ],
                    [
                        'value' => 'fi',
                        'label' => 'LLL:EXT:dbi/Resources/Private/Language/locallang.xlf:fi',
                        'icon'  => 'EXT:dbi/Resources/Public/Icons/Language/fi.svg',
                    ],
                    [
                        'value' => 'fr',
                        'label' => 'LLL:EXT:dbi/Resources/Private/Language/locallang.xlf:fr',
                        'icon'  => 'EXT:dbi/Resources/Public/Icons/Language/fr.svg',
                    ],
                    [
                        'value' => 'el',
                        'label' => 'LLL:EXT:dbi/Resources/Private/Language/locallang.xlf:el',
                        'icon'  => 'EXT:dbi/Resources/Public/Icons/Language/el.svg',
                    ],
                    [
                        'value' => 'it',
                        'label' => 'LLL:EXT:dbi/Resources/Private/Language/locallang.xlf:it',
                        'icon'  => 'EXT:dbi/Resources/Public/Icons/Language/it.svg',
                    ],
                    [
                        'value' => 'hr',
                        'label' => 'LLL:EXT:dbi/Resources/Private/Language/locallang.xlf:hr',
                        'icon'  => 'EXT:dbi/Resources/Public/Icons/Language/hr.svg',
                    ],
                    [
                        'value' => 'fa',
                        'label' => 'LLL:EXT:dbi/Resources/Private/Language/locallang.xlf:fa',
                        'icon'  => 'EXT:dbi/Resources/Public/Icons/Language/fa.svg',
                    ],
                    [
                        'value' => 'yue',
                        'label' => 'LLL:EXT:dbi/Resources/Private/Language/locallang.xlf:yue',
                        'icon'  => 'EXT:dbi/Resources/Public/Icons/Language/yue.svg',
                    ],
                    [
                        'value' => 'nl',
                        'label' => 'LLL:EXT:dbi/Resources/Private/Language/locallang.xlf:nl',
                        'icon'  => 'EXT:dbi/Resources/Public/Icons/Language/nl.svg',
                    ],
                    [
                        'value' => 'pl',
                        'label' => 'LLL:EXT:dbi/Resources/Private/Language/locallang.xlf:pl',
                        'icon'  => 'EXT:dbi/Resources/Public/Icons/Language/pl.svg',
                    ],
                    [
                        'value' => 'ro',
                        'label' => 'LLL:EXT:dbi/Resources/Private/Language/locallang.xlf:ro',
                        'icon'  => 'EXT:dbi/Resources/Public/Icons/Language/ro.svg',
                    ],
                    [
                        'value' => 'ru',
                        'label' => 'LLL:EXT:dbi/Resources/Private/Language/locallang.xlf:ru',
                        'icon'  => 'EXT:dbi/Resources/Public/Icons/Language/ru.svg',
                    ],
                    [
                        'value' => 'se',
                        'label' => 'LLL:EXT:dbi/Resources/Private/Language/locallang.xlf:se',
                        'icon'  => 'EXT:dbi/Resources/Public/Icons/Language/se.svg',
                    ],
                    [
                        'value' => 'sr',
                        'label' => 'LLL:EXT:dbi/Resources/Private/Language/locallang.xlf:sr',
                        'icon'  => 'EXT:dbi/Resources/Public/Icons/Language/sr.svg',
                    ],
                    [
                        'value' => 'es',
                        'label' => 'LLL:EXT:dbi/Resources/Private/Language/locallang.xlf:es',
                        'icon'  => 'EXT:dbi/Resources/Public/Icons/Language/es.svg',
                    ],
                    [
                        'value' => 'tr',
                        'label' => 'LLL:EXT:dbi/Resources/Private/Language/locallang.xlf:tr',
                        'icon'  => 'EXT:dbi/Resources/Public/Icons/Language/tr.svg',
                    ],
                    [
                        'value' => 'uk',
                        'label' => 'LLL:EXT:dbi/Resources/Private/Language/locallang.xlf:uk',
                        'icon'  => 'EXT:dbi/Resources/Public/Icons/Language/uk.svg',
                    ],
                    [
                        'value' => 'hu',
                        'label' => 'LLL:EXT:dbi/Resources/Private/Language/locallang.xlf:hu',
                        'icon'  => 'EXT:dbi/Resources/Public/Icons/Language/hu.svg',
                    ],
                ]
            ]
        ],
        'awards_maklerempfehlung_id' => [
            'label' => 'Maklerempfehlung: ID',
            'config' => [
                'type' => 'input',
                'size' => 30,
                'eval' => 'trim,num'
            ]
        ],
        'awards_maklerempfehlung_hide' => [
            'label' => 'Maklerempfehlung: Verstecken',
            'config' => [
                'type' => 'check'
            ]
        ],
        'awards_capital_image' => [
            'label' => 'Bild "Capital"',
            'config' => [
                'type' => 'file',
                'allowed' => 'common-image-types',
                'maxitems' => 1,
            ],
        ],
        'awards_capital_hide' => [
            'label' => '"Capital"-Bild verstecken',
            'config' => [
                'type' => 'check'
            ]
        ],
        'image_awards' => [
            'label' => 'Auszeichnungen',
            'config' => [
                'type' => 'file',
                'allowed' => 'common-image-types',
                'maxitems' => 1,
            ],
        ],

        'fio_user_id' => [
            'exclude' => 1,
            'label' => 'FIO-Nutzer-ID',
            'config' => [
                'type' => 'input',
                'required' => false,
                'size' => 30,
                'eval' => 'trim'
            ],
        ],

        'fio_group_id' => [
            'exclude' => 0,
            'label' => 'FIO-Node-ID',
            'config' => [
                'type' => 'input',
                'required' => false,
                'size' => 30,
                'eval' => 'trim'
            ],
        ],

        //just here for extbase entity mapping :(
        'deleted' => [
            'label' => 'LLL:EXT:core/Resources/Private/Language/locallang_general.xlf:LGL.deleted',
            'config' => [
                'type' => 'check',
            ]
        ],
        'tstamp' => [
            'label' => 'tstamp',
            'config' => [
                'type' => 'passthrough',
            ],
        ],
    ]
];
