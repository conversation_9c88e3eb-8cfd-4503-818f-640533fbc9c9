<?php

/**
 * Realtor trading areas
 *
 * @link https://docs.typo3.org/typo3cms/TCAReference/
 */
return [
    'ctrl' => [
        'title'     => 'Stadt',
        'label'     => 'city',
        'label_alt' => 'postalcode',
        'label_alt_force' => true,
        'sortby' => 'city',
        'tstamp' => 'tstamp',
        'crdate' => 'crdate',

        'delete' => 'deleted',
        'enablecolumns' => [
            'disabled' => 'hidden',
        ],
        'searchFields' => 'postalcode,city',
        'iconfile' => 'EXT:dbi/Resources/Public/Icons/Content/City.svg',

        'adminOnly' => true,
        'rootLevel' => 1,
        'readOnly' => true,
    ],
    'interface' => [
        'maxDBListItems' => 0,
    ],
    'types' => [
        '1' => [
            'showitem' => ', city, district, postalcode'
                . ', --div--;LLL:EXT:frontend/Resources/Private/Language/locallang_ttc.xlf:tabs.access'
                . ', hidden'
        ],
    ],
    'columns' => [
        'hidden' => [
            'exclude' => 1,
            'label' => 'LLL:EXT:core/Resources/Private/Language/locallang_general.xlf:LGL.hidden',
            'config' => [
                'type' => 'check',
            ]
        ],

        'city' => [
            'label' => 'Stadt',
            'config' => [
                'type' => 'input',
                'size' => 32,
                'required' => false,
                'eval' => 'trim',
            ]
        ],
        'district' => [
            'label' => 'Ortsteil',
            'config' => [
                'type' => 'input',
                'size' => 32,
                'required' => false,
                'eval' => 'trim',
            ]
        ],
        'postalcode' => [
            'label' => 'Postleitzahl',
            'config' => [
                'type' => 'input',
                'required' => true,
                'size' => 5,
                'eval' => 'trim',
            ]
        ],
    ]
];
