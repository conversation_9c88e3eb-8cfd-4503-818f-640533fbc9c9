<?php

/**
 * Realtor trading areas
 *
 * @link https://docs.typo3.org/typo3cms/TCAReference/
 */
return [
    'ctrl' => [
        'title'     => 'Vertriebsgebiet',
        'label'     => 'postalcode',
        'label_alt' => 'fio_username',
        'label_alt_force' => true,
        'sortby' => 'postalcode',
        'tstamp' => 'tstamp',
        'crdate' => 'crdate',

        'delete' => 'deleted',
        'enablecolumns' => [
            'disabled' => 'hidden',
        ],
        'searchFields' => 'postalcode,fio_username',
        'iconfile' => 'EXT:dbi/Resources/Public/Icons/Content/Tradingarea.svg',

        'rootLevel' => 1,
    ],
    'interface' => [
        'maxDBListItems' => 0,
    ],
    'types' => [
        '1' => [
            'showitem' => ', postalcode, population, fio_username'
                . ', --div--;LLL:EXT:frontend/Resources/Private/Language/locallang_ttc.xlf:tabs.access'
                . ', hidden'
        ],
    ],
    'columns' => [
        'hidden' => [
            'exclude' => 1,
            'label' => 'LLL:EXT:core/Resources/Private/Language/locallang_general.xlf:LGL.hidden',
            'config' => [
                'type' => 'check',
            ]
        ],

        'postalcode' => [
            'label' => 'Postleitzahl',
            'config' => [
                'type' => 'input',
                'required' => true,
                'size' => 5,
                'eval' => 'trim',
            ]
        ],
        'population' => [
            'label' => 'Einwohnerzahl',
            'config' => [
                'type' => 'input',
                'required' => false,
                'size' => 10,
                'eval' => 'trim'
            ]
        ],
        'fio_username' => [
            'label' => 'FIO-Nutzername',
            'config' => [
                'type' => 'input',
                'required' => false,
                'size' => 30,
                'eval' => 'trim'
            ]
        ],

        //this is not in the table itself, but we need it for automatic property
        // mapping for SQL selects across tradingarea and city.
        // Tradingarea::$city is otherwise always NULL
        'city' => [
            'label' => 'Stadt',
            'config' => [
                'type' => 'input',
                'size' => 32,
                'required' => false,
                'eval' => 'trim',
            ]
        ],
        'district' => [
            'label' => 'Ortsteil',
            'config' => [
                'type' => 'input',
                'size' => 32,
                'required' => false,
                'eval' => 'trim',
            ]
        ],
    ]
];
