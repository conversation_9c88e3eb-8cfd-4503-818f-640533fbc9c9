{"version": "8.1.0", "restructuringDone": true, "tables": {"tt_content": {"elements": {"textlink": {"color": "#0018a8", "colorOverlay": "#0018a8", "columns": ["header", "tx_mask_theme", "tx_mask_link", "tx_mask_icon"], "columnsOverride": {"tx_mask_icon": {"config": {"fieldWizard": {"selectIcons": {"disabled": 0}}, "fileFolderConfig": {"folder": "EXT:dbi/Resources/Public/Icons/Mask"}, "items": [{"description": "", "group": "", "icon": "", "label": "<PERSON><PERSON>", "value": ""}], "renderType": "selectSingle"}, "fullKey": "tx_mask_icon", "key": "icon", "type": "select"}, "tx_mask_theme": {"config": {"default": "dark-gray", "fieldWizard": {"selectIcons": {"disabled": 1}}, "items": [{"description": "", "group": "", "icon": "", "label": "Standard Textlink (DBI)", "value": "dark-gray"}, {"description": "", "group": "", "icon": "", "label": "Grauer Textlink", "value": "gray"}, {"description": "", "group": "", "icon": "", "label": "als Tag", "value": "tag_style"}], "renderType": "selectSingle"}, "fullKey": "tx_mask_theme", "key": "theme", "type": "select"}}, "description": "Textlink", "descriptions": ["", "", "", "Icon vor dem Link"], "icon": "", "iconOverlay": "", "key": "textlink", "label": "Textlink", "labels": ["Linktext", "<PERSON><PERSON><PERSON>", "Verlinkung", "Icon"], "shortLabel": "Textlink", "sorting": 2}}, "sql": {"tx_mask_icon": {"tt_content": {"tx_mask_icon": "varchar(255) DEFAULT '' NOT NULL"}}, "tx_mask_link": {"tt_content": {"tx_mask_link": "varchar(255) DEFAULT '' NOT NULL"}}, "tx_mask_theme": {"tt_content": {"tx_mask_theme": "varchar(255) DEFAULT '' NOT NULL"}}}, "tca": {"header": {"coreField": 1, "fullKey": "header", "key": "header", "type": "string"}, "tx_mask_icon": {"config": {"type": "select"}, "fullKey": "tx_mask_icon", "key": "icon", "type": "select"}, "tx_mask_link": {"config": {"nullable": 0, "type": "link"}, "fullKey": "tx_mask_link", "key": "link", "type": "link"}, "tx_mask_theme": {"config": {"type": "select"}, "fullKey": "tx_mask_theme", "key": "theme", "type": "select"}}}}}