{"version": "8.1.0", "restructuringDone": true, "tables": {"tt_content": {"elements": {"content_snippet": {"color": "#000000", "colorOverlay": "#000000", "columns": ["tx_mask_7636373359807", "tx_mask_theme", "bodytext", "tx_mask_icon", "tx_mask_orientation", "tx_mask_simplified", "tx_mask_buttons"], "columnsOverride": {"bodytext": {"config": {"wrap": "virtual"}, "fullKey": "bodytext", "key": "bodytext", "type": "richtext"}, "header_layout": {"config": {"default": "104", "fieldWizard": {"selectIcons": {"disabled": 1}}, "items": [], "renderType": "selectSingle"}, "fullKey": "header_layout", "key": "header_layout", "type": "select"}, "tx_mask_buttons": {"cTypes": ["mask_button"], "config": {"appearance": {"enabledControls": {"delete": 1, "dragdrop": 1, "hide": 1, "info": 1, "localize": 1, "new": 1, "sort": 1}, "levelLinksPosition": "top", "showAllLocalizationLink": 1, "showNewRecordLink": 1, "showPossibleLocalizationRecords": 1, "useSortable": 1}, "foreign_sortby": "sorting", "maxitems": "2", "overrideChildTca": {"columns": {"colPos": {"config": {"default": 999}}}}}, "fullKey": "tx_mask_buttons", "key": "buttons", "type": "content"}, "tx_mask_icon": {"config": {"fieldWizard": {"selectIcons": {"disabled": 0}}, "fileFolderConfig": {"folder": "EXT:dbi/Resources/Public/Icons/Mask"}, "items": [{"description": "", "group": "", "icon": "", "label": "<PERSON><PERSON>", "value": ""}], "renderType": "selectSingle"}, "fullKey": "tx_mask_icon", "key": "icon", "type": "select"}, "tx_mask_orientation": {"config": {"default": "vertical", "fieldWizard": {"selectIcons": {"disabled": 1}}, "items": [{"description": "", "group": "", "icon": "", "label": "vertical", "value": "vertical"}, {"description": "", "group": "", "icon": "", "label": "horizontal", "value": "horizontal"}], "renderType": "selectSingle"}, "fullKey": "tx_mask_orientation", "key": "orientation", "type": "select"}, "tx_mask_simplified": {"config": {"default": "0", "items": []}, "fullKey": "tx_mask_simplified", "key": "simplified", "type": "check"}, "tx_mask_theme": {"config": {"fieldWizard": {"selectIcons": {"disabled": 1}}, "items": [], "renderType": "selectSingle"}, "fullKey": "tx_mask_theme", "key": "theme", "type": "select"}}, "description": "Inhaltselement um z.B. Teaser oder Stageinhalt zu befüllen", "descriptions": ["", "", "", "Icon links neben dem Inhalt", "", "Zur Verwendung beispielsweise im Footer. Es zeigt nur das gewählte Icon und den Button als Textlink an", ""], "icon": "", "iconOverlay": "", "key": "content_snippet", "label": "Content Snippet", "labels": ["Überschriftbereich", "<PERSON><PERSON><PERSON>", "Fließtext", "Icon", "Ausrichtung", "vereinfachtes Content Snippet", "Verlinkungen"], "saveAndClose": 1, "shortLabel": "Content Snippet", "sorting": 1}}, "palettes": {"tx_mask_7636373359807": {"description": "", "label": "Überschriftbereich", "showitem": ["subheader", "tx_mask_06d1a8c02163b", "header", "header_layout"]}}, "sql": {"tx_mask_buttons": {"tt_content": {"tx_mask_buttons": "int(11) unsigned DEFAULT '0' NOT NULL"}}, "tx_mask_icon": {"tt_content": {"tx_mask_icon": "varchar(255) DEFAULT '' NOT NULL"}}, "tx_mask_orientation": {"tt_content": {"tx_mask_orientation": "varchar(255) DEFAULT '' NOT NULL"}}, "tx_mask_simplified": {"tt_content": {"tx_mask_simplified": "int(11) DEFAULT '0' NOT NULL"}}, "tx_mask_theme": {"tt_content": {"tx_mask_theme": "varchar(255) DEFAULT '' NOT NULL"}}}, "tca": {"bodytext": {"bodytextTypeByElement": {"center_header": "richtext", "center_info": "richtext", "content_snippet": "text", "personenbox": "richtext", "rating": "text", "stage": "text", "teaser": "richtext", "testimonial": "text", "text": "richtext"}, "coreField": 1, "fullKey": "bodytext", "key": "bodytext"}, "header": {"coreField": 1, "description": {"content_snippet": ""}, "fullKey": "header", "inPalette": 1, "inlineParent": {"content_snippet": "tx_mask_7636373359807"}, "key": "header", "label": {"content_snippet": "Überschrift"}, "order": {"content_snippet": 3}, "type": "string"}, "header_layout": {"coreField": 1, "description": {"content_snippet": ""}, "fullKey": "header_layout", "inPalette": 1, "inlineParent": {"content_snippet": "tx_mask_7636373359807"}, "key": "header_layout", "label": {"content_snippet": "Verhalten/Aussehen der Überschrift"}, "order": {"content_snippet": 4}, "type": "select"}, "subheader": {"coreField": 1, "description": {"content_snippet": ""}, "fullKey": "subheader", "inPalette": 1, "inlineParent": {"content_snippet": "tx_mask_7636373359807"}, "key": "subheader", "label": {"content_snippet": "Overline"}, "order": {"content_snippet": 1}, "type": "string"}, "tx_mask_06d1a8c02163b": {"config": {"type": "linebreak"}, "description": {"content_snippet": ""}, "fullKey": "tx_mask_06d1a8c02163b", "inPalette": 1, "inlineParent": {"content_snippet": "tx_mask_7636373359807"}, "key": "06d1a8c02163b", "label": {"content_snippet": ""}, "order": {"content_snippet": 2}, "type": "linebreak"}, "tx_mask_7636373359807": {"config": {"type": "palette"}, "fullKey": "tx_mask_7636373359807", "key": "7636373359807", "type": "palette"}, "tx_mask_buttons": {"cTypes": ["mask_button"], "config": {"foreign_table": "tt_content", "type": "inline"}, "fullKey": "tx_mask_buttons", "key": "buttons", "type": "content"}, "tx_mask_icon": {"config": {"type": "select"}, "fullKey": "tx_mask_icon", "key": "icon", "type": "select"}, "tx_mask_orientation": {"config": {"type": "select"}, "fullKey": "tx_mask_orientation", "key": "orientation", "type": "select"}, "tx_mask_simplified": {"config": {"type": "check"}, "fullKey": "tx_mask_simplified", "key": "simplified", "type": "check"}, "tx_mask_theme": {"config": {"type": "select"}, "fullKey": "tx_mask_theme", "key": "theme", "type": "select"}}}}}