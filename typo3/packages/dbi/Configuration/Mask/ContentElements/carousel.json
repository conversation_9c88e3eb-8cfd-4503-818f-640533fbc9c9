{"version": "8.1.0", "restructuringDone": true, "tables": {"tt_content": {"elements": {"carousel": {"color": "#000000", "colorOverlay": "#000000", "columns": ["tx_mask_24216d3458be3", "tx_mask_items_per_slide", "tx_mask_items"], "columnsOverride": {"tx_mask_items": {"cTypes": ["mask_text", "mask_content_tile", "mask_teaser", "mask_testimonial"], "config": {"appearance": {"enabledControls": {"delete": 1, "dragdrop": 1, "hide": 1, "info": 1, "localize": 1, "new": 1, "sort": 1}, "levelLinksPosition": "top", "showAllLocalizationLink": 1, "showNewRecordLink": 1, "showPossibleLocalizationRecords": 1, "useSortable": 1}, "foreign_sortby": "sorting", "minitems": "1", "overrideChildTca": {"columns": {"colPos": {"config": {"default": 999}}}}}, "fullKey": "tx_mask_items", "key": "items", "type": "content"}, "tx_mask_items_per_slide": {"config": {"autoSizeMax": "20", "default": "2 lg:3", "fieldWizard": {"selectIcons": {"disabled": 1}}, "items": [{"description": "", "group": "", "icon": "", "label": "Tablet: <PERSON><PERSON>, Desktop: d<PERSON><PERSON>", "value": "2 lg:3"}, {"description": "", "group": "", "icon": "", "label": "Tablet: <PERSON><PERSON>, Desktop: d<PERSON><PERSON>", "value": "1 lg:3"}, {"description": "", "group": "", "icon": "", "label": "Tablet: <PERSON><PERSON>, Desktop: zwei Spalten", "value": "1 lg:2"}, {"description": "", "group": "", "icon": "", "label": "Immer drei <PERSON>", "value": "3"}, {"description": "", "group": "", "icon": "", "label": "Immer zwei Spalten", "value": "2"}, {"description": "", "group": "", "icon": "", "label": "Immer eine Spalte", "value": "1"}], "renderType": "selectSingle"}, "fullKey": "tx_mask_items_per_slide", "key": "items_per_slide", "type": "select"}}, "description": "Carousel", "descriptions": ["", "Anzahl der Elementen auf Tablet und Desktop pro Slide.\nMobil immer eine Spalte.", ""], "icon": "", "iconOverlay": "", "key": "carousel", "label": "Carousel", "labels": ["Überschriftbreich", "Anzahl der Elemente Pro Slide", ""], "shortLabel": "", "sorting": 7}}, "palettes": {"tx_mask_24216d3458be3": {"description": "", "label": "Überschriftbreich", "showitem": ["header"]}}, "sql": {"tx_mask_items": {"tt_content": {"tx_mask_items": "int(11) unsigned DEFAULT '0' NOT NULL"}}, "tx_mask_items_per_slide": {"tt_content": {"tx_mask_items_per_slide": "varchar(255) DEFAULT '' NOT NULL"}}}, "tca": {"header": {"coreField": 1, "description": {"carousel": "Die Überschrift wird nur für das Backend genutzt."}, "fullKey": "header", "inPalette": 1, "inlineParent": {"carousel": "tx_mask_24216d3458be3"}, "key": "header", "label": {"carousel": ""}, "order": {"carousel": 1}, "type": "string"}, "tx_mask_24216d3458be3": {"config": {"type": "palette"}, "fullKey": "tx_mask_24216d3458be3", "key": "24216d3458be3", "type": "palette"}, "tx_mask_items": {"cTypes": ["mask_text", "mask_content_tile", "mask_teaser", "mask_testimonial", "mask_projekt_card"], "config": {"foreign_table": "tt_content", "type": "inline"}, "fullKey": "tx_mask_items", "key": "items", "type": "content"}, "tx_mask_items_per_slide": {"config": {"type": "select"}, "fullKey": "tx_mask_items_per_slide", "key": "items_per_slide", "type": "select"}}}}}