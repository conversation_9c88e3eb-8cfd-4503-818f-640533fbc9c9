{"version": "8.1.0", "restructuringDone": true, "tables": {"tt_content": {"elements": {"center_info": {"color": "#000000", "colorOverlay": "#000000", "columns": ["tx_mask_a9cc77a632465", "tx_mask_links"], "columnsOverride": {"bodytext": {"config": {"default": "Unser erfahrenes Makler-Team für den Raum ###name2### und darüber hinaus steht Ihnen jederzeit mit Rat und Tat zur Seite: Wir unterstützen Sie gern beim Verkauf Ihrer Immobilie - oder helfen Ihnen bei der Suche nach dem idealen Zuhause. Profitieren Sie jetzt von unseren vielseitigen Services! ", "enableRichtext": 1}, "fullKey": "bodytext", "key": "bodytext", "type": "richtext"}, "header": {"config": {"default": "Immer ausgezeichnet beraten", "required": 1}, "fullKey": "header", "key": "header", "type": "string"}, "header_layout": {"config": {"default": "102", "fieldWizard": {"selectIcons": {"disabled": 1}}, "items": [], "renderType": "selectSingle"}, "fullKey": "header_layout", "key": "header_layout", "type": "select"}, "subheader": {"config": {"default": "Willkommen in der Region"}, "fullKey": "subheader", "key": "subheader", "type": "string"}, "tx_mask_links": {"cTypes": ["mask_textlink"], "config": {"appearance": {"enabledControls": {"delete": 1, "dragdrop": 1, "hide": 1, "info": 1, "localize": 1, "new": 1, "sort": 1}, "levelLinksPosition": "top", "showAllLocalizationLink": 1, "showNewRecordLink": 1, "showPossibleLocalizationRecords": 1, "useSortable": 1}, "foreign_sortby": "sorting", "overrideChildTca": {"columns": {"colPos": {"config": {"default": 999}}}}}, "fullKey": "tx_mask_links", "key": "links", "type": "content"}}, "description": "<PERSON><PERSON><PERSON>mmobiliencenterinformationen wie Öffnungszeiten und Services", "descriptions": ["<PERSON><PERSON> könne<PERSON> im Text verwendet werden:\n###name2### (Name)\n###addressStreet### (Straße)\n###addressPostalcode### (PLZ)\n###addressCity### (Stadt)\n###addressState### (Landescode)\n###telephone### (Telefon)\n###fax### (Fax)\n###email### (E-Mail)\n###contactOpeninghours### (Öffnungzeiten)", ""], "icon": "", "iconOverlay": "", "key": "center_info", "label": "Immobiliencenter: Information", "labels": ["Textbereich", "Services"], "shortLabel": "Centerinfo", "sorting": 1}}, "palettes": {"tx_mask_a9cc77a632465": {"description": "<PERSON><PERSON> könne<PERSON> im Text verwendet werden:\n###name2### (Name)\n###addressStreet### (Straße)\n###addressPostalcode### (PLZ)\n###addressCity### (Stadt)\n###addressState### (Landescode)\n###telephone### (Telefon)\n###fax### (Fax)\n###email### (E-Mail)\n###contactOpeninghours### (Öffnungzeiten)", "label": "Textbereich", "showitem": ["header", "header_layout", "tx_mask_641785cf8baf8", "subheader", "tx_mask_c17ec87dadee9", "bodytext"]}}, "sql": {"tx_mask_links": {"tt_content": {"tx_mask_links": "int(11) unsigned DEFAULT '0' NOT NULL"}}}, "tca": {"bodytext": {"bodytextTypeByElement": {"center_header": "richtext", "center_info": "richtext", "content_snippet": "text", "personenbox": "richtext", "rating": "text", "stage": "text", "teaser": "richtext", "testimonial": "text", "text": "richtext"}, "coreField": 1, "description": {"center_info": ""}, "fullKey": "bodytext", "inPalette": 1, "inlineParent": {"center_info": "tx_mask_a9cc77a632465"}, "key": "bodytext", "label": {"center_info": ""}, "order": {"center_info": 6}}, "header": {"coreField": 1, "description": {"center_info": ""}, "fullKey": "header", "inPalette": 1, "inlineParent": {"center_info": "tx_mask_a9cc77a632465"}, "key": "header", "label": {"center_info": "Überschrift"}, "order": {"center_info": 1}, "type": "string"}, "header_layout": {"coreField": 1, "description": {"center_info": ""}, "fullKey": "header_layout", "inPalette": 1, "inlineParent": {"center_info": "tx_mask_a9cc77a632465"}, "key": "header_layout", "label": {"center_info": "Verhalten/Aussehen der Überschrift"}, "order": {"center_info": 2}, "type": "select"}, "subheader": {"coreField": 1, "description": {"center_info": ""}, "fullKey": "subheader", "inPalette": 1, "inlineParent": {"center_info": "tx_mask_a9cc77a632465"}, "key": "subheader", "label": {"center_info": ""}, "order": {"center_info": 4}, "type": "string"}, "tx_mask_641785cf8baf8": {"config": {"type": "linebreak"}, "description": {"center_info": ""}, "fullKey": "tx_mask_641785cf8baf8", "inPalette": 1, "inlineParent": {"center_info": "tx_mask_a9cc77a632465"}, "key": "641785cf8baf8", "label": {"center_info": ""}, "order": {"center_info": 3}, "type": "linebreak"}, "tx_mask_a9cc77a632465": {"config": {"type": "palette"}, "fullKey": "tx_mask_a9cc77a632465", "key": "a9cc77a632465", "type": "palette"}, "tx_mask_c17ec87dadee9": {"config": {"type": "linebreak"}, "description": {"center_info": ""}, "fullKey": "tx_mask_c17ec87dadee9", "inPalette": 1, "inlineParent": {"center_info": "tx_mask_a9cc77a632465"}, "key": "c17ec87dadee9", "label": {"center_info": ""}, "order": {"center_info": 5}, "type": "linebreak"}, "tx_mask_links": {"cTypes": ["mask_textlink"], "config": {"foreign_table": "tt_content", "type": "inline"}, "fullKey": "tx_mask_links", "key": "links", "type": "content"}}}}}