{"version": "8.1.0", "restructuringDone": true, "tables": {"tt_content": {"elements": {"gallery": {"color": "#000000", "colorOverlay": "#000000", "columns": ["tx_mask_4da42d39e4c8e", "tx_mask_theme", "assets"], "columnsOverride": {"assets": {"config": {"appearance": {"elementBrowserEnabled": 1, "enabledControls": {"delete": 1, "dragdrop": 1, "hide": 1, "info": 1, "localize": 1, "sort": 0}, "fileByUrlAllowed": 1, "fileUploadAllowed": 1, "useSortable": 1}, "minitems": "2"}, "fullKey": "assets", "key": "assets", "type": "media"}, "header_layout": {"config": {"fieldWizard": {"selectIcons": {"disabled": 1}}, "items": [], "renderType": "selectSingle"}, "fullKey": "header_layout", "key": "header_layout", "type": "select"}, "tx_mask_theme": {"config": {"default": "default", "fieldWizard": {"selectIcons": {"disabled": 1}}, "items": [{"description": "", "group": "", "icon": "", "label": "Standard", "value": "default"}, {"description": "", "group": "", "icon": "", "label": "Standard (<PERSON> <PERSON><PERSON><PERSON><PERSON>)", "value": "default-with-thumbs"}], "renderType": "selectSingle"}, "fullKey": "tx_mask_theme", "key": "theme", "type": "select"}}, "description": "Eine Bildergalerie", "descriptions": ["", "", ""], "icon": "", "iconOverlay": "", "key": "gallery", "label": "Bildergalerie", "labels": ["Überschriftbereich", "Aussehen / Verhalten", "Bilder"], "shortLabel": "Gallery", "sorting": 1}}, "palettes": {"tx_mask_4da42d39e4c8e": {"description": "", "label": "Überschriftbereich", "showitem": ["subheader", "tx_mask_509ac54a19bd", "header", "header_layout"]}}, "sql": {"tx_mask_theme": {"tt_content": {"tx_mask_theme": "varchar(255) DEFAULT '' NOT NULL"}}}, "tca": {"assets": {"coreField": 1, "fullKey": "assets", "key": "assets", "type": "media"}, "header": {"coreField": 1, "description": {"gallery": ""}, "fullKey": "header", "inPalette": 1, "inlineParent": {"gallery": "tx_mask_4da42d39e4c8e"}, "key": "header", "label": {"gallery": ""}, "order": {"gallery": 3}, "type": "string"}, "header_layout": {"coreField": 1, "description": {"gallery": ""}, "fullKey": "header_layout", "inPalette": 1, "inlineParent": {"gallery": "tx_mask_4da42d39e4c8e"}, "key": "header_layout", "label": {"gallery": ""}, "order": {"gallery": 4}, "type": "select"}, "subheader": {"coreField": 1, "description": {"gallery": ""}, "fullKey": "subheader", "inPalette": 1, "inlineParent": {"gallery": "tx_mask_4da42d39e4c8e"}, "key": "subheader", "label": {"gallery": ""}, "order": {"gallery": 1}, "type": "string"}, "tx_mask_4da42d39e4c8e": {"config": {"type": "palette"}, "fullKey": "tx_mask_4da42d39e4c8e", "key": "4da42d39e4c8e", "type": "palette"}, "tx_mask_509ac54a19bd": {"config": {"type": "linebreak"}, "description": {"gallery": ""}, "fullKey": "tx_mask_509ac54a19bd", "inPalette": 1, "inlineParent": {"gallery": "tx_mask_4da42d39e4c8e"}, "key": "509ac54a19bd", "label": {"gallery": ""}, "order": {"gallery": 2}, "type": "linebreak"}, "tx_mask_theme": {"config": {"type": "select"}, "fullKey": "tx_mask_theme", "key": "theme", "type": "select"}}}}}