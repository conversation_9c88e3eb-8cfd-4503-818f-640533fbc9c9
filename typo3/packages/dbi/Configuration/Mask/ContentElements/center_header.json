{"version": "8.1.0", "restructuringDone": true, "tables": {"tt_content": {"elements": {"center_header": {"color": "#000000", "colorOverlay": "#000000", "columns": ["tx_mask_febc676d44144"], "columnsOverride": {"bodytext": {"config": {"enableRichtext": 1}, "fullKey": "bodytext", "key": "bodytext", "type": "richtext"}, "header": {"config": {"default": "Ihre Immobilienmakler im Raum ###name2###", "required": 1}, "fullKey": "header", "key": "header", "type": "string"}, "header_layout": {"config": {"default": "101", "fieldWizard": {"selectIcons": {"disabled": 1}}, "items": [], "renderType": "selectSingle"}, "fullKey": "header_layout", "key": "header_layout", "type": "select"}}, "description": "Centerüberschrift, Bild und Introtext", "descriptions": ["<PERSON><PERSON> könne<PERSON> im Text verwendet werden:\n###name2### (Name)\n###addressStreet### (Straße)\n###addressPostalcode### (PLZ)\n###addressCity### (Stadt)\n###addressState### (Landescode)\n###telephone### (Telefon)\n###fax### (Fax)\n###email### (E-Mail)\n###contactOpeninghours### (Öffnungzeiten)"], "icon": "", "iconOverlay": "", "key": "center_header", "label": "Immobiliencenter: <PERSON><PERSON><PERSON>ei<PERSON>", "labels": ["Textbreich"], "shortLabel": "Centerheader", "sorting": 3}}, "palettes": {"tx_mask_febc676d44144": {"description": "<PERSON><PERSON> könne<PERSON> im Text verwendet werden:\n###name2### (Name)\n###addressStreet### (Straße)\n###addressPostalcode### (PLZ)\n###addressCity### (Stadt)\n###addressState### (Landescode)\n###telephone### (Telefon)\n###fax### (Fax)\n###email### (E-Mail)\n###contactOpeninghours### (Öffnungzeiten)", "label": "Textbreich", "showitem": ["header", "header_layout", "tx_mask_aba061215a614", "bodytext"]}}, "tca": {"bodytext": {"bodytextTypeByElement": {"center_header": "richtext", "center_info": "richtext", "content_snippet": "text", "personenbox": "richtext", "rating": "text", "stage": "text", "teaser": "richtext", "testimonial": "text", "text": "richtext"}, "coreField": 1, "description": {"center_header": ""}, "fullKey": "bodytext", "inPalette": 1, "inlineParent": {"center_header": "tx_mask_febc676d44144"}, "key": "bodytext", "label": {"center_header": ""}, "order": {"center_header": 4}}, "header": {"coreField": 1, "description": {"center_header": ""}, "fullKey": "header", "inPalette": 1, "inlineParent": {"center_header": "tx_mask_febc676d44144"}, "key": "header", "label": {"center_header": ""}, "order": {"center_header": 1}, "type": "string"}, "header_layout": {"coreField": 1, "description": {"center_header": ""}, "fullKey": "header_layout", "inPalette": 1, "inlineParent": {"center_header": "tx_mask_febc676d44144"}, "key": "header_layout", "label": {"center_header": ""}, "order": {"center_header": 2}, "type": "select"}, "tx_mask_aba061215a614": {"config": {"type": "linebreak"}, "description": {"center_header": ""}, "fullKey": "tx_mask_aba061215a614", "inPalette": 1, "inlineParent": {"center_header": "tx_mask_febc676d44144"}, "key": "aba061215a614", "label": {"center_header": ""}, "order": {"center_header": 3}, "type": "linebreak"}, "tx_mask_febc676d44144": {"config": {"type": "palette"}, "fullKey": "tx_mask_febc676d44144", "key": "febc676d44144", "type": "palette"}}}}}