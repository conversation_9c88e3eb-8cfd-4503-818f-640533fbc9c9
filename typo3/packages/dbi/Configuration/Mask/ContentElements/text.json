{"version": "8.1.0", "restructuringDone": true, "tables": {"tt_content": {"elements": {"text": {"color": "#0018a8", "colorOverlay": "#0018a8", "columns": ["tx_mask_23066b4cbbf32", "bodytext"], "columnsOverride": {"bodytext": {"config": {"enableRichtext": 1}, "fullKey": "bodytext", "key": "bodytext", "type": "richtext"}, "header_layout": {"config": {"default": "100", "fieldWizard": {"selectIcons": {"disabled": 1}}, "items": [], "renderType": "selectSingle"}, "fullKey": "header_layout", "key": "header_layout", "type": "select"}, "tx_mask_overline": {"config": {"items": [], "renderType": "checkboxToggle"}, "fullKey": "tx_mask_overline", "key": "overline", "type": "check"}}, "description": "Ein Textelement mit Überschriftbereich", "descriptions": ["", ""], "icon": "", "iconOverlay": "", "key": "text", "label": "Text", "labels": ["Überschriftsbereich", "Fließtext"], "shortLabel": "Text", "sorting": 7}}, "palettes": {"tx_mask_23066b4cbbf32": {"description": "", "label": "Überschriftsbereich", "showitem": ["header", "tx_mask_5656e08e6a46", "header_layout", "tx_mask_fe9f13169da57", "subheader", "tx_mask_59d78ec68f777", "tx_mask_overline"]}}, "sql": {"tx_mask_overline": {"tt_content": {"tx_mask_overline": "int(11) DEFAULT '0' NOT NULL"}}}, "tca": {"bodytext": {"bodytextTypeByElement": {"center_header": "richtext", "center_info": "richtext", "content_snippet": "text", "personenbox": "richtext", "rating": "text", "stage": "text", "teaser": "richtext", "testimonial": "text", "text": "richtext"}, "coreField": 1, "fullKey": "bodytext", "key": "bodytext"}, "header": {"coreField": 1, "description": {"text": ""}, "fullKey": "header", "inPalette": 1, "inlineParent": {"text": "tx_mask_23066b4cbbf32"}, "key": "header", "label": {"text": ""}, "order": {"text": 1}, "type": "string"}, "header_layout": {"coreField": 1, "description": {"text": ""}, "fullKey": "header_layout", "inPalette": 1, "inlineParent": {"text": "tx_mask_23066b4cbbf32"}, "key": "header_layout", "label": {"text": ""}, "order": {"text": 3}, "type": "select"}, "subheader": {"coreField": 1, "description": {"text": ""}, "fullKey": "subheader", "inPalette": 1, "inlineParent": {"text": "tx_mask_23066b4cbbf32"}, "key": "subheader", "label": {"text": ""}, "order": {"text": 5}, "type": "string"}, "tx_mask_23066b4cbbf32": {"config": {"type": "palette"}, "fullKey": "tx_mask_23066b4cbbf32", "key": "23066b4cbbf32", "type": "palette"}, "tx_mask_5656e08e6a46": {"config": {"type": "linebreak"}, "description": {"text": ""}, "fullKey": "tx_mask_5656e08e6a46", "inPalette": 1, "inlineParent": {"text": "tx_mask_23066b4cbbf32"}, "key": "5656e08e6a46", "label": {"text": ""}, "order": {"text": 2}, "type": "linebreak"}, "tx_mask_59d78ec68f777": {"config": {"type": "linebreak"}, "description": {"text": ""}, "fullKey": "tx_mask_59d78ec68f777", "inPalette": 1, "inlineParent": {"text": "tx_mask_23066b4cbbf32"}, "key": "59d78ec68f777", "label": {"text": ""}, "order": {"text": 6}, "type": "linebreak"}, "tx_mask_fe9f13169da57": {"config": {"type": "linebreak"}, "description": {"text": ""}, "fullKey": "tx_mask_fe9f13169da57", "inPalette": 1, "inlineParent": {"text": "tx_mask_23066b4cbbf32"}, "key": "fe9f13169da57", "label": {"text": ""}, "order": {"text": 4}, "type": "linebreak"}, "tx_mask_overline": {"config": {"type": "check"}, "description": {"text": ""}, "fullKey": "tx_mask_overline", "inPalette": 1, "inlineParent": {"text": "tx_mask_23066b4cbbf32"}, "key": "overline", "label": {"text": "Untertitel als Zeile darüber"}, "order": {"text": 7}, "type": "check"}}}}}