{"version": "8.1.0", "restructuringDone": true, "tables": {"tt_content": {"elements": {"media": {"color": "#0018a8", "colorOverlay": "#0018a8", "columns": ["tx_mask_1e7c52ad4c5db", "assets", "tx_mask_orientation"], "columnsOverride": {"assets": {"allowedFileExtensions": "common-media-types", "config": {"appearance": {"elementBrowserEnabled": 1, "enabledControls": {"delete": 1, "dragdrop": 1, "hide": 1, "info": 1, "localize": 1, "sort": 0}, "fileByUrlAllowed": 1, "fileUploadAllowed": 1, "useSortable": 1}, "minitems": ""}, "fullKey": "assets", "key": "assets", "onlineMedia": ["youtube", "vimeo"], "type": "media"}, "header_layout": {"config": {"fieldWizard": {"selectIcons": {"disabled": 1}}, "items": [], "renderType": "selectSingle"}, "fullKey": "header_layout", "key": "header_layout", "type": "select"}, "subheader": {"config": {"placeholder": "100"}, "fullKey": "subheader", "key": "subheader", "type": "string"}, "tx_mask_orientation": {"config": {"default": "row", "fieldWizard": {"selectIcons": {"disabled": 1}}, "items": [{"description": "", "group": "", "icon": "", "label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "col"}, {"description": "", "group": "", "icon": "", "label": "<PERSON><PERSON><PERSON><PERSON>", "value": "row"}], "renderType": "selectSingle"}, "fullKey": "tx_mask_orientation", "key": "orientation", "type": "select"}, "tx_mask_overline": {"config": {"items": [], "renderType": "checkboxToggle"}, "fullKey": "tx_mask_overline", "key": "overline", "type": "check"}}, "description": "Medien (Bilder, Videos) mit Überschriftenbereich", "descriptions": ["", "", ""], "icon": "", "iconOverlay": "", "key": "media", "label": "Medien", "labels": ["", "Bilder", "Ausrichtung"], "shortLabel": "Medien", "sorting": 1}}, "palettes": {"tx_mask_1e7c52ad4c5db": {"description": "", "label": "", "showitem": ["header", "tx_mask_8d36f909c3b8c", "header_layout", "tx_mask_75f5ee1fdd93c", "subheader", "tx_mask_b9e43c3800a7a", "tx_mask_overline"]}}, "sql": {"tx_mask_orientation": {"tt_content": {"tx_mask_orientation": "varchar(255) DEFAULT '' NOT NULL"}}, "tx_mask_overline": {"tt_content": {"tx_mask_overline": "int(11) DEFAULT '0' NOT NULL"}}}, "tca": {"assets": {"coreField": 1, "fullKey": "assets", "key": "assets", "type": "media"}, "header": {"coreField": 1, "description": {"media": ""}, "fullKey": "header", "inPalette": 1, "inlineParent": {"media": "tx_mask_1e7c52ad4c5db"}, "key": "header", "label": {"media": ""}, "order": {"media": 1}, "type": "string"}, "header_layout": {"coreField": 1, "description": {"media": ""}, "fullKey": "header_layout", "inPalette": 1, "inlineParent": {"media": "tx_mask_1e7c52ad4c5db"}, "key": "header_layout", "label": {"media": ""}, "order": {"media": 3}, "type": "select"}, "subheader": {"coreField": 1, "description": {"media": ""}, "fullKey": "subheader", "inPalette": 1, "inlineParent": {"media": "tx_mask_1e7c52ad4c5db"}, "key": "subheader", "label": {"media": ""}, "order": {"media": 5}, "type": "string"}, "tx_mask_1e7c52ad4c5db": {"config": {"type": "palette"}, "fullKey": "tx_mask_1e7c52ad4c5db", "key": "1e7c52ad4c5db", "type": "palette"}, "tx_mask_75f5ee1fdd93c": {"config": {"type": "linebreak"}, "description": {"media": ""}, "fullKey": "tx_mask_75f5ee1fdd93c", "inPalette": 1, "inlineParent": {"media": "tx_mask_1e7c52ad4c5db"}, "key": "75f5ee1fdd93c", "label": {"media": ""}, "order": {"media": 4}, "type": "linebreak"}, "tx_mask_8d36f909c3b8c": {"config": {"type": "linebreak"}, "description": {"media": ""}, "fullKey": "tx_mask_8d36f909c3b8c", "inPalette": 1, "inlineParent": {"media": "tx_mask_1e7c52ad4c5db"}, "key": "8d36f909c3b8c", "label": {"media": ""}, "order": {"media": 2}, "type": "linebreak"}, "tx_mask_b9e43c3800a7a": {"config": {"type": "linebreak"}, "description": {"media": ""}, "fullKey": "tx_mask_b9e43c3800a7a", "inPalette": 1, "inlineParent": {"media": "tx_mask_1e7c52ad4c5db"}, "key": "b9e43c3800a7a", "label": {"media": ""}, "order": {"media": 6}, "type": "linebreak"}, "tx_mask_orientation": {"config": {"type": "select"}, "fullKey": "tx_mask_orientation", "key": "orientation", "type": "select"}, "tx_mask_overline": {"config": {"type": "check"}, "description": {"media": ""}, "fullKey": "tx_mask_overline", "inPalette": 1, "inlineParent": {"media": "tx_mask_1e7c52ad4c5db"}, "key": "overline", "label": {"media": "Untertitel als Zeile darüber"}, "order": {"media": 7}, "type": "check"}}}}}