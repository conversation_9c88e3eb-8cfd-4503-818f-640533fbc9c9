{"version": "8.1.0", "restructuringDone": true, "tables": {"tt_content": {"elements": {"teaser": {"color": "#000000", "colorOverlay": "#000000", "columns": ["tx_mask_theme", "tx_mask_orientation", "assets", "tx_mask_4a53d28936a4b", "bodytext", "tx_mask_buttons"], "columnsOverride": {"assets": {"allowedFileExtensions": "jpg,jpeg,png,svg,webp", "config": {"appearance": {"elementBrowserEnabled": 1, "enabledControls": {"delete": 1, "dragdrop": 1, "hide": 1, "info": 1, "localize": 1, "sort": 0}, "fileByUrlAllowed": 1, "fileUploadAllowed": 1, "useSortable": 1}, "minitems": ""}, "fullKey": "assets", "key": "assets", "type": "media"}, "bodytext": {"config": {"enableRichtext": 1}, "fullKey": "bodytext", "key": "bodytext", "type": "richtext"}, "header_layout": {"config": {"fieldWizard": {"selectIcons": {"disabled": 1}}, "items": [], "renderType": "selectSingle"}, "fullKey": "header_layout", "key": "header_layout", "type": "select"}, "tx_mask_buttons": {"cTypes": ["mask_button"], "config": {"appearance": {"enabledControls": {"delete": 1, "dragdrop": 1, "hide": 1, "info": 1, "localize": 1, "new": 1, "sort": 1}, "levelLinksPosition": "top", "showAllLocalizationLink": 1, "showNewRecordLink": 1, "showPossibleLocalizationRecords": 1, "useSortable": 1}, "foreign_sortby": "sorting", "maxitems": "2", "overrideChildTca": {"columns": {"colPos": {"config": {"default": 999}}}}}, "fullKey": "tx_mask_buttons", "key": "buttons", "type": "content"}, "tx_mask_orientation": {"config": {"default": "column", "fieldWizard": {"selectIcons": {"disabled": 1}}, "items": [{"description": "", "group": "", "icon": "", "label": "Bild (oben) Inhalt (darunter)", "value": "column"}, {"description": "", "group": "", "icon": "", "label": "Bild (links) Inhalt (rechts)", "value": "row"}, {"description": "", "group": "", "icon": "", "label": "Bild (rechts) Inhalt (links)", "value": "row-reverse"}], "renderType": "selectSingle"}, "fullKey": "tx_mask_orientation", "key": "orientation", "type": "select"}, "tx_mask_theme": {"config": {"default": "brand", "fieldWizard": {"selectIcons": {"disabled": 1}}, "items": [], "renderType": "selectSingle"}, "fullKey": "tx_mask_theme", "key": "theme", "type": "select"}}, "description": "Teaser mit unterschiedlichen Bildpositionen", "descriptions": ["", "", "", "", "", ""], "icon": "", "iconOverlay": "", "key": "teaser", "label": "Teaser", "labels": ["<PERSON><PERSON><PERSON>", "Ausrichtung", "", "Überschriftbereich", "", "Verlinkungen"], "shortLabel": "", "sorting": 1}}, "palettes": {"tx_mask_4a53d28936a4b": {"description": "", "label": "Überschriftbereich", "showitem": ["subheader", "tx_mask_6f44d490d331d", "header", "header_layout"]}}, "sql": {"tx_mask_buttons": {"tt_content": {"tx_mask_buttons": "int(11) unsigned DEFAULT '0' NOT NULL"}}, "tx_mask_orientation": {"tt_content": {"tx_mask_orientation": "varchar(255) DEFAULT '' NOT NULL"}}, "tx_mask_theme": {"tt_content": {"tx_mask_theme": "varchar(255) DEFAULT '' NOT NULL"}}}, "tca": {"assets": {"coreField": 1, "fullKey": "assets", "key": "assets", "type": "media"}, "bodytext": {"bodytextTypeByElement": {"center_header": "richtext", "center_info": "richtext", "content_snippet": "text", "personenbox": "richtext", "rating": "text", "stage": "text", "teaser": "richtext", "testimonial": "text", "text": "richtext"}, "coreField": 1, "fullKey": "bodytext", "key": "bodytext"}, "header": {"coreField": 1, "description": {"teaser": ""}, "fullKey": "header", "inPalette": 1, "inlineParent": {"teaser": "tx_mask_4a53d28936a4b"}, "key": "header", "label": {"teaser": "Überschrift"}, "order": {"teaser": 3}, "type": "string"}, "header_layout": {"coreField": 1, "description": {"teaser": ""}, "fullKey": "header_layout", "inPalette": 1, "inlineParent": {"teaser": "tx_mask_4a53d28936a4b"}, "key": "header_layout", "label": {"teaser": "Verhalten/Aussehen der Überschrift"}, "order": {"teaser": 4}, "type": "select"}, "subheader": {"coreField": 1, "description": {"teaser": ""}, "fullKey": "subheader", "inPalette": 1, "inlineParent": {"teaser": "tx_mask_4a53d28936a4b"}, "key": "subheader", "label": {"teaser": "Overline"}, "order": {"teaser": 1}, "type": "string"}, "tx_mask_4a53d28936a4b": {"config": {"type": "palette"}, "fullKey": "tx_mask_4a53d28936a4b", "key": "4a53d28936a4b", "type": "palette"}, "tx_mask_6f44d490d331d": {"config": {"type": "linebreak"}, "description": {"teaser": ""}, "fullKey": "tx_mask_6f44d490d331d", "inPalette": 1, "inlineParent": {"teaser": "tx_mask_4a53d28936a4b"}, "key": "6f44d490d331d", "label": {"teaser": ""}, "order": {"teaser": 2}, "type": "linebreak"}, "tx_mask_buttons": {"cTypes": ["mask_button"], "config": {"foreign_table": "tt_content", "type": "inline"}, "fullKey": "tx_mask_buttons", "key": "buttons", "type": "content"}, "tx_mask_orientation": {"config": {"type": "select"}, "fullKey": "tx_mask_orientation", "key": "orientation", "type": "select"}, "tx_mask_theme": {"config": {"type": "select"}, "fullKey": "tx_mask_theme", "key": "theme", "type": "select"}}}}}