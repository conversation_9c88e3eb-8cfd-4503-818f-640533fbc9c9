{"version": "8.1.0", "restructuringDone": true, "tables": {"tt_content": {"elements": {"center_team": {"color": "#000000", "colorOverlay": "#000000", "columns": ["tx_mask_18901a94c95c8", "tx_mask_grid_config", "tx_mask_nav_title", "tx_mask_records"], "columnsOverride": {"header_layout": {"config": {"fieldWizard": {"selectIcons": {"disabled": 1}}, "items": [], "renderType": "selectSingle"}, "fullKey": "header_layout", "key": "header_layout", "type": "select"}, "tx_mask_grid_config": {"config": {"default": "1", "fieldWizard": {"selectIcons": {"disabled": 1}}, "items": [{"description": "", "group": "", "icon": "", "label": "1 Spalte", "value": "1"}, {"description": "", "group": "", "icon": "", "label": "Desktop: 2 Spalten (Mobile, Tablet Portait: 1 Spalte)", "value": "2"}, {"description": "", "group": "", "icon": "", "label": "Desktop: 3 Spalten (Mobile, Tablet Portait: 1 Spalte)", "value": "3"}], "renderType": "selectSingle"}, "fullKey": "tx_mask_grid_config", "key": "grid_config", "type": "select"}, "tx_mask_nav_title": {"config": {"mode": "useOrOverridePlaceholder"}, "fullKey": "tx_mask_nav_title", "key": "nav_title", "type": "string"}, "tx_mask_records": {"config": {"autoSizeMax": "10", "fieldControl": {"addRecord": {"disabled": 1}, "editPopup": {"disabled": 1}, "listModule": {"disabled": 1}}, "multiple": 0}, "fullKey": "tx_mask_records", "key": "records", "type": "group"}}, "description": "<PERSON>eigt eine Liste aller Teammitglieder eines Centers", "descriptions": ["", "", "", "<PERSON>le Auswahl eines Centers oder von Teammitgliedern zur Anzeige"], "icon": "", "iconOverlay": "", "key": "center_team", "label": "Teammitglieder", "labels": ["Überschriftbereich", "Layout", "Navigationstitel innerhalb der Seite", "Center/Mitglieder"], "shortLabel": "", "sorting": 2}}, "palettes": {"tx_mask_18901a94c95c8": {"description": "", "label": "Überschriftbereich", "showitem": ["header", "header_layout", "subheader"]}}, "sql": {"tx_mask_grid_config": {"tt_content": {"tx_mask_grid_config": "varchar(255) DEFAULT '' NOT NULL"}}, "tx_mask_nav_title": {"tt_content": {"tx_mask_nav_title": "varchar(255) DEFAULT '' NOT NULL"}}, "tx_mask_records": {"tt_content": {"tx_mask_records": "text"}}}, "tca": {"header": {"coreField": 1, "description": {"center_team": ""}, "fullKey": "header", "inPalette": 1, "inlineParent": {"center_team": "tx_mask_18901a94c95c8"}, "key": "header", "label": {"center_team": ""}, "order": {"center_team": 1}, "type": "string"}, "header_layout": {"coreField": 1, "description": {"center_team": ""}, "fullKey": "header_layout", "inPalette": 1, "inlineParent": {"center_team": "tx_mask_18901a94c95c8"}, "key": "header_layout", "label": {"center_team": ""}, "order": {"center_team": 2}, "type": "select"}, "subheader": {"coreField": 1, "description": {"center_team": ""}, "fullKey": "subheader", "inPalette": 1, "inlineParent": {"center_team": "tx_mask_18901a94c95c8"}, "key": "subheader", "label": {"center_team": ""}, "order": {"center_team": 3}, "type": "string"}, "tx_mask_18901a94c95c8": {"config": {"type": "palette"}, "fullKey": "tx_mask_18901a94c95c8", "key": "18901a94c95c8", "type": "palette"}, "tx_mask_grid_config": {"config": {"type": "select"}, "fullKey": "tx_mask_grid_config", "key": "grid_config", "type": "select"}, "tx_mask_nav_title": {"config": {"nullable": 1, "type": "input"}, "fullKey": "tx_mask_nav_title", "key": "nav_title", "type": "string"}, "tx_mask_records": {"config": {"allowed": "tx_dbi_center,tx_dbi_teammember", "type": "group"}, "fullKey": "tx_mask_records", "key": "records", "type": "group"}}}}}