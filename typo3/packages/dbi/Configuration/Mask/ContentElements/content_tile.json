{"version": "8.1.0", "restructuringDone": true, "tables": {"tt_content": {"elements": {"content_tile": {"color": "#0018a8", "colorOverlay": "#0018a8", "columns": ["tx_mask_theme", "tx_mask_orientation", "tx_mask_icon", "tx_mask_link", "header"], "columnsOverride": {"tx_mask_icon": {"config": {"fieldWizard": {"selectIcons": {"disabled": 0}}, "fileFolderConfig": {"folder": "EXT:dbi/Resources/Public/Icons/Mask"}, "items": [{"description": "", "group": "", "icon": "", "label": "<PERSON><PERSON>", "value": ""}], "renderType": "selectSingle"}, "fullKey": "tx_mask_icon", "key": "icon", "type": "select"}, "tx_mask_orientation": {"config": {"default": "row", "fieldWizard": {"selectIcons": {"disabled": 1}}, "items": [{"description": "", "group": "", "icon": "", "label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "row"}, {"description": "", "group": "", "icon": "", "label": "<PERSON><PERSON><PERSON><PERSON>", "value": "column"}], "renderType": "selectSingle"}, "fullKey": "tx_mask_orientation", "key": "orientation", "type": "select"}, "tx_mask_theme": {"config": {"fieldWizard": {"selectIcons": {"disabled": 1}}, "items": [], "renderType": "selectSingle"}, "fullKey": "tx_mask_theme", "key": "theme", "type": "select"}}, "description": "Inhaltskachel mit Text und Icon mit optionaler Verlinkung", "descriptions": ["", "", "Icon links oder oberhalb des Inhalts", "", ""], "icon": "", "iconOverlay": "", "key": "content_tile", "label": "<PERSON><PERSON>", "labels": ["<PERSON><PERSON><PERSON>", "Ausrichtung", "Icon", "Verlinkung", "Text"], "shortLabel": "<PERSON><PERSON>", "sorting": 4}}, "sql": {"tx_mask_icon": {"tt_content": {"tx_mask_icon": "varchar(255) DEFAULT '' NOT NULL"}}, "tx_mask_link": {"tt_content": {"tx_mask_link": "varchar(255) DEFAULT '' NOT NULL"}}, "tx_mask_orientation": {"tt_content": {"tx_mask_orientation": "varchar(255) DEFAULT '' NOT NULL"}}, "tx_mask_theme": {"tt_content": {"tx_mask_theme": "varchar(255) DEFAULT '' NOT NULL"}}}, "tca": {"header": {"coreField": 1, "fullKey": "header", "key": "header", "type": "string"}, "tx_mask_icon": {"config": {"type": "select"}, "fullKey": "tx_mask_icon", "key": "icon", "type": "select"}, "tx_mask_link": {"config": {"nullable": 0, "type": "link"}, "fullKey": "tx_mask_link", "key": "link", "type": "link"}, "tx_mask_orientation": {"config": {"type": "select"}, "fullKey": "tx_mask_orientation", "key": "orientation", "type": "select"}, "tx_mask_theme": {"config": {"type": "select"}, "fullKey": "tx_mask_theme", "key": "theme", "type": "select"}}}}}