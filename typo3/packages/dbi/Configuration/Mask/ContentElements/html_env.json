{"version": "8.1.0", "restructuringDone": true, "tables": {"tt_content": {"elements": {"html_env": {"color": "#ff7300", "colorOverlay": "#000000", "columns": ["tx_mask_env"], "description": "Gibt HTML aus - je nach Server-/Anwendungskontext unterschiedlich.", "descriptions": ["Reihenfolge beachten! \"Alle\" immer ans Ende schieben."], "icon": "", "iconOverlay": "", "key": "html_env", "label": "HTML (umgebungsspezifisch)", "labels": ["Umgebung"], "shortLabel": "", "sorting": 1}}, "sql": {"tx_mask_env": {"tt_content": {"tx_mask_env": "int(11) unsigned DEFAULT '0' NOT NULL"}}}, "tca": {"tx_mask_env": {"config": {"appearance": {"enabledControls": {"delete": 1, "dragdrop": 1, "hide": 1, "info": 0, "localize": 0, "new": 1, "sort": 1}, "levelLinksPosition": "top", "showAllLocalizationLink": 1, "showNewRecordLink": 1, "showPossibleLocalizationRecords": 1, "useSortable": 1}, "foreign_field": "parentid", "foreign_sortby": "sorting", "foreign_table": "--inlinetable--", "foreign_table_field": "parenttable", "type": "inline"}, "ctrl": {"label": "tx_mask_environment"}, "fullKey": "tx_mask_env", "key": "env", "type": "inline"}}}, "tx_mask_env": {"sql": {"tx_mask_environment": {"tx_mask_env": {"tx_mask_environment": "varchar(255) DEFAULT '' NOT NULL"}}, "tx_mask_html": {"tx_mask_env": {"tx_mask_html": "mediumtext"}}}, "tca": {"tx_mask_environment": {"config": {"fieldWizard": {"selectIcons": {"disabled": 1}}, "items": [{"description": "", "group": "", "icon": "", "label": "- alle -", "value": ""}, {"description": "", "group": "", "icon": "", "label": "Production", "value": "Production"}, {"description": "", "group": "", "icon": "", "label": "Production/Staging", "value": "Production/Staging"}, {"description": "", "group": "", "icon": "", "label": "Development/Local", "value": "Development/Local"}], "renderType": "selectSingle", "type": "select"}, "fullKey": "tx_mask_environment", "inlineParent": "tx_mask_env", "key": "environment", "label": "Umgebung", "order": 1, "type": "select"}, "tx_mask_html": {"config": {"fixedFont": 1, "format": "html", "nullable": 0, "type": "text", "wrap": "virtual"}, "description": "Reiner HTML-Code", "fullKey": "tx_mask_html", "inlineParent": "tx_mask_env", "key": "html", "label": "HTML", "order": 2, "type": "text"}}}}}