{"version": "8.1.0", "restructuringDone": true, "tables": {"tt_content": {"elements": {"content_tiles": {"color": "#0018a8", "colorOverlay": "#0018a8", "columns": ["tx_mask_grid_config", "tx_mask_items"], "columnsOverride": {"tx_mask_grid_config": {"config": {"fieldWizard": {"selectIcons": {"disabled": 1}}, "items": [{"description": "", "group": "", "icon": "", "label": "<PERSON><PERSON> Spalte", "value": "1"}, {"description": "", "group": "", "icon": "", "label": "2 Spalten", "value": "2"}, {"description": "", "group": "", "icon": "", "label": "3 Spalten", "value": "3"}, {"description": "", "group": "", "icon": "", "label": "4 Spalten", "value": "4"}, {"description": "", "group": "", "icon": "", "label": "<PERSON><PERSON><PERSON><PERSON>", "value": "5"}, {"description": "", "group": "", "icon": "", "label": "Mobile: 1 Spalte, Desktop: 3 Spalten", "value": "1 lg:3"}, {"description": "", "group": "", "icon": "", "label": "Mobile: 2 Spalten, Desktop: 4 Spalten", "value": "2 lg:4"}, {"description": "", "group": "", "icon": "", "label": "Mobile: 3 Spalten, Desktop: 5 Spalten", "value": "3 lg:5"}], "renderType": "selectSingle"}, "fullKey": "tx_mask_grid_config", "key": "grid_config", "type": "select"}, "tx_mask_items": {"cTypes": ["mask_content_tile"], "config": {"appearance": {"enabledControls": {"delete": 1, "dragdrop": 1, "hide": 1, "info": 1, "localize": 1, "new": 1, "sort": 1}, "levelLinksPosition": "top", "showAllLocalizationLink": 1, "showNewRecordLink": 1, "showPossibleLocalizationRecords": 1, "useSortable": 1}, "foreign_sortby": "sorting", "overrideChildTca": {"columns": {"colPos": {"config": {"default": 999}}}}}, "fullKey": "tx_mask_items", "key": "items", "type": "content"}}, "description": "Inhaltskacheln", "descriptions": ["", ""], "icon": "", "iconOverlay": "", "key": "content_tiles", "label": "<PERSON><PERSON><PERSON>", "labels": ["Layout", "<PERSON><PERSON><PERSON>"], "shortLabel": "<PERSON><PERSON><PERSON>", "sorting": 5}}, "sql": {"tx_mask_grid_config": {"tt_content": {"tx_mask_grid_config": "varchar(255) DEFAULT '' NOT NULL"}}, "tx_mask_items": {"tt_content": {"tx_mask_items": "int(11) unsigned DEFAULT '0' NOT NULL"}}}, "tca": {"tx_mask_grid_config": {"config": {"type": "select"}, "fullKey": "tx_mask_grid_config", "key": "grid_config", "type": "select"}, "tx_mask_items": {"cTypes": ["mask_text", "mask_content_tile", "mask_teaser", "mask_testimonial", "mask_projekt_card"], "config": {"foreign_table": "tt_content", "type": "inline"}, "fullKey": "tx_mask_items", "key": "items", "type": "content"}}}}}