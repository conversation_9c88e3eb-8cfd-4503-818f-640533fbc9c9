{"version": "8.1.0", "restructuringDone": true, "tables": {"tt_content": {"elements": {"list": {"color": "#000000", "colorOverlay": "#000000", "columns": ["tx_mask_list_type", "tx_mask_list_items"], "columnsOverride": {"tx_mask_list_type": {"config": {"default": "list-none", "fieldWizard": {"selectIcons": {"disabled": 1}}, "items": [{"description": "", "group": "", "icon": "", "label": "<PERSON><PERSON>", "value": "list-none"}, {"description": "", "group": "", "icon": "", "label": "<PERSON><PERSON><PERSON><PERSON>", "value": "list-checkmark"}, {"description": "", "group": "", "icon": "", "label": "Bindestrich", "value": "list-dash"}, {"description": "", "group": "", "icon": "", "label": "<PERSON><PERSON><PERSON>", "value": "list-decimal"}], "renderType": "selectSingle"}, "fullKey": "tx_mask_list_type", "key": "list_type", "type": "select"}}, "description": "Geordnete und ungeordnete Liste", "descriptions": ["", ""], "icon": "", "iconOverlay": "", "key": "list", "label": "Liste", "labels": ["<PERSON><PERSON>", "Inhalts *"], "shortLabel": "", "sorting": 6}}, "sql": {"tx_mask_list_items": {"tt_content": {"tx_mask_list_items": "int(11) unsigned DEFAULT '0' NOT NULL"}}, "tx_mask_list_type": {"tt_content": {"tx_mask_list_type": "varchar(255) DEFAULT '' NOT NULL"}}}, "tca": {"tx_mask_list_items": {"config": {"appearance": {"enabledControls": {"delete": 1, "dragdrop": 1, "hide": 1, "info": 1, "localize": 1, "new": 1, "sort": 1}, "levelLinksPosition": "top", "showAllLocalizationLink": 1, "showNewRecordLink": 1, "showPossibleLocalizationRecords": 1, "useSortable": 1}, "foreign_field": "parentid", "foreign_sortby": "sorting", "foreign_table": "--inlinetable--", "foreign_table_field": "parenttable", "minitems": "1", "type": "inline"}, "fullKey": "tx_mask_list_items", "key": "list_items", "type": "inline"}, "tx_mask_list_type": {"config": {"type": "select"}, "fullKey": "tx_mask_list_type", "key": "list_type", "type": "select"}}}, "tx_mask_list_items": {"sql": {"tx_mask_list_item": {"tx_mask_list_items": {"tx_mask_list_item": "mediumtext"}}}, "tca": {"tx_mask_list_item": {"config": {"enableRichtext": 1, "richtextConfiguration": "minimal", "type": "text"}, "fullKey": "tx_mask_list_item", "inlineParent": "tx_mask_list_items", "key": "list_item", "label": "Inhalt *", "order": 1, "type": "richtext"}}}}}