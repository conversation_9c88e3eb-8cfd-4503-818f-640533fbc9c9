{"version": "8.1.0", "restructuringDone": true, "tables": {"tt_content": {"elements": {"button": {"color": "#000000", "colorOverlay": "#000000", "columns": ["header", "tx_mask_link", "tx_mask_icon", "tx_mask_theme"], "columnsOverride": {"tx_mask_icon": {"config": {"fieldWizard": {"selectIcons": {"disabled": 0}}, "fileFolderConfig": {"folder": "EXT:dbi/Resources/Public/Icons/Mask"}, "items": [{"description": "", "group": "", "icon": "", "label": "<PERSON><PERSON>", "value": ""}], "renderType": "selectSingle"}, "fullKey": "tx_mask_icon", "key": "icon", "type": "select"}, "tx_mask_link": {"config": {"allowedTypes": ["url", "email", "record", "telephone", "page"]}, "fullKey": "tx_mask_link", "key": "link", "type": "link"}, "tx_mask_theme": {"config": {"fieldWizard": {"selectIcons": {"disabled": 1}}, "items": [{"description": "", "group": "", "icon": "", "label": "Secondary", "value": "secondary"}], "renderType": "selectSingle"}, "fullKey": "tx_mask_theme", "key": "theme", "type": "select"}}, "description": "<PERSON> als <PERSON>", "descriptions": ["", "", "Icon innerhalb des Buttons (links neben But<PERSON>text)", ""], "icon": "", "iconOverlay": "", "key": "button", "label": "<PERSON><PERSON>", "labels": ["Text", "Verlinkung", "Icon", "<PERSON><PERSON><PERSON>"], "shortLabel": "<PERSON><PERSON>", "sorting": 2}}, "sql": {"tx_mask_icon": {"tt_content": {"tx_mask_icon": "varchar(255) DEFAULT '' NOT NULL"}}, "tx_mask_link": {"tt_content": {"tx_mask_link": "varchar(255) DEFAULT '' NOT NULL"}}, "tx_mask_theme": {"tt_content": {"tx_mask_theme": "varchar(255) DEFAULT '' NOT NULL"}}}, "tca": {"header": {"coreField": 1, "fullKey": "header", "key": "header", "type": "string"}, "tx_mask_icon": {"config": {"type": "select"}, "fullKey": "tx_mask_icon", "key": "icon", "type": "select"}, "tx_mask_link": {"config": {"nullable": 0, "type": "link"}, "fullKey": "tx_mask_link", "key": "link", "type": "link"}, "tx_mask_theme": {"config": {"type": "select"}, "fullKey": "tx_mask_theme", "key": "theme", "type": "select"}}}}}