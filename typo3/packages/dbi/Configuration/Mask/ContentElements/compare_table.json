{"version": "8.1.0", "restructuringDone": true, "tables": {"tt_content": {"elements": {"compare_table": {"color": "#000000", "colorOverlay": "#000000", "columns": ["tx_mask_type", "tx_mask_table"], "columnsOverride": {"tx_mask_table": {"config": {"wrap": "virtual"}, "fullKey": "tx_mask_table", "key": "table", "type": "text"}, "tx_mask_type": {"config": {"default": "default", "fieldWizard": {"selectIcons": {"disabled": 1}}, "items": [{"description": "", "group": "", "icon": "", "label": "Standard", "value": "default"}, {"description": "", "group": "", "icon": "", "label": "Wohneinheiten", "value": "expose"}], "renderType": "selectSingle"}, "fullKey": "tx_mask_type", "key": "type", "type": "select"}}, "description": "", "descriptions": ["", ""], "icon": "", "iconOverlay": "", "key": "compare_table", "label": "Tabelle für Vergleiche", "labels": ["Typ der Vergleichstabelle", "<PERSON><PERSON><PERSON>"], "shortLabel": "Vergleichstabelle", "sorting": 1}}, "sql": {"tx_mask_table": {"tt_content": {"tx_mask_table": "mediumtext"}}, "tx_mask_type": {"tt_content": {"tx_mask_type": "varchar(255) DEFAULT '' NOT NULL"}}}, "tca": {"tx_mask_table": {"config": {"nullable": 0, "type": "text"}, "fullKey": "tx_mask_table", "key": "table", "type": "text"}, "tx_mask_type": {"config": {"type": "select"}, "fullKey": "tx_mask_type", "key": "type", "type": "select"}}}}}