# Move content elements to a different position in the New Content Element Wizard
# this cannot be loaded via page.tsconfig
# see https://github.com/Gernott/mask/issues/573
# Clear frontend cache to see changes made here
mod.wizards.newContentElement.wizardItems {

    center {
        header = Center
        elements {
            mask_center_info < mod.wizards.newContentElement.wizardItems.mask.elements.mask_center_info
            mask_center_team < mod.wizards.newContentElement.wizardItems.mask.elements.mask_center_team
            mask_center_header < mod.wizards.newContentElement.wizardItems.mask.elements.mask_center_header
        }
        show = mask_center_header,mask_center_info,mask_center_team
        after = mask
        before = plugins
    }

    special {
        elements {
            genericGallery < mod.wizards.newContentElement.wizardItems.plugins.elements.genericGallery
            ce_timeline < mod.wizards.newContentElement.wizardItems.common.elements.ce_timeline
            mask_html_env < mod.wizards.newContentElement.wizardItems.mask.elements.mask_html_env
            mask_test_centerdata < mod.wizards.newContentElement.wizardItems.mask.elements.mask_test_centerdata
            mask_widget_search_profile < mod.wizards.newContentElement.wizardItems.mask.elements.mask_widget_search_profile
        }

        show := addToList(genericGallery, ce_timeline, mask_html_env, mask_test_centerdata, mask_widget_search_profile)
    }

    common {
        elements {
            ce_timeline >
        }
    }


    plugins {
        after = center
        before = special

        elements {
            genericGallery >
        }
    }

    # must be last because of removals
    mask {
        elements {
            mask_center_header >
            mask_center_info >
            mask_center_team >
            mask_html_env >
            mask_test_centerdata >
            mask_widget_search_profile >
        }
        show := removeFromList(mask_center_info, mask_center_team, mask_html_env, mask_test_centerdata)
    }
}
