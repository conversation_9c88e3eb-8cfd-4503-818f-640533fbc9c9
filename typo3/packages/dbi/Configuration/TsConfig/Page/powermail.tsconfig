tx_powermail.flexForm.addField {
    settings\.flexform\.main\.msa {
        _sheet = main
        label = Daten an die Elanders Mobile Sales API schicken
        config {
            type = check
        }
    }
}

tx_powermail.flexForm.type.addFieldOptions {
    {
        toggle = Toggle
        # Tell powermail that the new fieldtype will transmit anything else then a string (0:string, 1:array, 2:date, 3:file)
        toggle.dataType = 1
        # It's a field where the user can send values and powermail stores the values
        # You can tell powermail that this new field should be exportable in backend module and via CommandController
        toggle.export = 1
    }
}

TCEFORM {
    tx_powermail_domain_model_field {
        css {
            removeItems = layout1, layout2, layout3
            addItems {
                default = Standard
                twocolspan = Auf zwei Spalten aufspannen
                faketwocolspan = Bereich auf zwei Spalten aufspannen, Feldgröße beibehalten
                threecolspan = Auf drei Spalten aufspannen
            }
        }
    }

    tx_powermail_domain_model_page {
        css {
            removeItems = layout1, layout2, layout3, nolabel
            addItems {
                default = Standard
                twocolumns = Basis-Grid: zweispaltig
                threecolumns = Basis-Grid: dreispaltig
            }
        }
    }

    tx_powermail_domain_model_form {
        css {
            removeItems = layout1, layout2, layout3, nolabel
            addItems {
                default = Standard
                showlegend = Seitentitel anzeigen
            }
        }
    }
}

TCAdefaults {
    tx_powermail_domain_model_field {
        css = default
    }

    tx_powermail_domain_model_page {
        css = default
    }

    tx_powermail_domain_model_form {
        css = default
    }
}
