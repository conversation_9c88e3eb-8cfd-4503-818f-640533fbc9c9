<?xml version="1.0" encoding="UTF-8"?>
<T3DataStructure>
    <meta type="array">
        <langChildren>0</langChildren>
        <langDisable>1</langDisable>
    </meta>
    <ROOT>
        <type>array</type>
        <TCEforms>
            <title>Konfiguration der Immobilienprojektliste</title>
        </TCEforms>
        <el>
            <settings.category>
                <label>Kategorie</label>
                <description><PERSON><PERSON> lassen um Projekte aller Kategorien anzuzeigen</description>
                <config>
                    <type>category</type>
                    <relationship>oneToOne</relationship>
                    <size>8</size>
                    <treeConfig>
                        <startingPoints>###SITE:settings.categories.projectTypes###</startingPoints>
                        <appearance>
                            <showHeader>0</showHeader>
                            <nonSelectableLevels>0</nonSelectableLevels>
                        </appearance>
                    </treeConfig>
                </config>
            </settings.category>

            <settings.order>
                <label>Sortierung</label>
                <description>Daten werden von den Projektseiteneigenschaften genommen. Änderungsdatum erst aus "Metadaten > Letzte Aktualisierung", wenn leer dann vom Änderungzeitpunkt des Datensatzes selbst.</description>
                <config>
                    <type>select</type>
                    <renderType>selectSingle</renderType>
                    <items>
                        <numIndex index="0">
                            <numIndex index="label">Seitentitel (A-Z)</numIndex>
                            <numIndex index="value">titleAsc</numIndex>
                        </numIndex>
                        <numIndex index="1">
                            <numIndex index="label">Änderungsdatum (neueste zuerst)</numIndex>
                            <numIndex index="value">dateDesc</numIndex>
                        </numIndex>
                    </items>
                </config>
            </settings.order>

            <settings.pageSize>
                <label>Projekte pro Seite</label>
                <config>
                    <type>number</type>
                    <default>20</default>
                    <range>
                        <lower>1</lower>
                    </range>
                </config>
            </settings.pageSize>

        </el>
    </ROOT>
</T3DataStructure>
