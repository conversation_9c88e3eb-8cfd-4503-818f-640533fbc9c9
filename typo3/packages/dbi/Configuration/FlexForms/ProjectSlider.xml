<?xml version="1.0" encoding="UTF-8"?>
<T3DataStructure>
    <meta type="array">
        <langChildren>0</langChildren>
        <langDisable>1</langDisable>
    </meta>
    <ROOT>
        <type>array</type>
        <TCEforms>
            <title>Konfiguration des Immobilienprojektsliders</title>
        </TCEforms>
        <el>
            <settings.projects>
                <label>Anzuzeigende Immobilienprojekte</label>
                <config>
                    <type>select</type>
                    <renderType>selectMultipleSideBySide</renderType>
                    <foreign_table>pages</foreign_table>
                    <foreign_table_where>
                        AND pages.doktype = 11
                        ORDER BY pages.title
                    </foreign_table_where>
                    <enableMultiSelectFilterTextfield>true</enableMultiSelectFilterTextfield>
                    <maxitems>20</maxitems>
                    <size>10</size>
                </config>
            </settings.projects>

        </el>
    </ROOT>
</T3DataStructure>
