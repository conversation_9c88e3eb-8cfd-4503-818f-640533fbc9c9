lib.containerElement < lib.contentElement
lib.containerElement {
  templateRootPaths.10 = EXT:dbi/Resources/Private/Templates/Container
  dataProcessing {
    200 = B13\Container\DataProcessing\ContainerProcessor
  }
}

tt_content.tx_dbi_container_one_column < lib.containerElement
tt_content.tx_dbi_container_one_column.templateName = MainContainer

tt_content.tx_dbi_container_two_columns < lib.containerElement
tt_content.tx_dbi_container_two_columns.templateName = TwoColumnContainer

tt_content.tx_dbi_container_two_columns_2_1 < lib.containerElement
tt_content.tx_dbi_container_two_columns_2_1.templateName = TwoColumnContainer

tt_content.tx_dbi_container_two_columns_1_2 < lib.containerElement
tt_content.tx_dbi_container_two_columns_1_2.templateName = TwoColumnContainer

tt_content.tx_dbi_container_three_columns < lib.containerElement
tt_content.tx_dbi_container_three_columns.templateName = ThreeColumnContainer

tt_content.tx_dbi_container_four_columns < lib.containerElement
tt_content.tx_dbi_container_four_columns.templateName = FourColumnContainer

tt_content.tx_dbi_stage_container < lib.containerElement
tt_content.tx_dbi_stage_container {
  templateName = StageContainer
  dataProcessing {
    300 = TYPO3\CMS\Frontend\DataProcessing\FilesProcessor
    300 {
      references {
        table = tt_content
        fieldName = tx_dbi_stage_image
      }
      as = stage_images
    }
  }

  variables {
    marketPriceEstimationPid = TEXT
    marketPriceEstimationPid {
      value = {$dbi.pids.marketPriceEstimation}
    }
  }
}
