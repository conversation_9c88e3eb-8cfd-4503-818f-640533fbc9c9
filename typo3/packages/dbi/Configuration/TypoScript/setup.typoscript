@import 'EXT:fluid_styled_content/Configuration/TypoScript/setup.typoscript'
@import 'EXT:mask/Configuration/TypoScript/setup.typoscript'
@import 'EXT:powermail/Configuration/TypoScript/Main/setup.typoscript'
@import 'EXT:scheduler_status/Configuration/TypoScript/setup.typoscript'
@import 'EXT:seo/Configuration/TypoScript/XmlSitemap/setup.typoscript'
@import 'EXT:ce_timeline/Configuration/TypoScript/setup.typoscript'
@import 'EXT:generic_gallery/Configuration/TypoScript/setup.typoscript'

@import 'EXT:dbi/Configuration/TypoScript/backend.typoscript'
@import 'EXT:dbi/Configuration/TypoScript/page.typoscript'
@import 'EXT:dbi/Configuration/TypoScript/page.ajax.typoscript'
@import 'EXT:dbi/Configuration/TypoScript/page.center.typoscript'
@import 'EXT:dbi/Configuration/TypoScript/plugins.typoscript'
@import 'EXT:dbi/Configuration/TypoScript/powermail.typoscript'
@import 'EXT:dbi/Configuration/TypoScript/sitemap.typoscript'
@import 'EXT:dbi/Configuration/TypoScript/container.typoscript'
@import 'EXT:dbi/Configuration/TypoScript/Styles/default.typoscript'
@import 'EXT:dbi/Configuration/TypoScript/Scripts/default.typoscript'

[applicationContext == "Development/Local"]
    @import 'EXT:dbi/Configuration/TypoScript/development.typoscript'
[end]
