# Empty lines after the custom category declaration is required because of a bug
# https://forge.typo3.org/issues/100936

# customcategory=dbi=Deutsche Bank Immobilien
# customsubcategory=theme=Theme
# customsubcategory=fio=FIO
# customsubcategory=images=Bilder
# customsubcategory=pids=Seiten-IDs
# customsubcategory=maintenance=Wartungsbanner

@import 'EXT:fluid_styled_content/Configuration/TypoScript/constants.typoscript'
@import 'EXT:powermail/Configuration/TypoScript/Main/constants.typoscript'
@import 'EXT:scheduler_status/Configuration/TypoScript/constants.typoscript'
@import 'EXT:seo/Configuration/TypoScript/XmlSitemap/constants.typoscript'
@import 'EXT:ce_timeline/Configuration/TypoScript/constants.typoscript'
@import 'EXT:generic_gallery/Configuration/TypoScript/constants.typoscript'

dbi {

    theme {
        # cat=dbi/theme/a; type=string; label=Pfad zu den Favicons
        favicon_path = EXT:dbi/Resources/Public/Icons/Favicons
        # cat=dbi/theme/b; type=color; label=Farbe
        color = #0018A8
        # cat=dbi/theme/c; type=string; label=Copyright
        copyright = Deutsche Bank Immobilien GmbH
    }

    fio {
        # cat=dbi/fio/d; type=string; label=Standard-Umkreis für Centerimmobiliensuche in km
        defaultCenterRadiusKm = 30
    }

    pids {
        # cat=dbi/pids/menu_support; type=int+; label=Menu (Support im Header)
        header_support_menu = 9
        # cat=dbi/pids/menu_footer; type=int+; label=Menu (Footer)
        footer_menu = 7
        # cat=dbi/pids/menu_support_footer; type=int+; label=Menu (Support im Footer)
        footer_support_menu = 10

        # cat=dbi/pids/xa; type=int+; label=Immobiliencenter Ordner
        centerFolder = 17
        # cat=dbi/pids/xb; type=int+; label=Immobiliencenter Vorlage/Mountziel
        centerTemplate = 15

        #cat=dbi/pids/cba; type=int+; label=Page ID for cookie banner content
        cookiebannerContent = 512
        #cat=dbi/pids/cbb; type=string; label=Page IDs for pages without cookie banner
        cookiebannerHide = 514,515

        # cat=dbi/pids/sell; type=int+; label=Page Id for Immobilie verkaufen
        sellEstate = 519
        # cat=dbi/pids/request; type=int+; label=Page Id for Suchauftrag anlegen
        searchRequest = 511
        # cat=dbi/pids/expose; type=int+; label=Page Id for Exposes
        expose = 3
        # cat=dbi/pids/estimation; type=int+; label=Page Id for Marktpreiseinschätzung
        marketPriceEstimation = 507
        # cat=dbi/pids/estimation; type=int+; label=Page Id for Maklersuche
        brokerSearch = 520
        # cat=dbi/pids/special_footer_element; type=int+; label=Page ID for Special Footer Content Element
        specialFooterElement = 556
    }

    images {
        #cat=dbi/images; type=string; label=Pfad zum Bild von "makler-empfehlung.de"
        maklerempfehlung=EXT:dbi/Resources/Public/Images/makler_empfehlung.png

        #cat=dbi/images; type=string; label=FAL path to award image #1 for all centers
        allcenterawards.0=1:siegel/siegel_diewelt.2020.png
        #cat=dbi/images; type=string; label=FAL path to award image #2 for all centers
        allcenterawards.1=
        #cat=dbi/images; type=string; label=FAL path to award image #3 for all centers
        allcenterawards.2=
    }

    maintenanceBanner {
        #cat=dbi/maintenance; type=boolean; label=Wartungsbanner aktivieren
        active = 0

        #cat=dbi/maintenance; type=string; label=Text Wartungsbanner
        text =

        #cat=dbi/maintenance; type=string; label=Link: Text
        btnText =

        #cat=dbi/maintenance; type=string; label=Link: Seiten-ID
        btnPid =
    }
}

plugin.tx_schedulerstatus.settings.token = eiw0aiwee1eenoa9jeiWoije6Hohyi0o

plugin.tx_seo.settings.xmlSitemap.sitemaps.pages {
    # remove expose details, system folder and center pages
    excludePagesRecursive = 3, 14, 17
}

ce_timeline.gallery.image_width = 720m
ce_timeline.gallery.image_height = 344m
ce_timeline.gallery.enable_lightbox = 0

plugin.tx_powermail.view {
    templateRootPath = EXT:dbi/Resources/Private/Templates/Powermail/
    partialRootPath = EXT:dbi/Resources/Private/Partials/Powermail/
    // layoutRootPath = EXT:powermail/Resources/Private/Layouts/
}

plugin.tx_genericgallery.view {
    templateRootPath = EXT:dbi/Resources/Private/Templates/GenericGallery/
    // partialRootPath = EXT:dbi/Resources/Private/Partials/GenericGallery/
    layoutRootPath = EXT:dbi/Resources/Private/Layouts/GenericGallery/
}
