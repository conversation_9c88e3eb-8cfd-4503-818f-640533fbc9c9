# custom canonical URL generation for center pages
# TYPO3 uses the Mount Point target as canonical URL,
# which we do not want
# https://forge.typo3.org/issues/93300
[17 in tree.rootLineIds]
config.disableCanonical = 1

page {
    headerData {
        21 = TEXT
        21 {
            htmlSpecialChars = 1
            typolink {
                parameter.data = TSFE:id
                returnLast = url
                addQueryString = 1
                addQueryString.method = GET
                forceAbsoluteUrl = 1
            }
            wrap = <link rel="canonical" href="|"/>
        }
    }
}
[end]
