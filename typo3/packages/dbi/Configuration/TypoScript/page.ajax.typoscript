# page type=5 for AJAX/JSON results, rendering plugins only
page-ajax = PAGE
page-ajax {
    typeNum = 5

    config {
        #parsetime output
        debug = 0
        disableAllHeaderCode = true
        xhtml_cleaning = 0
    }

    #render plugins only
    10 = CONTENT
    10.table = tt_content
    10.select {
        where = (CType = 'list' AND list_type not in ('dbi_realtorsearchsearchbar'))
        orderBy = sorting
    }
}

[request?.getPageArguments()?.getPageType() == 5]
#append our own fluid_styled_content paths that remove wrapper tags
lib.contentElement {
    layoutRootPaths.10   = EXT:dbi/Resources/Private/FluidStyledContentBare/Layouts/
}
[END]
