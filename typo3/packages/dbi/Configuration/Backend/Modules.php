<?php

use Mogic\Dbi\Controller\Backend\CreateCenterWizardController;
use Mogic\Dbi\Backend\Controller\FioApiDataController;
use Mogic\Dbi\Backend\Controller\TradingAreaController;

return [
    'dbi' => [
        'labels' => [
            'title' => 'Deutsche Bank Immobilien',
        ],
        'iconIdentifier' => 'tx-dbi-mod',
        'position' => [
            'after' => 'file',
        ],
    ],

    'dbi_createcenter' => [
        'parent' => 'web',
        'access' => 'user',
        'labels' => [
            'title' => 'Immobiliencenter'
        ],
        'appearance' => [
            'renderInModuleMenu' => false,
        ],
        'path' => '/dbi/center',
        'extensionName' => 'Dbi',
        'controllerActions' => [
            CreateCenterWizardController::class => [
                'main'
            ],
        ],
    ],

    'dbi_fioapidata' => [
        'parent' => 'web',
        'access' => 'user',
        'labels' => [
            'title' => 'FIO-Webmaklerdaten'
        ],
        'appearance' => [
            'renderInModuleMenu' => false,
        ],
        'path' => '/dbi/fioapidata',
        'extensionName' => 'Dbi',
        'controllerActions' => [
            FioApiDataController::class => [
                'show'
            ],
        ],
    ],

    'dbi_tradingarea' => [
        'parent' => 'dbi',
        'access' => 'user',
        'labels' => [
            'title' => 'Vertriebsgebiete',
            'shortDescription' => 'Importiert neue Vertriebsgebiete',
        ],
        'iconIdentifier' => 'tx-dbi-mod-tradingarea',
        'path' => '/dbi/tradingarea',
        'extensionName' => 'Dbi',
        'controllerActions' => [
            TradingAreaController::class => [
                'datacheck',
                'export',
                'operations',
            ],
        ]
    ],
];
