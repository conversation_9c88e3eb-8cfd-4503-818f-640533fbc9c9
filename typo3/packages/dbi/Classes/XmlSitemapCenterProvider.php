<?php

namespace Mogic\Dbi;

use Psr\Http\Message\ServerRequestInterface;
use TYPO3\CMS\Core\Database\ConnectionPool;
use TYPO3\CMS\Core\Domain\Repository\PageRepository;
use TYPO3\CMS\Core\Utility\GeneralUtility;
use TYPO3\CMS\Frontend\ContentObject\ContentObjectRenderer;
use TYPO3\CMS\Frontend\Typolink\LinkFactory;
use TYPO3\CMS\Seo\XmlSitemap\AbstractXmlSitemapDataProvider;

/**
 * Custom center page provider for XML sitemaps
 *
 * Supports subpages of mount targets.
 * Takes change frequency and priority from mount target pages,
 * and 'last modification' from center records
 *
 * This is required because of mount point handling bugs in TYPO's sitemap code
 * - https://forge.typo3.org/issues/100102
 * - https://forge.typo3.org/issues/91670
 */
class XmlSitemapCenterProvider extends AbstractXmlSitemapDataProvider
{
    protected int $centerFolderUid = 17;

    protected LinkFactory $linkFactory;

    public function __construct(
        ServerRequestInterface $request, string $key, array $config = [],
        ContentObjectRenderer $cObj = null
    ) {
        parent::__construct($request, $key, $config, $cObj);

        $this->linkFactory = GeneralUtility::makeInstance(LinkFactory::class);
        $this->generateItems();
    }

    protected function generateItems(): void
    {
        $pageRepository = GeneralUtility::makeInstance(PageRepository::class);

        $qb = GeneralUtility::makeInstance(ConnectionPool::class)
            ->getQueryBuilderForTable('pages');
        $stmt = $qb->select(
            'pages.uid', 'pages.pid', 'doktype', 'mount_pid', 'mount_pid_ol',
            'title', 'sitemap_changefreq', 'sitemap_priority', 'pages.tstamp',
            'center.tstamp AS center_tstamp'
        )
            ->from('pages')
            ->join('pages', 'tx_dbi_center', 'center')
            ->where(
                $qb->expr()->eq('pages.pid', $qb->createNamedParameter($this->centerFolderUid)),
                $qb->expr()->eq('pages.uid', 'center.pid'),
                $qb->expr()->eq('center.hidden', $qb->createNamedParameter(0)),
                $qb->expr()->eq('pages.doktype', 7),
                $qb->expr()->eq('pages.mount_pid_ol', 1),
            )
            ->orderBy('sorting')
            ->execute();

        $cachedTargets = [];
        while ($centerPageRow = $stmt->fetch()) {
            $mountPointInfo = $pageRepository->getMountPointInfo(
                $centerPageRow['uid'], $centerPageRow
            );

            $mountTargetUid = $centerPageRow['mount_pid'];
            if (!isset($cachedTargets[$mountTargetUid])) {
                $targetPageIds = $pageRepository->getPageIdsRecursive([$mountTargetUid], 3);
                $targetPages = $pageRepository->getMenuForPages(
                    $targetPageIds, 'uid, sitemap_changefreq, sitemap_priority'
                );
                $cachedTargets[$mountTargetUid] = $targetPages;
            }

            foreach ($cachedTargets[$mountTargetUid] as $targetPage) {
                $item = [
                    'uid'        => $targetPage['uid'],
                    'lastMod'    => (int) $centerPageRow['center_tstamp'],
                    'changefreq' => $targetPage['sitemap_changefreq'],
                    'priority'   => $targetPage['sitemap_priority'],
                ];
                if (is_array($mountPointInfo)) {
                    $item['MPvar'] = $mountPointInfo['MPvar'];
                }
                $this->items[] = $item;
            }
        }
    }

    protected function defineUrl(array $data): array
    {
        //"MPvar" is used by our own SitemapLinkModifier
        $link = $this->linkFactory->create(
            '',
            [
                'parameter'        => $data['uid'],
                'MPvar'            => $data['MPvar'],
                'forceAbsoluteUrl' => true,
            ],
            $this->cObj
        );
        $data['loc'] = $link->getUrl();
        return $data;
    }
}
