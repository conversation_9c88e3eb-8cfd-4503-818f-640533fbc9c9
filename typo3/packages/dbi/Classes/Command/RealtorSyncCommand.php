<?php

namespace Mogic\Dbi\Command;

use Mogic\Dbi\Backend\TceMainHook;
use Mogic\Dbi\Domain\Model\Center;
use Mogic\Dbi\Domain\Model\Teammember;
use Mogic\Dbi\Domain\Model\WebmaklerListUser;
use Mogic\Dbi\Domain\Model\WebmaklerUser;
use Mogic\Dbi\Domain\Repository\CenterRepository;
use Mogic\Dbi\Domain\Repository\TeammemberRepository;
use Mogic\Dbi\Domain\Repository\WebmaklerUserRepository;
use Mogic\Dbi\Domain\Repository\WebmaklerUserRepositoryCached;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use TYPO3\CMS\Core\Core\Environment;
use TYPO3\CMS\Core\Database\ConnectionPool;
use TYPO3\CMS\Core\Database\Query\Restriction\HiddenRestriction;
use TYPO3\CMS\Core\DataHandling\DataHandler;
use TYPO3\CMS\Core\Resource\DuplicationBehavior;
use TYPO3\CMS\Core\Resource\Folder;
use TYPO3\CMS\Core\Resource\ResourceStorage;
use TYPO3\CMS\Core\Resource\StorageRepository;
use TYPO3\CMS\Core\Utility\GeneralUtility;
use TYPO3\CMS\Core\Utility\StringUtility;

/**
 * Import team members (Makler-Teammitglieder) from FIO webmakler
 * and update their local records.
 *
 * Default usage in scheduler:
 * $ ./vendor/bin/typo3 dbi:realtorsync sync-changed --since=2d
 *
 * Initial sync:
 * $ ./vendor/bin/typo3 dbi:realtorsync sync-all -vvv
 *
 * Development:
 * Use the "use-cache" option:
 * $ ./vendor/bin/typo3 dbi:realtorsync download --use-cache --cache-max-age=3600
 * $ ./vendor/bin/typo3 dbi:realtorsync sync-all --use-cache -vvv
 */
class RealtorSyncCommand extends Command
{
    protected int $centerFolderUid = 17;

    protected SymfonyStyle $io;
    protected OutputInterface $output;
    protected bool $force = false;
    protected ?int $limit = null;
    protected ?int $since = null;
    protected ?int $userId = null;

    protected ResourceStorage $storage;

    /**
     * Array of team member UIDs that have been updated.
     * Key is the UID, value is "true"
     * The rest will be hidden.
     */
    protected array $updatedMemberUids = [];

    /**
     * Array of center page UIDs that need to be updated
     * Key is the UID, value is "true"
     */
    protected array $updatedCenterPids = [];

    /**
     * Array of WebmaklerUser objects.
     * Key is the ID.
     */
    protected array $wmUserCache = [];

    /**
     * API access to the FIO webmakler realtors
     * Either cached or uncached (see "use-cache" option).
     */
    protected WebmaklerUserRepository $webmaklerUserRepo;

    public function __construct(
        protected readonly DataHandler $dataHandler,
        protected readonly CenterRepository $centerRepo,
        protected readonly TeammemberRepository $teammemberRepo,
        StorageRepository $storageRepository,
    ) {
        parent::__construct();
        $this->storage = $storageRepository->getDefaultStorage();
        $this->teammemberRepo->setIncludeHidden();
    }

    protected function configure(): void
    {
        $this->setHelp('Update center team member data from FIO webmakler')
            ->addOption(
                'limit',
                null,
                InputOption::VALUE_REQUIRED,
                'Number of team members to update'
            )
            ->addOption(
                'since',
                null,
                InputOption::VALUE_REQUIRED,
                'Import changes that happened since the given timestamp/date/interval'
            )
            ->addOption(
                'use-cache',
                null,
                InputOption::VALUE_NONE,
                'Cache and use cached API responses'
            )
            ->addOption(
                'cache-max-age',
                null,
                InputOption::VALUE_REQUIRED,
                'Number of seconds'
            )
            ->addOption(
                'user-id',
                null,
                InputOption::VALUE_REQUIRED,
                'User ID of realtor to import'
            )
            ->addArgument(
                'action',
                InputArgument::REQUIRED,
                'What sync action to execute:'
                . ' sync-changed, sync-all, download, centerids, compare, hide-missing, sort',
                null,
                [
                    'centerids',
                    'compare',
                    'download',
                    'hide-missing',
                    'sort',
                    'sync-changed',
                    'sync-all',
                ]
            );
    }

    /**
     * Parse input options and switch to the correct subcommand
     */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $vvv = OutputInterface::VERBOSITY_DEBUG;

        $this->io = new SymfonyStyle($input, $output);
        $this->output = $output;
        $this->limit = $input->getOption('limit');
        $this->userId = $input->getOption('user-id');

        $since = $input->getOption('since');
        if ($since !== null) {
            if (!$this->parseSinceOption($since)) {
                return Command::INVALID;
            }
        }

        $userRepoClass = WebmaklerUserRepository::class;
        if ($input->getOption('use-cache')) {
            $userRepoClass = WebmaklerUserRepositoryCached::class;
        }
        $this->webmaklerUserRepo = GeneralUtility::makeInstance($userRepoClass);


        if ($input->getOption('cache-max-age') !== null) {
            if ($this->webmaklerUserRepo instanceof WebmaklerUserRepositoryCached) {
                $this->webmaklerUserRepo->setMaxAgeSeconds(
                    (int) $input->getOption('cache-max-age')
                );
            }
        }

        \TYPO3\CMS\Core\Core\Bootstrap::initializeBackendAuthentication();
        TceMainHook::$showFlashMessages = false;

        $action = $input->getArgument('action');
        switch ($action) {
            case 'centerids':
                $this->setCenterIds();
                break;

            case 'download':
                $this->downloadAllData();
                break;

            case 'hide-missing':
                $this->hideMissingRealtors();
                break;

            case 'compare':
                $this->compareRealtors();
                break;

            case 'sort':
                $this->sortMembersInAllCenters();
                break;

            case 'sync-all':
                $this->syncAllRealtors();
                break;

            case 'sync-changed':
                $this->syncChangedRealtors();
                break;

            default:
                $this->io->error('Invalid action');
                return Command::INVALID;
        }

        return Command::SUCCESS;
    }

    /**
     * Download all visible API users into the cache.
     *
     * Useful during development.
     */
    protected function downloadAllData(): void
    {
        if (!$this->webmaklerUserRepo instanceof WebmaklerUserRepositoryCached) {
            $this->io->error('Use the "--use-cache" option');
            return;
        }

        $v  = OutputInterface::VERBOSITY_VERBOSE;
        $this->io->write('API base URL: ' . $this->webmaklerUserRepo->getApiBaseUrl(), $v);

        foreach ($this->webmaklerUserRepo->findAllVisible() as $listUser) {
            if ($this->userId !== null && $this->userId != $listUser->id) {
                continue;
            }

            $details = $this->webmaklerUserRepo->findOneById($listUser->id);
            if ($details !== null) {
                $this->io->write('.');
            } else {
                $this->io->write('E');
            }
        }
        $this->io->writeln('');
    }

    /**
     * Grab a list of all visible realtors and hide the ones that are not in the list.
     */
    protected function hideMissingRealtors()
    {
        $v   = OutputInterface::VERBOSITY_VERBOSE;
        $vvv = OutputInterface::VERBOSITY_DEBUG;
        $activeMembers = [];

        foreach ($this->webmaklerUserRepo->findAllVisible() as $listUser) {
            $member = $this->teammemberRepo->findOneByFioUserId($listUser->id);
            if ($member !== null) {
                $activeMembers[$member->getUid()] = true;
            } else {
                $this->io->writeln('Realtor with ID ' . $listUser->id . ' not found locally.', $vvv);
            }
        }

        $this->updatedMemberUids = $activeMembers;

        if ($activeMembers && $this->userId === null) {
            $numHidden = $this->disableMissingMembers();
        }

        $this->io->writeln('Hidden: ' . $numHidden, $v);
    }

    /**
     * List all visible realtors/team members and compare their local and API slug
     *
     * Not useful anymore since we compare center Node IDs now.
     */
    protected function compareRealtors(): void
    {
        $normal = OutputInterface::VERBOSITY_NORMAL;

        $this->io->writeln('Name;FIO user ID;FIO center;TYPO3 center;Status');

        foreach ($this->webmaklerUserRepo->findAllVisible() as $listUser) {
            $level = OutputInterface::VERBOSITY_VERBOSE;

            $details = $this->webmaklerUserRepo->findOneById($listUser->id);
            if ($details === null) {
                continue;
            }
            $member  = $this->teammemberRepo->findOneByFioUserId($listUser->id);

            $line = $details->getLastname() . ', ' . $details->getFirstname();
            $line .= ';' . $listUser->id;
            $line .= ';' . $details->group->getCenterSlug();
            if ($member) {
                $center = $member->getCenter();
                if ($center) {
                    $line .= ';' . $member->getCenter()->getSlug();
                    if ($details->group->getCenterSlug() != $member->getCenter()->getSlug()) {
                        $line .= ';#diff';
                        $level = $normal;
                    }
                } else {
                    $line .= ';!!center-missing';
                    $level = $normal;
                }
            } else {
                $line .= ';!!member-not-found';
                $level = $normal;
            }
            $this->io->writeln($line, $level);
        }
    }

    /**
     * Fill the center record field "FIO Vertriebseinheit: Node IDs" automatically
     * by mapping the webmakler user URL and the center slugs.
     *
     * Only useful in the beginning.
     */
    protected function setCenterIds(): void
    {
        $v  = OutputInterface::VERBOSITY_VERBOSE;
        $vv = OutputInterface::VERBOSITY_VERY_VERBOSE;

        $centerIds = [];
        foreach ($this->webmaklerUserRepo->findAllVisible() as $listUser) {
            $details = $this->webmaklerUserRepo->findOneById($listUser->id);
            if ($details === null) {
                continue;
            }

            $center = $this->centerRepo->findByFioGroupId($details->group->id);
            if ($center !== null) {
                $slug = $center->getSlug();
            } else {
                $slug = $details->group->getCenterSlug();
            }

            $key = $details->group->id . '-' . $details->group->shortName;

            if (!isset($centerIds[$slug][$key])) {
                $centerIds[$slug][$key] = [
                    'id'        => $details->group->id,
                    'name'      => $details->group->shortName,
                    'hasCenter' => $center !== null,
                    'count'     => 1,
                ];

            } else {
                $centerIds[$slug][$key]['count']++;
            }
        }
        ksort($centerIds);

        foreach ($centerIds as $slug => $fioGroups) {
            $this->io->writeln($slug, $v);
            if (count($fioGroups) > 1) {
                $numWithCenters = array_sum(array_column($fioGroups, 'hasCenter'));
                if ($numWithCenters == count($fioGroups)) {
                    $this->io->writeln(' All groups have a center', $vv);
                    continue;
                }

                $this->io->writeln(' Multiple groups found for ' . $slug . ', some without a center:');
                foreach ($fioGroups as $fioGroup) {
                    if (!$fioGroup['hasCenter']) {
                        $this->io->writeln(
                            '  - ' . $fioGroup['count'] . 'x'
                                . ' ' . $fioGroup['id']
                                . ' ' . $fioGroup['name']
                        );
                    }
                }
                continue;
            }

            $fioGroup = reset($fioGroups);
            $center = $this->centerRepo->findByFioGroupId($fioGroup['id']);

            if ($center === null) {
                $center = $this->centerRepo->findBySlug($slug);
                if ($center === null) {
                    $this->io->error(
                        'No center found for slug: ' . $slug
                            . ' / ' . $fioGroup['count'] . 'x'
                            . ' ' . $fioGroup['id']
                            . ' ' . $fioGroup['name']
                    );
                    continue;
                }
            }

            if ($center->containsFioGroupId($fioGroup['id'])
                && $center->fioGroupName == $fioGroup['name']
            ) {
                $this->io->writeln(' No update needed', $vv);
                continue;
            }

            $this->io->writeln(' Updating FIO group data', $v);

            $center->addFioGroupId($fioGroup['id']);
            $data = [
                'tx_dbi_center' => [
                    $center->getUid() => [
                        'fio_group_ids'  => $center->fioGroupIds,
                        'fio_group_name' => $fioGroup['name'],
                    ],
                ],
            ];
            $this->dataHandler->start($data, []);
            $this->dataHandler->process_datamap();
            $this->checkDataHandlerError();
        }
    }

    /**
     * Move the "Vertriebsleiter"s to the top of the realtor list in all centers
     */
    protected function sortMembersInAllCenters(): void
    {
        $centerPids = $this->centerRepo->findAllPids();
        foreach ($centerPids as $centerPid) {
            $this->sortMembersInCenter($centerPid);
        }
    }

    /**
     * Move the "Vertriebsleiter"s to the top of the realtor list for a center
     *
     * @return boolean True if members were moved
     */
    protected function sortMembersInCenter(int $centerPid): bool
    {
        $v  = OutputInterface::VERBOSITY_VERBOSE;
        $vv = OutputInterface::VERBOSITY_VERY_VERBOSE;
        $vvv = OutputInterface::VERBOSITY_DEBUG;

        $this->io->writeln('Checking page #' . $centerPid, $v);
        $members = $this->teammemberRepo->findByPid($centerPid);
        $members = array_values(
            array_filter(
                $members, function (Teammember $member) {
                    return !$member->hidden;
                }
            )
        );

        $moveRequired = false;

        $sorted = $members;
        usort(
            $sorted,
            function (Teammember $memberA, Teammember $memberB) {
                return strcmp($memberA->getSortKey(), $memberB->getSortKey());
            }
        );

        foreach ($members as $key => $member) {
            if ($members[$key] != $sorted[$key]) {
                //something changed
                $moveRequired = true;
                break;
            }
        }

        if ($moveRequired) {
            $this->io->writeln(' Moving members', $v);
            $cmd = [
                'tx_dbi_teammember' => []
            ];

            $lastUid = $centerPid;
            foreach ($sorted as $member) {
                $cmd['tx_dbi_teammember'][$member->getUid()] = [
                    'move' => $lastUid,
                ];
                $lastUid = -$member->getUid();
            }

            $this->dataHandler->start([], $cmd);
            $this->dataHandler->process_cmdmap();
            $this->checkDataHandlerError();
        }

        $center = $this->centerRepo->findByPid($centerPid);
        if ($center) {
            $this->io->writeln(' Checking center address update', $vvv);
            foreach ($sorted as $member) {
                if (!$center->isFirstGroupId($member->fioGroupId)) {
                    continue;
                }

                if (isset($this->wmUserCache[$member->fioUserId])) {
                    $details = $this->wmUserCache[$member->fioUserId];
                } else {
                    $details = $this->retryOnApiFailure(
                        function () use ($member) {
                            return $this->webmaklerUserRepo->findOneById($member->fioUserId);
                        }
                    );
                }
                $this->updateCenterAddressFromMember($center, $details);
                break;
            }
        }

        return $moveRequired;
    }

    /**
     * Import all realtors from FIO webmakler API to the database.
     *
     * Useful for the initial import.
     */
    protected function syncAllRealtors(): void
    {
        $v  = OutputInterface::VERBOSITY_VERBOSE;
        $vv = OutputInterface::VERBOSITY_VERY_VERBOSE;

        $numUpdated = 0;
        $numHidden  = 0;
        $complete   = true;

        foreach ($this->webmaklerUserRepo->findAllVisible() as $listUser) {
            if ($this->userId !== null && $this->userId != $listUser->id) {
                continue;
            }

            $this->io->writeln($listUser->name . ' #' . $listUser->id, $v);

            $member  = $this->teammemberRepo->findOneByFioUserId($listUser->id);
            $details = null;
            if ($member === null) {
                $details = $this->retryOnApiFailure(
                    function () use ($listUser) {
                        return $this->webmaklerUserRepo->findOneById($listUser->id);
                    }
                );
                if ($details === null) {
                    continue;
                }
                $this->wmUserCache[$details->id] = $details;
                $this->setFioUserIdToUserName($details);

                $member = $this->teammemberRepo->findOneByFioUserId($listUser->id);
                if ($member === null) {
                    $this->io->writeln(' Creating new member record', $v);
                } else {
                    $this->updatedMemberUids[$member->getUid()] = true;
                    $this->io->writeln(' Member ID updated', $v);
                }

            } else {
                $this->updatedMemberUids[$member->getUid()] = true;
            }

            if ($details === null) {
                $details = $this->retryOnApiFailure(
                    function () use ($listUser) {
                        return $this->webmaklerUserRepo->findOneById($listUser->id);
                    }
                );
            }

            $updated = $this->updateOrCreateMember($member, $details);
            if ($updated) {
                ++$numUpdated;
                $this->io->writeln(' Updated', $v);

                if ($this->limit !== null && $numUpdated >= $this->limit) {
                    $complete = false;
                    break;
                }
            } else {
                $this->io->writeln(' No update necessary', $v);
            }
        }

        if ($complete && $this->userId === null) {
            $numHidden = $this->disableMissingMembers();
        }

        if (count($this->updatedCenterPids)) {
            $this->io->writeln('', $v);
            $this->io->writeln('Updating team member order', $v);
            foreach ($this->updatedCenterPids as $centerPid => $dummy) {
                $this->sortMembersInCenter($centerPid);
            }
        }

        $this->io->writeln('', $v);
        $this->io->writeln('Updated: ' . $numUpdated, $v);
        $this->io->writeln('Hidden: ' . $numHidden, $v);
    }

    /**
     * Import changed realtors from the FIO Webmakler API.
     * Useful to get daily updates via the scheduler.
     */
    protected function syncChangedRealtors(): void
    {
        $v   = OutputInterface::VERBOSITY_VERBOSE;
        $vv  = OutputInterface::VERBOSITY_VERY_VERBOSE;
        $vvv = OutputInterface::VERBOSITY_DEBUG;

        $numUpdated = 0;
        $numHidden  = 0;

        $since = $this->since;
        if ($since === null) {
            $since = time() - 86400;
        }
        $this->io->writeln('--since: ' . date('Y-m-d H:i:s', $since), $vvv);

        foreach ($this->webmaklerUserRepo->findAllChanged($since) as $listUser) {
            $this->io->writeln($listUser->name . ' #' . $listUser->id, $v);

            $member  = $this->teammemberRepo->findOneByFioUserId($listUser->id);

            if (!$listUser->showInBrokerSearch) {
                $numHidden += $this->disableMember($member);
                continue;
            }

            $details = $this->webmaklerUserRepo->findOneById($listUser->id);
            $this->wmUserCache[$details->id] = $details;
            if ($member === null) {
                $this->io->writeln(' Creating new member record', $v);
            }
            $updated = $this->updateOrCreateMember($member, $details);
            if ($updated) {
                ++$numUpdated;
                $this->io->writeln(' Updated', $v);
            } else {
                $this->io->writeln(' No update necessary', $v);
            }

            if ($this->limit !== null && $numUpdated >= $this->limit) {
                break;
            }
        }

        if (count($this->updatedCenterPids)) {
            $this->io->writeln('', $v);
            $this->io->writeln('Updating team member order', $v);
            foreach ($this->updatedCenterPids as $centerPid => $dummy) {
                $this->sortMembersInCenter($centerPid);
            }
        }

        $this->io->writeln('', $v);
        $this->io->writeln('Updated: ' . $numUpdated, $v);
        $this->io->writeln('Hidden: ' . $numHidden, $v);
    }

    /**
     * Team member cannot be found via the FIO user id, so we search by FIO username
     */
    protected function setFioUserIdToUserName(WebmaklerUser $details): bool
    {
        $member = $this->teammemberRepo->findOneByFioUserName($details->userName);
        if ($member === null) {
            return false;
        }

        $data = [
            'tx_dbi_teammember' => [
                $member->getUid() => [
                    'fio_user_id' => $details->id,
                ],
            ],
        ];
        $this->dataHandler->start($data, []);
        $this->dataHandler->process_datamap();
        $this->checkDataHandlerError();

        return true;
    }

    /**
     * Create a new team member or update the existing record
     *
     * @return bool If an update was made
     */
    protected function updateOrCreateMember(?Teammember $member, WebmaklerUser $details): bool
    {
        $v   = OutputInterface::VERBOSITY_VERBOSE;
        $vv  = OutputInterface::VERBOSITY_VERY_VERBOSE;
        $vvv = OutputInterface::VERBOSITY_DEBUG;

        $memberData = [
            'name_lastname'   => $details->getLastname(),
            'name_firstname'  => $details->getFirstname(),
            'name_salutation' => strtolower($details->salutation),
            'name_degree'     => $details->getTitleKey(),

            'fio_username'   => $details->userName,
            'fio_group_id'   => $details->group->id,
            'function'       => $details->position,
            'identification' => $details->identification,

            'contact_telephone' => $details->phone,
            'contact_mobile'    => $details->mobile,
            'contact_fax'       => $details->fax,
            'contact_email'     => str_replace(
                '@postbank.de', '@db.com', $details->emailPortal
            ),
        ];
        if ($memberData['contact_email'] === '<EMAIL>') {
            unset($memberData['contact_email']);
        }

        $cmd = [];
        $data = [
            //predefined order is important!
            'tx_dbi_teammember' => [],
            'sys_file_reference' => [],
        ];

        if ($member === null) {
            $memberData['fio_user_id'] = $details->id;

            $memberUid  = StringUtility::getUniqueId('NEW');
            $centerPid = $this->findCenterPid($details);
            if ($centerPid === null) {
                return false;
            }

            $memberData['pid'] = $centerPid;
            $memberData['languages'] = 'de';

        } else {
            $memberUid = $member->getUid();
            $centerPid = $member->getPid();

            if ($member->hidden) {
                $this->io->writeln(' Un-hiding member', $vv);
                $memberData['hidden'] = 0;
            }

            //moved?
            $newCenterPid = $this->findCenterPid($details);
            if ($newCenterPid !== null && $newCenterPid != $centerPid) {
                $this->io->writeln(' Moving to new center (pid #' . $newCenterPid . ')', $v);
                $centerPid = $newCenterPid;

                $cmd['tx_dbi_teammember'][$memberUid] = [
                    'move' => $newCenterPid,
                ];
                //image references are moved by typo3 automatically
            }
        }

        $this->attachMemberFileToDataHandler(
            $member, $details, $centerPid,
            $memberData, $data, $cmd
        );

        $data['tx_dbi_teammember'][$memberUid] = $memberData;

        $this->dataHandler->start($data, $cmd);
        if (count($cmd)) {
            $this->dataHandler->process_cmdmap();
        }

        $this->dataHandler->process_datamap();
        $this->checkDataHandlerError();

        if (substr($memberUid, 0, 3) === 'NEW') {
            $memberUid = $this->dataHandler->substNEWwithIDs[$memberUid];
            $this->io->writeln(' New member UID: ' . $memberUid, $vvv);
        }
        $this->updatedMemberUids[$memberUid] = true;
        $this->updatedCenterPids[$centerPid] = true;

        if ($member === null) {
            $member = $this->teammemberRepo->findByUid($memberUid);
        }

        return true;
    }

    /**
     * Update address of center record address
     */
    protected function updateCenterAddressFromMember(
        Center $center, WebmaklerUser $details
    ): void {
        $v = OutputInterface::VERBOSITY_VERBOSE;
        $vvv = OutputInterface::VERBOSITY_DEBUG;

        if (!$center->isFirstGroupId($details->group->id)) {
            $this->io->writeln(' Not updating center address: Member not in first group', $vvv);
            return;
        }

        $group = $details->group;
        $centerData = [
            'address_street'     => $group->street . ' ' . $group->houseNumber,
            'address_postalcode' => $group->zip,
            'address_city'       => $group->town,
        ];
        if ($group->phone) {
            $centerData['telephone'] = trim($group->phone);
        }
        if ($group->email) {
            $centerData['email'] = trim(
                str_replace('@postbank.de', '@db.com', $group->email)
            );
        }

        $data = [
            'tx_dbi_center' => [
                $center->getUid() => $centerData
            ]
        ];

        $this->io->writeln(' Updating center address', $v);
        $this->dataHandler->start($data, []);
        $this->dataHandler->process_datamap();
        $this->checkDataHandlerError();
    }

    /**
     * Hide a member
     *
     * @return bool If the member was hidden
     */
    protected function disableMember(?Teammember $member): bool
    {
        $v   = OutputInterface::VERBOSITY_VERBOSE;
        $vv  = OutputInterface::VERBOSITY_VERY_VERBOSE;

        if ($member === null) {
            $this->io->writeln(' Non-existing member does not need to be hidden', $v);
            return false;
        }

        if ($member->hidden) {
            $this->io->writeln(' Already hidden', $vv);
            return false;
        }

        $this->io->writeln(
            ' Hiding #' . $member->getUid() . ' ' . $member->getFullName(), $vv
        );

        $data = [
            'tx_dbi_teammember' => [
                $member->getUid() => [
                    'hidden' => 1,
                ],
            ],
        ];
        $this->dataHandler->start($data, []);
        $this->dataHandler->process_datamap();
        $this->checkDataHandlerError();

        return true;
    }

    protected function attachMemberFileToDataHandler(
        ?Teammember $member, WebmaklerUser $details, int $centerPid, &$memberData, &$data, &$cmd
    ): void {
        $vv  = OutputInterface::VERBOSITY_VERY_VERBOSE;
        $vvv = OutputInterface::VERBOSITY_DEBUG;

        $addImage = false;
        $imageRef = $member ? $member->getImageNoFallback() : null;

        if ($details->picture === null && $imageRef !== null) {
            //delete image
            //we keep the old image for now because not all webmakler users
            // have images
            return;

            $cmd['sys_file_reference'][$imageRef->getUid()]['delete'] = 1;
            $memberData['image'] = 0;
            $this->io->writeln(' Deleting image', $vv);

        } elseif ($details->picture !== null && $imageRef === null) {
            //add image
            $addImage = true;
            $this->io->writeln('  Adding image', $vv);

        } elseif ($details->picture !== null && $imageRef !== null) {
            //both have an image. let's see if it changed.
            $pictureBytes = $details->getDecodedPicture();
            $pictureHash = md5($pictureBytes);
            $origFile = $imageRef->getOriginalFile();

            if ($pictureHash . '.jpg' != $origFile->getName()) {
                $this->io->writeln(' Image changed', $vv);
                $addImage = true;
            } else {
                $this->io->writeln(' Image unchanged', $vv);
            }
        }

        if ($addImage) {
            $pictureBytes = $details->getDecodedPicture();
            $pictureHash = md5($pictureBytes);
            $storageFilePath = 'teammember/' . $details->userName . '/' . $pictureHash . '.jpg';

            //FIXME: correct solution
            $storageFilePath = str_replace(
                ['ä', 'ö', 'ü', 'ß', ' ', 'é'],
                ['ae', 'oe', 'ue', 'ss', '_', 'e'],
                $storageFilePath
            );
            $this->io->writeln('   ' . $storageFilePath, $vvv);

            if ($this->storage->hasFile($storageFilePath)) {
                $file = $this->storage->getFile($storageFilePath);
                $this->io->writeln('   file exist already', $vvv);
            } else {
                $this->io->writeln('  Uploading image', $vv);
                $tmpPath = tempnam(sys_get_temp_dir(), 'dbi_rs_');
                file_put_contents($tmpPath, $pictureBytes);

                $targetFolder = $this->getAndCreateFolder(dirname($storageFilePath));
                $targetFileName = basename($storageFilePath);

                $file = $this->storage->addFile(
                    $tmpPath, $targetFolder, $targetFileName,
                    DuplicationBehavior::CANCEL, true
                );
            }

            $tmpFileId = StringUtility::getUniqueId('NEW');
            $data['sys_file_reference'][$tmpFileId] = [
                'pid'       => $centerPid,
                'uid_local' => 'sys_file_' . $file->getUid(),
                //uid_foreign is added automatically by data handler
            ];
            $memberData['image'] = $tmpFileId;

            if ($imageRef !== null) {
                //delete old image
                $this->io->writeln('  Deleting old image', $vv);
                $cmd['sys_file_reference'][$imageRef->getUid()]['delete'] = 1;
            }
        }
    }

    /**
     * Hide all members that need to be hidden.
     * Used in the full sync and hide-missing actions.
     */
    protected function disableMissingMembers(): int
    {
        $v   = OutputInterface::VERBOSITY_VERBOSE;
        $vv  = OutputInterface::VERBOSITY_VERY_VERBOSE;

        $this->io->writeln('', $v);
        $this->io->writeln(
            'Found ' . count($this->updatedMemberUids) . ' members in webmakler', $v
        );

        $members = $this->teammemberRepo->findMissing(array_keys($this->updatedMemberUids));
        $this->io->writeln(
            ' ' . count($members) . ' local members need to be hidden', $v
        );

        foreach ($members as $member) {
            $this->disableMember($member);
        }

        return count($members);
    }

    /**
     * Find the user's center page UID, and follow redirects
     */
    protected function findCenterPid(WebmaklerUser $details): ?int
    {
        $vvv = OutputInterface::VERBOSITY_DEBUG;

        $center = $this->centerRepo->findByFioGroupId($details->group->id);
        if (!$center) {
            $this->io->error(
                'No center found for FIO node ID ' . $details->group->id
                    . ' in webmakler group data for'
                    . ' #' . $details->id
                    . ' ' . $details->name
            );
            return null;
        }

        $loop = 0;
        while ($center->redirect > 0) {
            $this->io->writeln('  Redirect "' . $center->name2 . '" to #' . $center->redirect, $vvv);
            $center = $this->centerRepo->findByUid($center->redirect);
            if ($center === null) {
                break;
            }
            if (++$loop >= 5) {
                $this->io->error('Infinite center loop: #' . $center->getUid() . ' ' . $center->name2);
                return null;
            }
        }

        if (!$center) {
            $this->io->writeln('  No final center', $vvv);
            return null;
        }

        $this->io->writeln(' Final center: ' . $center->name2, $vvv);
        return $center->getPid();
    }

    protected function checkDataHandlerError(): void
    {
        if ($this->dataHandler->errorLog !== []) {
            throw new \Exception(reset($this->dataHandler->errorLog));
        }
    }

    protected function getAndCreateFolder(string $folderPath): Folder
    {
        if ($this->storage->hasFolder($folderPath)) {
            return $this->storage->getFolder($folderPath);
        }

        $folder = $this->storage->getRootLevelFolder(false);
        $parts  = explode('/', $folderPath);
        foreach ($parts as $part) {
            if (!$this->storage->hasFolderInFolder($part, $folder)) {
                $folder = $this->storage->createFolder($part, $folder);
            } else {
                $folder = $this->storage->getFolderInFolder($part, $folder);
            }
        }

        return $folder;
    }

    protected function parseSinceOption(string $since): bool
    {
        if (is_numeric($since)) {
            //unix timestamp
            $this->since = $since;
            return true;
        }

        //normal date or interval
        $timestamp = strtotime($since);
        if ($timestamp !== false) {
            $this->since = $timestamp;
            return true;
        }

        //interval?
        try {
            $interval = new \DateInterval(strtoupper('P' . $since));
            $now = new \DateTime();
            $now->sub($interval);
            $this->since = $now->getTimestamp();
        } catch (\Exception $e) {
            $this->io->error('Invalid "since" option value: ' . $since);
            return false;
        }

        return true;
    }

    /**
     * Retry API calls when we get a "502 Bad Gateway" response 3 times
     */
    protected function retryOnApiFailure($functionToCall, int $try = 1): mixed
    {
        $v  = OutputInterface::VERBOSITY_VERBOSE;
        if ($try === 2) {
            $this->io->writeln(' retrying API call', $v);
        } elseif ($try === 3) {
            $this->io->writeln(' retrying API call a second time in 10s', $v);
            sleep(10);
        } elseif ($try === 4) {
            $this->io->writeln(' retrying API call a third time in 30s', $v);
            sleep(30);
        }

        try {
            return $functionToCall();
        } catch (\GuzzleHttp\Exception\ServerException $e) {
            if ($e->getCode() === 502) {
                if ($try >= 4) {
                    throw $e;
                }
                return $this->retryOnApiFailure($functionToCall, $try + 1);
            }
        }
    }
}
