<?php

namespace Mogic\Dbi\Command;

use Mogic\Dbi\Backend\TceMainHook;
use Mogic\Dbi\Domain\Model\Center;
use Mogic\Dbi\Domain\Model\WebmaklerGroup;
use Mogic\Dbi\Domain\Repository\CenterRepository;
use Mogic\Dbi\Domain\Repository\WebGroupRepository;
use Mogic\Dbi\Domain\Repository\WebGroupRepositoryCached;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use TYPO3\CMS\Core\DataHandling\DataHandler;
use TYPO3\CMS\Core\Utility\GeneralUtility;

/**
 * Synchronize centers with the FIO webmakler API.
 * and update their local records.
 *
 * Default usage in scheduler:
 * $ ./vendor/bin/typo3 dbi:centersync sync-changed --since=2d
 *
 * Development:
 * Use the "use-cache" option:
 * $ ./vendor/bin/typo3 dbi:centersync download --use-cache --cache-max-age=3600
 * $ ./vendor/bin/typo3 dbi:centersync sync-changed --since=2d
 */
class CenterSyncCommand extends Command
{
    protected int $centerFolderUid = 17;

    protected SymfonyStyle $io;
    protected OutputInterface $output;
    protected bool $force = false;
    protected ?int $limit = null;
    protected ?int $since = null;
    protected ?int $centerId = null;

    /**
     * Array of center UIDs that have been updated.
     * Key is the UID, value is "true"
     * The rest will be hidden.
     */
    protected array $updatedCenterUids = [];

    /**
     * API access to the FIO webmakler realtors
     * Either cached or uncached (see "use-cache" option).
     */
    protected WebGroupRepository $webGroupRepo;

    public function __construct(
        protected readonly DataHandler $dataHandler,
        protected readonly CenterRepository $centerRepo,
    ) {
        parent::__construct();
    }

    protected function configure(): void
    {
        $this->setHelp('Update center team member data from FIO webmakler')
            ->addOption(
                'limit',
                null,
                InputOption::VALUE_REQUIRED,
                'Number of centers to update'
            )
            ->addOption(
                'since',
                null,
                InputOption::VALUE_REQUIRED,
                'Import changes that happened since the given timestamp/date/interval'
            )
            ->addOption(
                'use-cache',
                null,
                InputOption::VALUE_NONE,
                'Cache and use cached API responses'
            )
            ->addOption(
                'cache-max-age',
                null,
                InputOption::VALUE_REQUIRED,
                'Number of seconds'
            )
            ->addArgument(
                'action',
                InputArgument::REQUIRED,
                'What sync action to execute:'
                . ' sync-changed, download',
                null,
                [
                    'sync-changed',
                    'download',
                ]
            );
    }

    /**
     * Parse input options and switch to the correct subcommand
     */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $vvv = OutputInterface::VERBOSITY_DEBUG;

        $this->io = new SymfonyStyle($input, $output);
        $this->output = $output;
        $this->limit = $input->getOption('limit');

        $since = $input->getOption('since');
        if ($since !== null) {
            if (!$this->parseSinceOption($since)) {
                return Command::INVALID;
            }
        }

        $centerRepoClass = WebGroupRepository::class;
        if ($input->getOption('use-cache')) {
            $centerRepoClass = WebGroupRepositoryCached::class;
        }
        $this->webGroupRepo = GeneralUtility::makeInstance($centerRepoClass);


        if ($input->getOption('cache-max-age') !== null) {
            if ($this->webGroupRepo instanceof WebGroupRepositoryCached) {
                $this->webGroupRepo->setMaxAgeSeconds(
                    (int) $input->getOption('cache-max-age')
                );
            }
        }

        \TYPO3\CMS\Core\Core\Bootstrap::initializeBackendAuthentication();
        TceMainHook::$showFlashMessages = false;

        $action = $input->getArgument('action');
        switch ($action) {
            case 'download':
                $this->downloadAllData();
                break;

            case 'sync-changed':
                $this->syncChangedCenters();
                break;

            default:
                $this->io->error('Invalid action');
                return Command::INVALID;
        }

        return Command::SUCCESS;
    }

    /**
     * Download all visible API users into the cache.
     *
     * Useful during development.
     */
    protected function downloadAllData(): void
    {
        if (!$this->webGroupRepo instanceof WebGroupRepositoryCached) {
            $this->io->error('Use the "--use-cache" option');
            return;
        }

        $v  = OutputInterface::VERBOSITY_VERBOSE;
        $this->io->write('API base URL: ' . $this->webGroupRepo->getApiBaseUrl(), $v);

        foreach ($this->webGroupRepo->findAllVisible() as $listCenter) {
            if ($this->centerId !== null && $this->centerId != $listCenter->id) {
                continue;
            }

            $details = $this->webGroupRepo->findOneById($listCenter->id);
            if ($details !== null) {
                $this->io->write('.');
            } else {
                $this->io->write('E');
            }
        }
        $this->io->writeln('');
    }

    /**
     * Import changed centers from the FIO Webmakler API.
     * Useful to get daily updates via the scheduler.
     */
    protected function syncChangedCenters(): void
    {
        $v   = OutputInterface::VERBOSITY_VERBOSE;
        $vv  = OutputInterface::VERBOSITY_VERY_VERBOSE;
        $vvv = OutputInterface::VERBOSITY_DEBUG;

        $numUpdated = 0;
        $numNotFound = 0;
        $numNotChanged = 0;
        $numNotRelevant = 0;

        $since = $this->since;
        if ($since === null) {
            $since = time() - 86400;
        }
        $this->io->writeln('--since: ' . date('Y-m-d H:i:s', $since), $vvv);

        foreach ($this->webGroupRepo->findAllChanged($since) as $listGroup) {
            $this->io->writeln($listGroup->shortName . ' #' . $listGroup->id, $v);

            //Find the center in the repo, fio_group_ids contains $listGroup->id
            $center = $this->centerRepo->findByFioGroupId($listGroup->id);
            //Nothing to update if not found
            if (!$center) {
                $numNotFound++;
                $this->io->writeln(
                    ' Center not found in database: '
                        . $listGroup->shortName . ' #' . $listGroup->id,
                    $v
                );
                continue;
            }
            $this->io->writeln(' Center: #' . $center->getUid() . ' ' . $center->name2, $vv);

            if (!$center->isFirstGroupId($listGroup->id)) {
                $numNotRelevant++;
                $this->io->writeln(' Not relevant since not first group ID', $vv);
                continue;
            }

            //Grab the center from the API
            $newestDetails = $this->webGroupRepo->findOneById($listGroup->id);
            //Map everything and save
            $updated = $this->updateCenter($center, $newestDetails);

            if ($updated) {
                ++$numUpdated;
                $this->io->writeln(' Updated', $vv);
            } else {
                ++$numNotChanged;
                $this->io->writeln(' No update necessary', $vv);
            }

            if ($this->limit !== null && $numUpdated >= $this->limit) {
                break;
            }
        }

        $this->io->writeln('', $v);
        $this->io->writeln('Updated: ' . $numUpdated, $v);
        $this->io->writeln('Not relevant: ' . $numNotRelevant, $v);
        $this->io->writeln('Not changed: ' . $numNotChanged, $v);
        $this->io->writeln('Not found: ' . $numNotFound, $v);
    }

    /**
     * Update a center
     *
     * @return bool If an update was made
     */
    protected function updateCenter(?Center $center, WebmaklerGroup $details): bool
    {
        $v   = OutputInterface::VERBOSITY_VERBOSE;
        $vv  = OutputInterface::VERBOSITY_VERY_VERBOSE;
        $vvv = OutputInterface::VERBOSITY_DEBUG;

        $centerData = [
            'address_street'     => trim("{$details->street} {$details->houseNumber}"),
            'address_postalcode' => trim($details->zip),
            'address_city'       => trim($details->town),
        ];
        if ($details->phone) {
            $centerData['telephone'] = trim($details->phone);
        }
        if ($details->email) {
            $centerData['email'] = trim(
                str_replace('@postbank.de', '@db.com', $details->email)
            );
        }

        if ($centerData['address_street'] === $center->addressStreet
            && $centerData['address_postalcode'] === $center->addressPostalcode
            && $centerData['address_city'] === $center->addressCity
            && (!isset($centerData['telephone']) || $centerData['telephone'] === $center->telephone)
            && (!isset($centerData['email']) || $centerData['email'] === $center->email)
        ) {
            return false;
        }

        $data = [
            'tx_dbi_center' => [],
        ];

        $data['tx_dbi_center'][$center->getUid()] = $centerData;
        $this->io->writeln(' Updating center', $vv);
        $this->dataHandler->start($data, []);
        $this->dataHandler->process_datamap();
        $this->checkDataHandlerError();
        return true;
    }

    protected function checkDataHandlerError(): void
    {
        if ($this->dataHandler->errorLog !== []) {
            throw new \Exception(reset($this->dataHandler->errorLog));
        }
    }

    protected function parseSinceOption(string $since): bool
    {
        if (is_numeric($since)) {
            //unix timestamp
            $this->since = $since;
            return true;
        }

        //normal date or interval
        $timestamp = strtotime($since);
        if ($timestamp !== false) {
            $this->since = $timestamp;
            return true;
        }

        //interval?
        try {
            $interval = new \DateInterval(strtoupper('P' . $since));
            $now = new \DateTime();
            $now->sub($interval);
            $this->since = $now->getTimestamp();
        } catch (\Exception $e) {
            $this->io->error('Invalid "since" option value: ' . $since);
            return false;
        }

        return true;
    }
}
