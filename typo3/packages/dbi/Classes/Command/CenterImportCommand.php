<?php

namespace Mogic\Dbi\Command;

use Mogic\Dbi\Backend\TceMainHook;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use TYPO3\CMS\Core\Core\Environment;
use TYPO3\CMS\Core\DataHandling\DataHandler;
use TYPO3\CMS\Core\Database\ConnectionPool;
use TYPO3\CMS\Core\Database\Query\Restriction\HiddenRestriction;
use TYPO3\CMS\Core\Resource\DuplicationBehavior;
use TYPO3\CMS\Core\Resource\Folder;
use TYPO3\CMS\Core\Resource\ResourceStorage;
use TYPO3\CMS\Core\Resource\StorageRepository;
use TYPO3\CMS\Core\Utility\GeneralUtility;
use TYPO3\CMS\Core\Utility\StringUtility;

class CenterImportCommand extends Command
{
    protected int $centerFolderUid = 17;
    protected int $centerTemplateUid = 15;

    protected SymfonyStyle $io;
    protected OutputInterface $output;

    protected ResourceStorage $storage;

    /**
     * Key is the old center page UID, value the new one
     */
    protected array $oldPageUids = [];

    protected static $expectedCsvHeaders = [
        'pbi-center-centers.csv' => [
            'pid',
            'name2',
            'fio_id',
            'loc_lat',
        ],
        'pbi-center-pages.csv' => [
            'uid',
            'title',
            'slug',
            'deleted',
            'hidden',
        ],
        'pbi-center-teammembers.csv' => [
            'pid',
            'fio_username',
            'name_lastname',
        ],
    ];

    public function __construct(
        protected readonly DataHandler $dataHandler,
        StorageRepository $storageRepository,
    ) {
        parent::__construct();
        $this->storage = $storageRepository->getDefaultStorage();
    }

    protected function configure(): void
    {
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $this->io = new SymfonyStyle($input, $output);
        $this->output = $output;

        \TYPO3\CMS\Core\Core\Bootstrap::initializeBackendAuthentication();
        TceMainHook::$updateCenterCoords = false;

        $this->importCenterPages();
        $this->importCenterRecords();
        $this->importTeammembers();

        return Command::SUCCESS;
    }

    /**
     * Create new center page records from pbi-center-pages.csv
     */
    protected function importCenterPages(): void
    {
        $v  = OutputInterface::VERBOSITY_VERBOSE;
        $vv = OutputInterface::VERBOSITY_VERY_VERBOSE;

        $qb = GeneralUtility::makeInstance(ConnectionPool::class)
            ->getQueryBuilderForTable('pages');
        $qb->getRestrictions()->removeByType(HiddenRestriction::class);

        $targetPid = $this->centerFolderUid;
        $created = 0;
        foreach ($this->readCsv('pbi-center-pages.csv') as $csvRow) {
            $csvRow['slug'] = str_replace(
                '/immobiliencenter/ihr-makler-vor-ort', '', $csvRow['slug']
            );

            $newId = $qb->select('uid')->from('pages')
                ->where(
                    $qb->expr()->eq('slug', $qb->createNamedParameter($csvRow['slug'])),
                    $qb->expr()->eq('pid', $qb->createNamedParameter($this->centerFolderUid)),
                )
                ->executeQuery()
                ->fetchOne();
            if ($newId) {
                $this->output->writeln($csvRow['slug'] . ' exists already', $vv);

            } else {
                $data = [
                    'pages' => [
                        'NEW1' => [
                            'pid'    => $targetPid,
                            'title'  => $csvRow['title'],
                            'slug'   => $csvRow['slug'],
                            'hidden' => $csvRow['hidden'],

                            'doktype'      => 7,
                            'mount_pid'    => $this->centerTemplateUid,
                            'mount_pid_ol' => 1,

                            'backend_layout' => 'pagets__dbiCenter',
                        ],
                    ],
                ];

                $this->dataHandler->start($data, []);
                $this->dataHandler->process_datamap();
                $this->checkDataHandlerError();

                $newId = $this->dataHandler->substNEWwithIDs['NEW1'];
                $this->io->writeln($csvRow['slug'] . ' (' . $csvRow['title'] . ') created', $v);
                $created++;
            }

            $this->oldPageUids[$csvRow['uid']] = $newId;
            $targetPid = -$newId;
        }

        if ($created > 0) {
            $this->io->info("Created $created center pages");
        }
    }

    /**
     * Create center records in the center pages from pbi-center-centers.csv
     */
    protected function importCenterRecords(): void
    {
        $v  = OutputInterface::VERBOSITY_VERBOSE;
        $vv = OutputInterface::VERBOSITY_VERY_VERBOSE;

        $qb = GeneralUtility::makeInstance(ConnectionPool::class)
            ->getQueryBuilderForTable('tx_dbi_centers');
        $qb->getRestrictions()->removeByType(HiddenRestriction::class);

        $created = 0;
        foreach ($this->readCsv('pbi-center-centers.csv') as $csvRow) {
            $centerPid = $csvRow['pid'];
            if (!isset($this->oldPageUids[$centerPid])) {
                //"Hameln" outside center folder
                if ($centerPid == 23 || $centerPid == 4 || $centerPid == 150) {
                    continue;
                }
                //old center folder, also wrong/broken
                if ($centerPid == 20) {
                    continue;
                }
                //Vorlageort
                if ($centerPid == 203) {
                    continue;
                }
                if ($csvRow['name2'] == 'Zentrale Hameln') {
                    continue;
                }
                throw new \Exception(
                    'No new page ID found for center ' . $csvRow['name2'] . ', old pid #' . $centerPid
                );
            }
            $centerPid = $this->oldPageUids[$centerPid];

            $centerUid = $qb->select('uid')->from('tx_dbi_center')
                ->where(
                    $qb->expr()->eq('pid', $qb->createNamedParameter($centerPid)),
                )
                ->executeQuery()
                ->fetchOne();
            if ($centerUid) {
                $this->output->writeln($csvRow['name2'] . ' exists already', $vv);
                continue;
            }

            $dbRow = $csvRow;
            unset($dbRow['uid']);
            unset($dbRow['image_pageheader']);
            unset($dbRow['image_mainpage']);
            unset($dbRow['image_search']);
            unset($dbRow['image_awards']);
            $dbRow['pid'] = $centerPid;
            // "1234 oder 4567"
            $dbRow['telephone'] = preg_replace('# oder.+$#', '', $dbRow['telephone']);
            $dbRow['fax']       = preg_replace('# oder.+$#', '', $dbRow['fax']);

            if (strlen($dbRow['telephone']) > 32) {
                $this->output->writeln(' Removing telephone, too long: ' . $dbRow['telephone'], $v);
                unset($dbRow['telephone']);
            }


            $newRecordUid = StringUtility::getUniqueId('NEW');
            $data = [
                'tx_dbi_center' => [
                    $newRecordUid => $dbRow,
                ],

                'sys_file_reference' => [],
            ];
            $data = $this->attachFilesToDataHandler(
                $data, $csvRow['image_pageheader'], 'tx_dbi_center', 'image_pageheader',
                $newRecordUid, $centerPid
            );
            $data = $this->attachFilesToDataHandler(
                $data, $csvRow['image_mainpage'], 'tx_dbi_center', 'image_mainpage',
                $newRecordUid, $centerPid
            );
            $data = $this->attachFilesToDataHandler(
                $data, $csvRow['image_search'], 'tx_dbi_center', 'image_search',
                $newRecordUid, $centerPid
            );
            $data = $this->attachFilesToDataHandler(
                $data, $csvRow['image_awards'], 'tx_dbi_center', 'image_awards',
                $newRecordUid, $centerPid
            );

            $this->dataHandler->start($data, []);
            $this->dataHandler->process_datamap();
            $this->checkDataHandlerError();

            $this->io->writeln($csvRow['name2'] . ' created', $v);
        }

        if ($created > 0) {
            $this->io->info("Created $created center records");
        }
    }

    /**
     * Create team member records in the center pages from pbi-center-teammembers.csv
     */
    protected function importTeammembers(): void
    {
        $v  = OutputInterface::VERBOSITY_VERBOSE;
        $vv = OutputInterface::VERBOSITY_VERY_VERBOSE;

        $qb = GeneralUtility::makeInstance(ConnectionPool::class)
            ->getQueryBuilderForTable('tx_dbi_teammember');
        $qb->getRestrictions()->removeByType(HiddenRestriction::class);

        $created = 0;
        foreach ($this->readCsv('pbi-center-teammembers.csv') as $csvRow) {
            $centerPid = $csvRow['pid'];
            if (!isset($this->oldPageUids[$centerPid])) {
                if ($centerPid == 4129 || $centerPid == 6439) {
                    //deleted on old system
                    continue;
                }
                throw new \Exception(
                    'No new page ID found for team member ' . $csvRow['fio_username'] . ', old pid #' . $centerPid
                );
            }
            $centerPid = $this->oldPageUids[$centerPid];

            $memberUid = $qb->select('uid')->from('tx_dbi_teammember')
                ->where(
                    $qb->expr()->eq('fio_username', $qb->createNamedParameter($csvRow['fio_username'])),
                    $qb->expr()->eq('name_lastname', $qb->createNamedParameter($csvRow['name_lastname'])),
                    $qb->expr()->eq('name_firstname', $qb->createNamedParameter($csvRow['name_firstname'])),
                    $qb->expr()->eq('pid', $qb->createNamedParameter($centerPid)),
                )
                ->executeQuery()
                ->fetchOne();
            if ($memberUid) {
                $this->output->writeln('member ' . $csvRow['fio_username'] . ' exists already', $vv);
                continue;
            }

            $dbRow = $csvRow;
            unset($dbRow['pid']);
            unset($dbRow['awards_capital_image']);
            unset($dbRow['image']);
            unset($dbRow['image_awards']);
            $dbRow['pid'] = $centerPid;

            $newRecordUid = StringUtility::getUniqueId('NEW');
            $data = [
                'tx_dbi_teammember' => [
                    $newRecordUid => $dbRow,
                ],

                'sys_file_reference' => [],
            ];
            $data = $this->attachFilesToDataHandler(
                $data, $csvRow['awards_capital_image'], 'tx_dbi_teammember', 'awards_capital_image',
                $newRecordUid, $centerPid
            );
            $data = $this->attachFilesToDataHandler(
                $data, $csvRow['image'], 'tx_dbi_teammember', 'image',
                $newRecordUid, $centerPid
            );
            $data = $this->attachFilesToDataHandler(
                $data, $csvRow['image_awards'], 'tx_dbi_teammember', 'image_awards',
                $newRecordUid, $centerPid
            );

            $this->dataHandler->start($data, []);
            $this->dataHandler->process_datamap();
            $this->checkDataHandlerError();

            $this->io->writeln($csvRow['fio_username'] . ' ' . $csvRow['name_lastname'] . ' created', $v);
        }

        if ($created > 0) {
            $this->io->info("Created $created team member records");
        }
    }

    /**
     * Modify a DataHandler $data array by adding sys_file_reference entries
     * for new files that get uploaded
     *
     * @param ?string $csvFileUrls Comma separated string of file URLs
     * @param string  $table       Table of the target record
     * @param string  $tableField  Field in the target record's table
     * @param string  $recordUid   ID or NEW* placeholder
     */
    protected function attachFilesToDataHandler(
        array $data, ?string $csvFileUrls, string $table, string $tableField,
        string $recordUid, int $recordPid
    ): array {
        if ($csvFileUrls == '') {
            return $data;
        }

        $fileUrls = array_filter(explode(',', $csvFileUrls));
        $tmpFileIds = [];
        foreach ($fileUrls as $fileUrl) {
            $localFilePath = parse_url($fileUrl, PHP_URL_PATH);
            $localFilePath = str_replace('/fileadmin/user_upload/', '', $localFilePath);
            $localFilePath = strtolower($localFilePath);
            if (!str_starts_with($localFilePath, 'center/')) {
                $localFilePath = 'center/' . $localFilePath;
            }

            $localFilePath = str_replace(
                ['ä', 'ö', 'ü', 'ß', ' '],
                ['ae', 'oe', 'ue', 'ss', '_'],
                $localFilePath
            );

            if ($this->storage->hasFile($localFilePath)) {
                $file = $this->storage->getFile($localFilePath);

            } else {
                $this->io->writeln('  Uploading ' . $localFilePath);
                // "center/Regensburg Süd und Kehlheim/Bild_Kathleen_Walter.jpg"
                $fileUrl = str_replace(
                    [' ', 'ä', 'ö', 'ü', 'Ä', 'Ö', 'Ü', 'ß'],
                    ['%20', '%C3%A4', '%C3%B6', '%C3%BC', '%C3%84', '%C3%96', '%C3%9C', '%C3%9F'],
                    $fileUrl
                );
                $tmpPath = tempnam(sys_get_temp_dir(), 'dbi_ci_');

                $dlOk = copy($fileUrl, $tmpPath);
                if (!$dlOk) {
                    throw new \Exception('Failed to download file: ' . $fileUrl);
                }

                $targetFolder = $this->getAndCreateFolder(dirname($localFilePath));
                $targetFileName = basename($localFilePath);

                $file = $this->storage->addFile(
                    $tmpPath, $targetFolder, $targetFileName,
                    DuplicationBehavior::CANCEL, true
                );
            }

            $tmpFileId = StringUtility::getUniqueId('NEW');
            $data['sys_file_reference'][$tmpFileId] = [
                'uid_local' => 'sys_file_' . $file->getUid(),
                'pid'       => $recordPid,
            ];
            $tmpFileIds[] = $tmpFileId;
        }

        $data[$table][$recordUid][$tableField] = implode(',', $tmpFileIds);

        return $data;
    }

    protected function getAndCreateFolder(string $folderPath): Folder
    {
        if ($this->storage->hasFolder($folderPath)) {
            return $this->storage->getFolder($folderPath);
        }

        $folder = $this->storage->getRootLevelFolder(false);
        $parts  = explode('/', $folderPath);
        foreach ($parts as $part) {
            if (!$this->storage->hasFolderInFolder($part, $folder)) {
                $folder = $this->storage->createFolder($part, $folder);
            } else {
                $folder = $this->storage->getFolderInFolder($part, $folder);
            }
        }

        return $folder;
    }

    protected function readCsv(string $filename): \Generator
    {
        $file = Environment::getProjectPath() . '/' . $filename;
        if (!file_exists($file)) {
            throw new \Exception('CSV file missing: ' . $file);
        }

        $hdl = fopen($file, 'r');
        if (fread($hdl, 3) != "\xEF\xBB\xBF") {
            //no BOM found, reset
            rewind($hdl);
        }

        $headers = fgetcsv($hdl, 8192);

        foreach (static::$expectedCsvHeaders[$filename] as $key) {
            if (array_search($key, $headers) === false) {
                throw new \Exception('CSV headers are not as expected');
            }
        }

        while (($data = fgetcsv($hdl, 8192)) !== false) {
            yield array_combine($headers, $data);
        }
    }

    protected function checkDataHandlerError(): void
    {
        if ($this->dataHandler->errorLog !== []) {
            throw new \Exception(reset($this->dataHandler->errorLog));
        }
    }
}
