<?php

namespace Mogic\Dbi\Controller;

use Mogic\Dbi\Domain\Repository\CenterRepository;
use Mogic\Dbi\Helper\QueryBuilderPaginator;
use Psr\Http\Message\ResponseInterface;
use TYPO3\CMS\Core\Database\ConnectionPool;
use TYPO3\CMS\Core\Pagination\SlidingWindowPagination;
use TYPO3\CMS\Core\Service\FlexFormService;
use TYPO3\CMS\Core\Utility\GeneralUtility;

/**
 * Show a list of projects
 *
 * <AUTHOR> <<EMAIL>>
 */
class ProjectListController extends \TYPO3\CMS\Extbase\Mvc\Controller\ActionController
{
    public function __construct(
        protected readonly FlexFormService $flexFormService,
    ) {
    }

    /**
     * List selected projects
     */
    public function sliderAction(): ResponseInterface
    {
        $this->settings['projects'] = $this->settings['projects'] ?? '';
        $projectPageIds = array_map('intval', explode(',', $this->settings['projects']));

        $queryBuilder = GeneralUtility::makeInstance(ConnectionPool::class)
            ->getQueryBuilderForTable('pages');
        $projectPages = $queryBuilder->select(
            'uid', 'title', 'projectdata', 'media'
        )
            ->from('pages')
            ->where($queryBuilder->expr()->in('uid', $projectPageIds))
            ->executeQuery()
            ->fetchAllAssociative();

        //make sure project order is correct
        $keyedProjectPages = [];
        foreach ($projectPages as $projectPage) {
            $keyedProjectPages[$projectPage['uid']] = $this->prepareProjectPageRow(
                $projectPage
            );
        }

        $projectPages = [];
        foreach ($projectPageIds as $projectPageId) {
            if (isset($keyedProjectPages[$projectPageId])) {
                $projectPages[$projectPageId] = $keyedProjectPages[$projectPageId];
            }
        }


        $this->view->assign('projects', $projectPages);
        return $this->htmlResponse();
    }

    /**
     * List all projects of a given category
     *
     * 1-based pagination.
     */
    public function listAction(): ResponseInterface
    {
        $categoryId = null;
        if (isset($this->settings['category']) && $this->settings['category'] > 0) {
            $categoryId = intval($this->settings['category']);
        }

        $order    = $this->settings['order'] ?? 'titleAsc';
        $pageSize = $this->settings['pageSize'] ?? 20;

        $pageNum = intval($this->request->getAttribute('routing')->get('dbipage'));
        if ($pageNum === 0) {
            $pageNum = 1;
        }

        $queryBuilder = GeneralUtility::makeInstance(ConnectionPool::class)
            ->getQueryBuilderForTable('pages');
        $query = $queryBuilder->select(
            'uid', 'title', 'projectdata', 'media'
        )
            ->from('pages')
            ->where($queryBuilder->expr()->eq('doktype', 11));

        if ($categoryId) {
            //we cannot use CategoryCollection because it doesn't support paging
            $query->join('pages', 'sys_category_record_mm', 'catmm')
                ->where(
                    $queryBuilder->expr()->eq(
                        'catmm.tablenames', $queryBuilder->createNamedParameter('pages')
                    ),
                    $queryBuilder->expr()->eq(
                        'catmm.fieldname', $queryBuilder->createNamedParameter('categories')
                    ),
                    $queryBuilder->expr()->eq('catmm.uid_foreign', 'pages.uid'),
                    $queryBuilder->expr()->eq('catmm.uid_local', $categoryId)
                );
        }

        $totalNum = (clone $query)->count('uid')->executeQuery()->fetchOne();

        if ($order === 'titleAsc') {
            $query->orderBy('title');

        } elseif ($order === 'dateDesc') {
            $query->add('orderBy', 'COALESCE(lastUpdated, tstamp) DESC');

        } else {
            throw new \UnexpectedValueException('Invalid order: ' . $order);
        }

        $paginator = new QueryBuilderPaginator($query, $pageNum, $pageSize);
        $pagination = new SlidingWindowPagination($paginator, 5);

        $projectPages = $paginator->getPaginatedItems();
        foreach ($projectPages as $key => $projectPage) {
            $projectPages[$key] = $this->prepareProjectPageRow($projectPage);
        }

        $showPager = $totalNum > $pageSize;

        $this->view->assign('currentPageNumber', $pageNum);
        $this->view->assign('projects', $projectPages);
        $this->view->assign('pagination', $pagination);
        $this->view->assign('showPager', $showPager);

        return $this->htmlResponse();
    }

    /**
     * Load data from project data flexform
     */
    protected function prepareProjectPageRow(array $projectPage): array
    {
        $flexData = $this->flexFormService->convertFlexFormContentToArray(
            $projectPage['projectdata']
        );
        unset($projectPage['projectdata']);

        $projectPage['price'] = $flexData['settings']['price'] ?? null;

        $projectPage['keyfacts'] = [];
        foreach ($flexData['settings']['keyfacts'] ?? [] as $keyfact) {
            $key   = $keyfact['container']['key'];
            $value = $keyfact['container']['value'];
            $projectPage['keyfacts'][$key] = $value;
        }

        return $projectPage;
    }
}
