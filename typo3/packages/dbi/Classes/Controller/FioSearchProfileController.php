<?php

namespace Mogic\Dbi\Controller;

use Psr\Http\Message\ResponseInterface;
use TYPO3\CMS\Core\Configuration\ExtensionConfiguration;
use TYPO3\CMS\Core\Core\Environment;
use TYPO3\CMS\Core\Utility\GeneralUtility;
use TYPO3\CMS\Extbase\Mvc\Controller\ActionController;

/**
 * FIO Suchprofil/Suchagent
 */
class FioSearchProfileController extends ActionController
{
    public function __construct(
        protected readonly \TYPO3\CMS\Core\Http\RequestFactory $requestFactory,
        protected readonly ExtensionConfiguration $extensionConfiguration
    ) {
    }

    /**
     * Delete a search agent from the FIO API by passing the "?searchAgentIdentifier" parameter.
     */
    public function deleteAction(): ResponseInterface
    {
        $searchAgentIdentifier = $this->request->getQueryParams()['searchAgentIdentifier'] ?? null;

        $extConf = $this->extensionConfiguration->get('dbi');
        $apiPrefix = $extConf['fio']['searchProfileApiPrefix'];

        if ($searchAgentIdentifier === null || $searchAgentIdentifier === '') {
            return $this->htmlResponse('');
        }

        $url = $apiPrefix . 'emailSearchAgents/' . $searchAgentIdentifier . '/delete';

        // https://docs.guzzlephp.org/en/stable/request-options.html
        $additionalOptions = [
            'allow_redirects' => false,
            'headers' => [
                'User-Agent' => 'TYPO3',
            ],
            'http_errors' => false,
            'timeout' => 10,
        ];

        $res = $this->requestFactory->request(
            $url,
            'GET',
            $additionalOptions
        );

        $success = intval($res->getStatusCode() / 100) === 2;
        $this->view->assign('success', $success);

        if ($success) {
            $response = $this->htmlResponse();
        } else {
            $content = $res->getBody()->getContents();
            $data = json_decode($content);

            if (!is_object($data)) {
                $errorMessage = 'Cannot decode server response';
            } else {
                $errorMessage = isset($data->AdditionalInfo) && count($data->AdditionalInfo)
                    ? implode(', ', $data->AdditionalInfo)
                    : ($data->Message);
            }

            $this->view->assign('error', $errorMessage);

            $response = $this->htmlResponse()
                ->withStatus($res->getStatusCode(), $res->getReasonPhrase())
                ->withHeader('X-FIO-Error', $errorMessage);
        }

        return $response;
    }
}
