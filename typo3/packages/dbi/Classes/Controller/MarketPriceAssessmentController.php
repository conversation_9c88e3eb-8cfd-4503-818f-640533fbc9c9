<?php

namespace Mogic\Dbi\Controller;

use Psr\Http\Message\ResponseInterface;
use TYPO3\CMS\Core\Configuration\ExtensionConfiguration;

/**
 * <PERSON><PERSON><PERSON> "Marktpreiseinschätzung" widget integration
 *
 * <AUTHOR> <<EMAIL>>
 */
class MarketPriceAssessmentController extends \TYPO3\CMS\Extbase\Mvc\Controller\ActionController
{
    public function __construct(
        protected readonly ExtensionConfiguration $extensionConfiguration
    ) {
    }

    /**
     * Embed the external Maklaro Javascript code
     */
    public function showAction(): ResponseInterface
    {
        $extConf = $this->extensionConfiguration->get('dbi');
        $scriptUrl    = $extConf['maklaro']['scriptUrl'];
        $partnerId    = $extConf['maklaro']['partnerId'];
        $sharedSecret = $extConf['maklaro']['sharedSecret'];
        $leadFlowCode = $extConf['maklaro']['leadFlowCode'];

        if (strlen($this->settings['scriptUrl']) > 0) {
            $scriptUrl = $this->settings['scriptUrl'];
        }
        if (strlen($this->settings['partnerId']) > 0) {
            $partnerID = $this->settings['partnerId'];
        }
        if (strlen($this->settings['sharedSecret']) > 0) {
            $sharedSecret = $this->settings['sharedSecret'];
        }
        if (strlen($this->settings['leadFlowCode']) > 0) {
            $leadFlowCode = $this->settings['leadFlowCode'];
        }

        $jwt = $this->getJwt($partnerId, $sharedSecret);

        $this->view->assign('jwt', $jwt);
        $this->view->assign('partnerid', $partnerId);
        $this->view->assign('leadflowcode', $leadFlowCode);
        $this->view->assign('scripturl', $scriptUrl);

        return $this->htmlResponse();
    }

    /**
     * Get jwt for maklaro widget
     *
     * @param string $partnerId    partner identifier (provided from maklaro)
     * @param string $sharedSecret provided secret to generate jwt
     */
    protected function getJwt($partnerId, $sharedSecret): string
    {
        // jwt generation
        $nonce = sha1(mt_rand(100000, 999999) . time());

        $timestamp = date('Y-m-d H:i:s', time());
        $exp = time() + (2 * 24 * 60 * 60); // token will expire in 2 days

        $payload = [
            'type'       => 'init',
            'nonce'      => $nonce,
            'partner_id' => $partnerId,
            'timestamp'  => $timestamp,
            'exp'        => $exp,
        ];

        $secret = $sharedSecret . $nonce;

        return \Firebase\JWT\JWT::encode($payload, $secret, 'HS256');
    }
}
