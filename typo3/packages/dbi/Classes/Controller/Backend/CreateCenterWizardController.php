<?php

namespace Mogic\Dbi\Controller\Backend;

use TYPO3\CMS\Backend\Routing\UriBuilder;
use TYPO3\CMS\Backend\Template\ModuleTemplateFactory;
use TYPO3\CMS\Backend\Utility\BackendUtility;
use TYPO3\CMS\Core\Database\ConnectionPool;
use TYPO3\CMS\Core\DataHandling\DataHandler;
use TYPO3\CMS\Core\Messaging\FlashMessage;
use TYPO3\CMS\Core\Messaging\FlashMessageService;
use TYPO3\CMS\Core\Type\ContextualFeedbackSeverity;
use TYPO3\CMS\Core\Utility\GeneralUtility;
use TYPO3\CMS\Core\Utility\HttpUtility;
use TYPO3\CMS\Extbase\Mvc\Controller\ActionController;
use Psr\Http\Message\ResponseInterface;

/**
 * Backend wizard to create a new center
 *
 * <AUTHOR> <<EMAIL>>
 */
class CreateCenterWizardController extends ActionController
{
    protected int $centerFolderUid;
    protected int $centerTemplateUid;
    protected int $createdCenterPid;

    public function __construct(
        protected readonly ModuleTemplateFactory $moduleTemplateFactory,
        protected readonly DataHandler $dataHandler,
    ) {
    }

    /**
     * Show the wizard
     *
     * @return string HTML
     */
    public function mainAction(): ResponseInterface
    {
        $this->loadPids();

        if (isset($_POST['data'])) {
            $data = $_POST['data'];

            $res = $this->validateData($data, $this->getCenterValidationRules());
            if ($res === true) {
                $ok = $this->createCenter(
                    $data['name2'], $data['fio_id'], $data['slug']
                );
                if ($ok) {
                    BackendUtility::setUpdateSignal('updatePageTree');
                    $uriBuilder = GeneralUtility::makeInstance(UriBuilder::class);
                    return $this->redirectToUri(
                        $uriBuilder->buildUriFromRoute(
                            'web_layout',
                            ['id' => $this->createdCenterPid]
                        )
                    );
                }

            } else {
                $this->view->assign('errors', $res);
            }
        }

        $this->view->assign('data', $data ?? []);
        $this->view->setTemplate('form');
        $moduleTemplate = $this->moduleTemplateFactory->create($this->request);
        $moduleTemplate->setContent($this->view->render());
        return $this->htmlResponse($moduleTemplate->renderContent());
    }

    /**
     * Create a new center
     *
     * @return boolean True if all was ok, false if not.
     *                 $this->error is filled with the message html code
     */
    protected function createCenter(string $name2, string $fioId, string $slug): bool
    {
        //find correct position in tree
        $targetPid = $this->centerFolderUid;
        if ($targetPid == 0) {
            throw new \Exception(
                'Center target page (TypoScript pids.centers) not set'
            );
        }

        $qb = GeneralUtility::makeInstance(ConnectionPool::class)
            ->getQueryBuilderForTable('pages');
        $rows = $qb->select('uid', 'title')
            ->from('pages')
            ->where(
                $qb->expr()->eq('pid', $qb->createNamedParameter($this->centerFolderUid)),
            )
            ->orderBy('title')
            ->executeQuery()
            ->fetchAllAssociative();
        foreach ($rows as $row) {
            if (strcasecmp($row['title'], $name2) < 0) {
                $targetPid = -$row['uid'];
            } else {
                break;
            }
        }
        if ($targetPid == 0) {
            throw new \Exception('Center target page broken');
        }

        try {
            //create center page + center record
            $data = [
                'pages' => [
                    'NEW1' => [
                        'pid'    => $targetPid,
                        'title'  => $name2,
                        'slug'   => '/' . $slug,
                        'hidden' => 0,

                        'doktype'      => 7,
                        'mount_pid'    => $this->centerTemplateUid,
                        'mount_pid_ol' => 1,

                        'backend_layout' => 'pagets__dbiCenter',
                    ],
                ],

                'tx_dbi_center' => [
                    'NEW2' => [
                        'pid'    => 'NEW1',
                        'name2'  => $name2,
                        'fio_id' => $fioId,
                        'hidden' => 0,
                    ],
                ],
            ];

            $this->dataHandler->start($data, []);
            $this->dataHandler->process_datamap();

            if ($this->dataHandler->errorLog !== []) {
                throw new \Exception(reset($this->dataHandler->errorLog));
            }

            $this->createdCenterPid = $this->dataHandler->substNEWwithIDs['NEW1'];

        } catch (\Exception $e) {
            $fms = GeneralUtility::makeInstance(FlashMessageService::class);
            $fms->getMessageQueueByIdentifier()->addMessage(
                GeneralUtility::makeInstance(
                    FlashMessage::class,
                    $e->getMessage(), 'Centeranlegefehler',
                    ContextualFeedbackSeverity::ERROR
                )
            );
            return false;
        }

        $fms = GeneralUtility::makeInstance(FlashMessageService::class);
        $fms->getMessageQueueByIdentifier()->addMessage(
            GeneralUtility::makeInstance(
                FlashMessage::class,
                'Immobiliencenter wurde angelegt', '',
                ContextualFeedbackSeverity::OK, true
            )
        );
        return true;
    }

    /**
     * Validation rules for a new center
     *
     * @return array Rules
     */
    protected function getCenterValidationRules()
    {
        return [
            'name2' => [
                new \TYPO3\CMS\Extbase\Validation\Validator\NotEmptyValidator(),
                new \Mogic\Dbi\Validator\CallbackValidator(
                    'Center Name 2 exists already',
                    [$this, 'checkName2']
                )
            ],
            'slug' => [
                new \TYPO3\CMS\Extbase\Validation\Validator\NotEmptyValidator(),
                new \Mogic\Dbi\Validator\CallbackValidator(
                    'URL slug exists already',
                    [$this, 'checkSlug']
                )
            ]
        ];
    }

    /**
     * Check if the given center name2 is already in use
     *
     * @param string $value Name to check
     *
     * @return boolean True if it is not in use, false if there is already
     *                 a record
     */
    public function checkName2($value)
    {
        $queryBuilder = GeneralUtility::makeInstance(ConnectionPool::class)
            ->getQueryBuilderForTable('tx_dbi_center');
        $count = $queryBuilder->count('uid')
            ->from('tx_dbi_center')
            ->where(
                $queryBuilder->expr()->eq('name2', $queryBuilder->createNamedParameter($value))
            )
            ->executeQuery()
            ->fetchOne();

        return $count == 0;
    }

    /**
     * Check if the given center page slug is already in use
     *
     * @param string $value Slug to check
     *
     * @return boolean True if it is not in use, false if there is already
     *                 a record
     */
    public function checkSlug($value)
    {
        $qb = GeneralUtility::makeInstance(ConnectionPool::class)
            ->getQueryBuilderForTable('pages');
        $count = $qb->count('uid')
            ->from('pages')
            ->where(
                $qb->expr()->eq('pid', $qb->createNamedParameter($this->centerFolderUid)),
                $qb->expr()->eq('slug', $qb->createNamedParameter('/' . $value))
            )
            ->executeQuery()
            ->fetchOne();

        return $count == 0;
    }

    /**
     * Validate the form data
     *
     * @param array $data  Array with: name, name2, fio_id and slug
     * @param array $rules Validation rules
     *
     * @return boolean|array True if all is fine, array of error messages
     *                       otherwise
     */
    protected function validateData(array $data, array $rules)
    {
        $errors = [];
        foreach ($rules as $field => $fValidators) {
            foreach ($fValidators as $validator) {
                $res = $validator->validate($data[$field]);
                if ($res->hasErrors()) {
                    $errors[$field] = $res->getFirstError()->getMessage();
                    break;
                }
            }
        }

        if (count($errors)) {
            return $errors;
        }
        return true;
    }

    protected function loadPids(): void
    {
        $this->centerFolderUid = (int) $this->settings['centerFolderUid'];
        $this->centerTemplateUid = (int) $this->settings['centerTemplateUid'];
    }
}
