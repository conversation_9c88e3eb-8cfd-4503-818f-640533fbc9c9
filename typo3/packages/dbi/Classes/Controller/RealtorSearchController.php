<?php

namespace Mogic\Dbi\Controller;

use Mogic\Dbi\Domain\Repository\TeammemberRepository;
use Mogic\Dbi\Domain\Repository\TradingareaRepository;
use Psr\Http\Message\ResponseInterface;
use TYPO3\CMS\Core\Utility\GeneralUtility;

/**
 * Search for realtors (Team members with at least one trading area)
 *
 * <AUTHOR> <<EMAIL>>
 */
class RealtorSearchController extends \TYPO3\CMS\Extbase\Mvc\Controller\ActionController
{
    protected $pidFolderCenters  = 20;

    /**
     * Lookup team members
     *
     * @var TeammemberRepository
     */
    protected $teammemberRepo;

    /**
     * Lookup trading areas
     *
     * @var TradingareaRepository
     */
    protected $tradingareaRepo;

    /**
     * Show the search form and search if the term is not empty
     *
     * @param string $q Search term
     */
    public function resultsAction($q = null): ResponseInterface
    {
        $queryParams = $this->request->getQueryParams();
        if ($this->request->getAttribute('routing')->getPageType() == 5) {
            return new \TYPO3\CMS\Extbase\Http\ForwardResponse('autocomplete');
        }

        if ($q === null && isset($queryParams['q']) && is_scalar($queryParams['q'])
            && mb_check_encoding($queryParams['q'], 'utf-8')
        ) {
            $q = $queryParams['q'];
        }
        if (!is_string($q)) {
            $q = null;
        }
        $q = trim($q);

        $members = [];
        $searchMode = null;
        $error = null;
        $hasMoreResults = false;
        if (ctype_digit(substr($q, 0, 5))) {
            //search for postal code
            $postalCode = substr($q, 0, 5);
            $searchMode = 'postalCode';

            if (strlen($postalCode) < 5) {
                $error = 'Die Postleitzahl muss aus 5 Ziffern bestehen';
            } else {
                $member = $this->teammemberRepo->findByPostalCode($postalCode)
                    ->getFirst();
                if ($member) {
                    $members[] = $member;
                }
            }

        } else {
            //search for realtor name (team member name)
            $searchMode = 'realtorName';

            $parts = static::splitQuery($q);

            if (count($parts) === 0) {
                $error = 'Kein Suchbegriff angegeben';
            } else {
                $minLength = min(array_map('strlen', $parts));
                if ($minLength == 1) {
                    $error = 'Suchbegriff zu kurz.'
                           . ' Sie müssen mindestens zwei Buchstaben angeben.';
                } else {
                    $members = $this->teammemberRepo->findByName($parts, 11);
                    if (count($members) > 10) {
                        $members = array_slice($members, 0, 10);
                        $hasMoreResults = true;
                    }
                }
            }
        }

        $this->view->assign('error', $error);
        $this->view->assign('hasMoreResults', $hasMoreResults);
        $this->view->assign('members', $members);
        $this->view->assign('q', $q);
        $this->view->assign('searchMode', $searchMode);
        $this->loadTargetPage();

        return $this->htmlResponse();
    }

    /**
     * Configure JSON view for autocompletion
     */
    public function initializeAutocompleteAction(): void
    {
        $this->defaultViewObjectName = \TYPO3\CMS\Extbase\Mvc\View\JsonView::class;
    }

    /**
     * JSON autocompletion
     *
     * @param string $q Search term
     */
    public function autocompleteAction($q = null): ResponseInterface
    {
        $queryParams = $this->request->getQueryParams();
        if ($q === null && isset($queryParams['q']) && is_scalar($queryParams['q'])
            && mb_check_encoding($queryParams['q'], 'utf-8')
        ) {
            $q = $queryParams['q'];
        }
        if (!is_string($q)) {
            $q = null;
        }
        $q = trim($q);

        $members = [];
        $tradingareas = [];

        if ($q === '') {
            $tradingareas = [];

        } elseif (ctype_digit(substr($q, 0, 5))) {
            //search by postal code
            $postalCode = substr($q, 0, 5);

            $tradingareas = $this->tradingareaRepo->findByPostalCode(
                $postalCode
            );

        } else {
            //search by name: name of city, name of team member

            //name of member
            $parts = static::splitQuery($q);
            $members = $this->teammemberRepo->findByName($parts, 3);

            //name of city
            $tradingareas = $this->tradingareaRepo->findByName(
                $parts
            );
            foreach ($tradingareas as $area) {
                $area->configureSearchTitle($parts);
            }
        }

        foreach ($members as $key => $member) {
            $members[$key] = [
                'title' => $member->getFullName(),
                'query' => $member->nameFirstname . ' ' . $member->nameLastname,
            ];
        }

        foreach ($tradingareas as $key => $tradingarea) {
            $tradingareas[$key] = [
                'title' => $tradingarea->getSearchtitle(),
                'query' => $tradingarea->postalcode,
            ];
        }

        $this->view->setVariablesToRender(['tradingareas', 'members']);
        $this->view->assign('tradingareas', $tradingareas);
        $this->view->assign('members', $members);
        return $this->jsonResponse();
    }

    /**
     * Show the realtor search bar
     *
     * @param string $q Search query
     */
    public function searchbarAction($q = null): ResponseInterface
    {
        $queryParams = $this->request->getQueryParams();
        if ($q === null && isset($queryParams['q']) && is_scalar($queryParams['q'])
            && mb_check_encoding($queryParams['q'], 'utf-8')
        ) {
            $q = $queryParams['q'];
        }
        $q = ltrim($q);

        $this->view->assign('q', $q);
        $this->loadTargetPage();
        return $this->htmlResponse();
    }

    /**
     * Load the target page into the settings array, and make
     * it accessible to the view.
     */
    protected function loadTargetPage(): void
    {
        if (!isset($this->settings['target_page'])
            || $this->settings['target_page'] == ''
        ) {
            $contentObj = $this->configurationManager->getContentObject();
            $this->settings['target_page'] = $contentObj->data['pid'];
            $this->view->assign('settings', $this->settings);
        }
    }

    /**
     * Let extbase inject the dependency
     *
     * @param object $teammemberRepo Lookup team members
     */
    public function injectTeammemberRepo(TeammemberRepository $teammemberRepo): void
    {
        $this->teammemberRepo = $teammemberRepo;
    }

    /**
     * Let extbase inject the dependency
     *
     * @param object $tradingareaRepo Lookup trading areas
     */
    public function injectTradingareaRepo(TradingareaRepository $tradingareaRepo): void
    {
        $this->tradingareaRepo = $tradingareaRepo;
    }

    /**
     * Split an incoming search query into multiple strings
     *
     * @param string $query Search query ("albert foo")
     *
     * @return string[] Array: ["albert", "foo"]
     */
    protected static function splitQuery(string $query): array
    {
        $parts = explode(' ', $query);
        $parts = array_map('trim', $parts);
        $parts = array_filter($parts);
        return $parts;
    }
}
