<?php

namespace Mogic\Dbi\Controller;

use Mogic\Dbi\Frontend\Middleware\CanonicalOverride;
use Mogic\Dbi\Helper\HtmlFixer;
use Mogic\Dbi\Helper\HtmlParser;
use Mogic\Dbi\ViewHelpers\CenterDataViewHelper;
use GuzzleHttp\Psr7\UriResolver;
use GuzzleHttp\Psr7\Utils as Psr7Utils;
use Psr\Http\Message\ResponseInterface;
use TYPO3\CMS\Core\Core\Environment;
use TYPO3\CMS\Core\Http\ImmediateResponseException;
use TYPO3\CMS\Core\MetaTag\MetaTagManagerRegistry;
use TYPO3\CMS\Core\Resource\FileRepository;
use TYPO3\CMS\Core\Utility\GeneralUtility;
use TYPO3\CMS\Extbase\Mvc\Controller\ActionController;
use TYPO3\CMS\Frontend\ContentObject\ContentObjectRenderer;
use TYPO3\CMS\Frontend\Controller\ErrorController;
use TYPO3\CMS\Frontend\Typolink\LinkFactory;

class FioImmoProxyController extends ActionController
{
    public function __construct(
        private readonly \TYPO3\CMS\Core\Http\RequestFactory $requestFactory,
        private readonly \TYPO3\CMS\Core\Page\AssetCollector $assetCollector,
        private readonly \Mogic\Dbi\PageTitle\FioImmoProxyPageTitleProvider $titleProvider,
    ) {
    }

    public function exposeAction(?string $exposeId = null): ResponseInterface
    {
        if ($exposeId === null || $exposeId === '') {
            $ec = GeneralUtility::makeInstance(ErrorController::class);
            $response = $ec->pageNotFoundAction(
                $GLOBALS['TYPO3_REQUEST'], 'Expose ID missing'
            );
            throw new ImmediateResponseException($response, **********);
        }

        $relUrlPath = 'expose/' . $exposeId
            . '?exposeUrl=' . urlencode($this->createThisPageUrl());
        if ($this->settings['listUrl']) {
            $relUrlPath .= '&listUrl='
                . urlencode($this->createUrl($this->settings['listUrl']));
        }

        $relUrlPath .= $this->getDynArgs();

        //write the expected canonical via the middleware so that
        // this also works for partially cached respones in which
        // only the USER_INT plugin is rendered.
        CanonicalOverride::$canonicalUrl = $this->uriBuilder->reset()
            ->setCreateAbsoluteUri(true)
            ->build()
            . '/' . $exposeId;

        return $this->handlePath($relUrlPath);
    }

    public function listAction(): ResponseInterface
    {
        $relUrlPath = 'list';

        $pagingNum = $this->request->getAttribute('routing')->get('dbipage');
        if ($pagingNum != null) {
            $relUrlPath .= '/' . $pagingNum;
        }

        $relUrlPath .= '?listUrl=' . urlencode($this->createThisPageUrl());

        if ($this->settings['exposeUrl']) {
            $relUrlPath .= '&exposeUrl='
                . urlencode($this->createUrl($this->settings['exposeUrl']));
        }

        $relUrlPath .= $this->getDynArgs();

        if (isset($this->settings['useCenterData']) && $this->settings['useCenterData']
            && false === strpos($relUrlPath, 'latitude')
        ) {
            $centerData = CenterDataViewHelper::getData(null, $this->request);
            if (is_array($centerData) && $centerData['loc_lat'] && $centerData['loc_long']) {
                $radiusKm = $this->settings['defaultCenterRadiusKm'];
                if ($centerData['search_radius']) {
                    $radiusKm = $centerData['search_radius'];
                }

                $relUrlPath .= '&latitude=' . urlencode($centerData['loc_lat'])
                    . '&longitude=' . urlencode($centerData['loc_long'])
                    . '&radius=' . urlencode($radiusKm);
            }
        }

        return $this->handlePath($relUrlPath);
    }

    public function searchAction(): ResponseInterface
    {
        $relUrlPath = 'search';
        $separator = '?';
        if ($this->settings['listUrl']) {
            $relUrlPath .= '?listUrl='
                . urlencode($this->createUrl($this->settings['listUrl']));
            $separator = '&';
        }

        if ($this->settings['searchAgent']) {
            $relUrlPath .= $separator . 'searchAgentPath='
                . urlencode($this->createUrl($this->settings['searchAgent']));
            $separator = '&';
        }

        if ($this->settings['backgroundImage']) {
            $fileRepository = GeneralUtility::makeInstance(FileRepository::class);
            $fileObjects = $fileRepository->findByRelation(
                'tt_content', 'settings.backgroundImage',
                $this->request->getAttribute('currentContentObject')->data['uid']
            );
            if (count($fileObjects)) {
                $relUrlPath .= $separator . 'searchBackgroundImagePath='
                    . urlencode($fileObjects[0]->getPublicUrl());
                $separator = '&';
            }
        }

        return $this->handlePath($relUrlPath);
    }

    /**
     * On local and staging systems we want to see the upstream error messages
     * Only production gets "nice" error pages.
     */
    protected function handlePath(string $relUrlPath): ResponseInterface
    {
        $context = (string) Environment::getContext();
        $hideErrorDetails = ($context === 'Production');

        $baseUrl = $this->settings['server']
            . $this->settings['customerId']
            . '/' . $this->settings['entryPointIdentifier']
            . '/';

        $url = $baseUrl . $relUrlPath;

        // https://docs.guzzlephp.org/en/stable/request-options.html
        $additionalOptions = [
            'allow_redirects' => false,
            'headers' => [
                'User-Agent' => 'TYPO3',
            ],
            'http_errors' => false,
            'timeout' => 10,
        ];

        try {
            $fioRes = $this->requestFactory->request(
                $url,
                'GET',
                $additionalOptions
            );

        } catch (\GuzzleHttp\Exception\ConnectException $e) {
            if (strpos($e->getMessage(), 'Operation timed out') !== false) {
                //no response after 10 seconds
                if ($hideErrorDetails) {
                    $ec = GeneralUtility::makeInstance(ErrorController::class);
                    $response = $ec->unavailableAction(
                        $GLOBALS['TYPO3_REQUEST'],
                        'Connection timeout'
                    );
                    $response = $response->withHeader('X-FIO-URL', $url);
                    throw new ImmediateResponseException($response, 1704787251);
                }
            }
            throw $e;
        }

        if ($fioRes->getStatusCode() === 404) {
            $ec = GeneralUtility::makeInstance(ErrorController::class);
            $response = $ec->pageNotFoundAction(
                $GLOBALS['TYPO3_REQUEST'], 'Remote page not found'
            );
            $response = $response->withHeader('X-FIO-URL', $url);
            throw new ImmediateResponseException($response, **********);

        } elseif (intval($fioRes->getStatusCode() / 100) === 5) {
            /**
             * If devIPmask matches a ServiceUnavailableException is shown
             * Otherwise pass on the error page content from FIO, except on production
             */
            $ec = GeneralUtility::makeInstance(ErrorController::class);
            $response = $ec->unavailableAction(
                $GLOBALS['TYPO3_REQUEST'],
                'Upstream error: '
                . $fioRes->getStatusCode()
                . ' ' . $fioRes->getReasonPhrase()
            );
            $response = $response->withHeader('X-FIO-URL', $url);
            if ($hideErrorDetails) {
                throw new ImmediateResponseException($response, 1704787251);
            }

        } elseif ($fioRes->getStatusCode() !== 200) {
            if ($hideErrorDetails) {
                /**
                 * If devIPmask matches a ServiceUnavailableException is shown
                 */
                $ec = GeneralUtility::makeInstance(ErrorController::class);
                $response = $ec->unavailableAction(
                    $GLOBALS['TYPO3_REQUEST'],
                    'Upstream error: '
                    . $fioRes->getStatusCode()
                        . ' ' . $fioRes->getReasonPhrase()
                );
                $response = $response->withHeader('X-FIO-URL', $url)
                    ->withStatus($fioRes->getStatusCode(), $fioRes->getReasonPhrase());
                throw new ImmediateResponseException($response, 1704787251);
            }
        }

        $content = $fioRes->getBody()->getContents();

        $body = $this->parseContent($content, $url);
        $response = $this->htmlResponse($body);

        if (!$hideErrorDetails) {
            $response = $response->withHeader('X-FIO-URL', $url);
            if (is_int($fioRes->getStatusCode())) {
                $response = $response->withStatus(
                    $fioRes->getStatusCode(), $fioRes->getReasonPhrase()
                );
            }
        }

        return $response;
    }

    /**
     * Handle the HTML we got from FIO:
     *
     * 1. Return body contents
     * 2. Extract JS URLs and inline JS and add them to the current page
     * 3. Extract CSS URLs and inline CSS and add them to the current page
     */
    protected function parseContent(string $content, string $url): string
    {
        preg_match('#<head[^>]*>(.*)</head>.*<body[^>]*>(.*)</body>#s', $content, $matches);
        $head = $matches[1] ?? '';
        $body = $matches[2] ?? '';


        $title    = HtmlParser::getTagContent($head, 'title');
        if ($title !== null) {
            $this->titleProvider->setTitle($title);
        }

        $baseHref = HtmlParser::getAllEmptyTags($head, 'base')[0]['attributes']['href']
            ?? null;
        if ($baseHref !== null) {
            $baseUri = UriResolver::resolve(Psr7Utils::uriFor($url), Psr7Utils::uriFor($baseHref));
        } else {
            $baseUri = Psr7Utils::uriFor($url);
        }

        //CSS URLs
        $linkTags = HtmlParser::getAllEmptyTags($head, 'link');
        foreach ($linkTags as $linkTag) {
            if ($linkTag['attributes']['rel'] != 'stylesheet') {
                continue;
            }

            $href = (string) UriResolver::resolve(
                $baseUri, Psr7Utils::uriFor($linkTag['attributes']['href'])
            );
            unset($linkTag['attributes']['href']);
            $this->assetCollector->addStyleSheet(
                'fio-s-' . md5($href), $href, $linkTag['attributes'], ['priority' => true]
            );
        }

        //inline CSS
        $styleTags = HtmlParser::getAllNonEmptyTags($head, 'style');
        foreach ($styleTags as $styleTag) {
            $css = HtmlFixer::absolutifyCssLinks($styleTag['value'], $baseUri);
            $this->assetCollector->addInlineStyleSheet(
                'fio-is-' . md5($styleTag['value']),
                $css,
                $styleTag['attributes'],
                ['priority' => true]
            );
        }

        //JavaScript URLs
        $emptyScriptTags = HtmlParser::getAllEmptyTags($head, 'script');
        foreach ($emptyScriptTags as $scriptTag) {
            $jsUrl = (string) UriResolver::resolve(
                $baseUri, Psr7Utils::uriFor($scriptTag['attributes']['src'])
            );
            unset($scriptTag['attributes']['src']);
            $this->assetCollector->addJavascript(
                'fio-j-' . md5($jsUrl), $jsUrl, $scriptTag['attributes'], ['priority' => true]
            );
        }

        //inline JavaScript
        $scriptTags = HtmlParser::getAllNonEmptyTags($head, 'script');
        foreach ($scriptTags as $scriptTag) {
            $this->assetCollector->addInlineJavaScript(
                'fio-ij-' . md5($scriptTag['value']),
                $scriptTag['value'],
                $scriptTag['attributes'],
                ['priority' => true]
            );
        }

        //OpenGraph meta tags
        $metaTags = HtmlParser::getAllEmptyTags($head, 'meta');
        foreach ($metaTags as $metaTag) {
            if (!isset($metaTag['attributes']['property'])
                || substr($metaTag['attributes']['property'], 0, 3) != 'og:'
            ) {
                continue;
            }

            $metaTagManager = GeneralUtility::makeInstance(MetaTagManagerRegistry::class)
                ->getManagerForProperty($metaTag['attributes']['property']);
            $metaTagManager->addProperty(
                $metaTag['attributes']['property'],
                $metaTag['attributes']['content'],
                [], true
            );
        }

        return HtmlFixer::absolutifyBodyLinks($body, $baseUri);
    }

    /**
     * Generates URL to current page
     */
    protected function createThisPageUrl(): string
    {
        return $this->uriBuilder->reset()
            ->setCreateAbsoluteUri(true)
            ->build();
    }

    protected function createUrl(string $tcaLinkValue): string
    {
        $contentObject = GeneralUtility::makeInstance(ContentObjectRenderer::class);
        $linkFactory = GeneralUtility::makeInstance(LinkFactory::class);
        $link = $linkFactory->create(
            '',
            [
                'parameter'        => $tcaLinkValue,
                'forceAbsoluteUrl' => true,
            ],
            $contentObject
        );
        return $link->getUrl();
    }

    /**
     * Pack additional URL parameters into a string that can be appended to an URL
     *
     * @link https://wiki.mogic.com/display/DBI/FIO-Immobiliensuche
     */
    protected function getDynArgs(): string
    {
        $dynArgsStr = '';
        $dynArgs = $this->request->getAttribute('routing')->getDynamicArguments();
        if (count($dynArgs)) {
            unset($dynArgs['tx_dbi_fioimmoproxyexpose']);
            unset($dynArgs['MP']);
            $dynArgsStr .= '&' . http_build_query($dynArgs, '', '&', PHP_QUERY_RFC3986);
        }

        return $dynArgsStr;
    }
}
