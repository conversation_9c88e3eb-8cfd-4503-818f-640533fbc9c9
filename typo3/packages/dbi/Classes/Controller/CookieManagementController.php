<?php

namespace Mogic\Dbi\Controller;

use Mogic\Dbi\Frontend\Middleware\UserTracking;
use Psr\Http\Message\ResponseInterface;

/**
 * Enable/disable etracker setting on the privacy page
 */
class CookieManagementController extends \TYPO3\CMS\Extbase\Mvc\Controller\ActionController
{
    /**
     * Show the cookie status and buttons
     *
     * @return void
     */
    public function showAction(): ResponseInterface
    {
        $this->view->assign(
            'etrackerEnabled',
            UserTracking::userAllowsTracking()
        );

        $this->contentObj = $this->configurationManager->getContentObject();
        $this->view->assign(
            'uid',
            $this->contentObj->data['uid']
        );

        return $this->htmlResponse();
    }

    /**
     * Allow/disallow cookies (we only have etracker currently)
     *
     * @param bool $enable True = cookies are allowed
     *
     * @return void
     */
    public function setEtrackerAction($enable = null): ResponseInterface
    {
        if ($enable === null) {
            //invalid setting by some crawler
            return $this->redirect('show');
        }

        UserTracking::setUserAllowsTracking($enable);

        return $this->redirectToUri($this->uriBuilder->build());
    }
}
