<?php

namespace Mogic\Dbi\Controller;

use Mogic\Dbi\Domain\Repository\CenterRepository;
use Psr\Http\Message\ResponseInterface;

/**
 * Show a list of manually selected centers
 *
 * <AUTHOR> <<EMAIL>>
 */
class RealtorCenterListController extends \TYPO3\CMS\Extbase\Mvc\Controller\ActionController
{
    /**
     * Find centers
     *
     * @var CenterRepository
     */
    protected $centerRepo;

    /**
     * List the selected centers
     */
    public function listAction(): ResponseInterface
    {
        $centerIds = array_map('intval', explode(',', $this->settings['centers']));
        $randomize = (bool) $this->settings['randomize'];

        $centers = $this->centerRepo->findByIds($centerIds);
        if ($randomize) {
            shuffle($centers);
        }

        $this->view->assign('centers', $centers);
        return $this->htmlResponse();
    }

    /**
     * Let extbase inject the dependency
     *
     * @param object $centerRepo Center repository
     */
    public function injectTeammemberRepo(CenterRepository $centerRepo): void
    {
        $this->centerRepo = $centerRepo;
    }
}
