<?php

declare(strict_types=1);

namespace Mogic\Dbi\Upgrades;

use Doctrine\DBAL\ArrayParameterType;
use Symfony\Component\Console\Output\OutputInterface;
use TYPO3\CMS\Core\Database\Connection;
use TYPO3\CMS\Core\Database\ConnectionPool;
use TYPO3\CMS\Core\Database\Query\Expression\CompositeExpression;
use TYPO3\CMS\Core\Database\Query\QueryBuilder;
use TYPO3\CMS\Core\Utility\GeneralUtility;
use TYPO3\CMS\Install\Updates\ChattyInterface;
use TYPO3\CMS\Install\Updates\DatabaseUpdatedPrerequisite;
use TYPO3\CMS\Install\Updates\UpgradeWizardInterface;

final class ThemeColorUpgradeWizard implements UpgradeWizardInterface, ChattyInterface
{
    protected const THEME_VALUE_MATCHING = [
      "light-blue"      => "light-brand",
      "primary"         => "brand",
      "pale-blue"       => "white",
      "dark-blue"       => "brand",
      "cerulean"        => "brand",
      "sentinel-blue"   => "brand",
      "gray"            => "light-gray",
      "dark-gray"       => ""
    ];

    protected QueryBuilder $queryBuilder;

    public function __construct()
    {
        $this->queryBuilder = GeneralUtility::makeInstance(ConnectionPool::class)->getQueryBuilderForTable('tt_content');
    }


    /**
     * @var OutputInterface
     */
    protected $output;

    public function setOutput(OutputInterface $output): void
    {
        $this->output = $output;
    }

    /**
     * Return the identifier for this wizard
     * This should be the same string as used in the ext_localconf.php class registration
     */
    public function getIdentifier(): string
    {
        return 'dbi_themeColorUpgradeWizard';
    }

    /**
     * Return the speaking name of this wizard
     */
    public function getTitle(): string
    {
        return 'Theme Color Upgrade Wizard';
    }

    /**
     * Return the description for this wizard
     */
    public function getDescription(): string
    {
        return 'this upgrade wizard change the color values of `tx_dbi_container_background_color` and `tx_mask_theme` fields in `tt_content` database table';
    }

    private function buildWhereRestriction(string $cTypeQuery, string $field, mixed $value): CompositeExpression
    {
        if ($value === null) {
            return $this->queryBuilder->expr()->and(
                $this->queryBuilder->expr()->like(
                    'CType',
                    $this->queryBuilder->createNamedParameter('%' . $cTypeQuery . '%', Connection::PARAM_STR)
                ),
                $this->queryBuilder->expr()->in(
                    $field,
                    $this->queryBuilder->createNamedParameter(array_keys(self::THEME_VALUE_MATCHING), ArrayParameterType::STRING)
                )
            );
        }

        return $this->queryBuilder->expr()->and(
            $this->queryBuilder->expr()->eq(
                $field,
                $this->queryBuilder->createNamedParameter(
                    $value,
                    Connection::PARAM_STR
                )
            )
        );
    }

    private function buildWhereRestrictionForContainer(mixed $value = null): CompositeExpression
    {
        return $this->buildWhereRestriction(
            "container",
            "tx_dbi_container_background_color",
            $value
        );
    }

    private function buildWhereRestrictionForMaskElements(mixed $value = null): CompositeExpression
    {
        return $this->buildWhereRestriction(
            "mask",
            "tx_mask_theme",
            $value
        );
    }

    /**
     * Execute the update
     *
     * Called when a wizard reports that an update is necessary
     *
     * The boolean indicates whether the update was successful
     */
    public function executeUpdate(): bool
    {
        // Add your logic here
        $this->output->writeln('starting');

        foreach (self::THEME_VALUE_MATCHING as $currentThemeValue => $newThemeValue) {
            $result = $this->queryBuilder->update(
                'tt_content'
            )->set(
                'tx_dbi_container_background_color',
                $newThemeValue,
                true,
                Connection::PARAM_STR
            )->where(
                $this->buildWhereRestrictionForContainer($currentThemeValue),
            )->executeStatement();

            $this->output->writeln(
                sprintf("`tx_dbi_container_background_color` with value `%s` changed to `%s` (%d)", $currentThemeValue, $newThemeValue, $result)
            );

            $result = $this->queryBuilder->update(
                "tt_content",
            )->set(
                'tx_mask_theme',
                $newThemeValue,
                true,
                Connection::PARAM_STR
            )->where(
                $this->buildWhereRestrictionForMaskElements($currentThemeValue)
            )->executeStatement();

            $this->output->writeln(
                sprintf("`tx_mask_theme` with value `%s` changed to `%s` (%d)", $currentThemeValue, $newThemeValue, $result)
            );
        }

        $this->output->writeln('finished');
        return true;
    }

    /**
     * Is an update necessary?
     *
     * Is used to determine whether a wizard needs to be run.
     * Check if data for migration exists.
     *
     * @return bool Whether an update is required (TRUE) or not (FALSE)
     */
    public function updateNecessary(): bool
    {
        $containerQuery = $this->queryBuilder->select(...['tx_dbi_container_background_color'])
            ->from('tt_content')
            ->where($this->buildWhereRestrictionForContainer());

        $maskQuery = $this->queryBuilder->select(...['tx_dbi_container_background_color'])
            ->from('tt_content')
            ->where($this->buildWhereRestrictionForMaskElements());

        $containerItemsCount = $containerQuery
            ->executeQuery()
            ->rowCount();

        $maskItemsCount = $maskQuery
            ->executeQuery()
            ->rowCount();

        return $maskItemsCount + $containerItemsCount > 0;
    }

    /**
     * Returns an array of class names of prerequisite classes
     *
     * This way a wizard can define dependencies like "database up-to-date" or
     * "reference index updated"
     *
     * @return string[]
     */
    public function getPrerequisites(): array
    {
        return [
            DatabaseUpdatedPrerequisite::class,
        ];
    }
}
