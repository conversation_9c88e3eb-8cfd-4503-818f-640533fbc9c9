<?php

namespace Mogic\Dbi\Frontend\Middleware;

use Psr\Http\Message\ResponseInterface;
use Psr\Http\Message\ServerRequestInterface;
use Psr\Http\Server\RequestHandlerInterface;

/**
 * Consent cookie handling, affiliate parameter cookies
 */
class UserTracking implements \Psr\Http\Server\MiddlewareInterface
{
    /**
     * Modify the generated HTML by injecting tracking code
     *
     * @param object $request Incoming HTTP request
     * @param object $handler Next handler to call
     *
     * @return ResponseInterface HTTP response
     */
    public function process(
        ServerRequestInterface $request,
        RequestHandlerInterface $handler
    ): ResponseInterface {

        $this->handleDnt();
        $this->handleAffiliateParameters($request);

        return $handler->handle($request);
    }

    /**
     * Handles the "Do-Not-Track" header and set a cookie.
     *
     * When the Do-Not-Track-Header "DNT: 1" is set, a cookie
     * "consentCookie: dnt" is set.
     * When the "Global Privacy Control"-Header "Sec-GPC: 1" is set,
     * the cookie gets set as well.
     * This can then be used in the frontend to show a notice.
     *
     * @return void
     *
     * @link https://w3c.github.io/dnt/drafts/tracking-dnt.html
     * @link https://privacycg.github.io/gpc-spec/
     */
    protected function handleDnt()
    {
        if (isset($_COOKIE['consentCookie'])) {
            //already set, no need to do anything
            return;
        }

        if ($this->doesNotWantToBeTracked()) {
            setcookie(
                'consentCookie',
                'dnt',
                time() + 180 * 86400,
                '/',
                ''
            );
        }
    }

    /**
     * Set cookies for affiliate GET parameters
     *
     * Handled GET parameters:
     * - s_id: financeAds, stored into af_s_id cookie, 60 days
     * - emid: medialead, stored into af_emid cookie, 30 days
     *
     * Value of the cookies are the ISO date time with zulu timezone,
     * a "|" and the actual parameter value.
     */
    protected function handleAffiliateParameters(ServerRequestInterface $request): void
    {
        $queryParams = $request->getQueryParams();

        if (isset($queryParams['s_id']) && strlen($queryParams['s_id'])) {
            $cookieValue = gmdate('Y-m-d\TH:i:s\Z') . '|' . $queryParams['s_id'];
            setcookie(
                'af_s_id', $cookieValue,
                time() + 60 * 86400,
                '/',
                ''
            );
            //so that following code can check immediately
            $_COOKIE['af_s_id'] = $cookieValue;
        }

        if (isset($queryParams['emid']) && strlen($queryParams['emid'])) {
            $cookieValue = gmdate('Y-m-d\TH:i:s\Z') . '|' . $queryParams['emid'];
            setcookie(
                'af_emid', $cookieValue,
                time() + 30 * 86400,
                '/',
                ''
            );
            //so that following code can check immediately
            $_COOKIE['af_s_id'] = $cookieValue;
        }
    }

    /**
     * Check if the user sends headers to not track him.
     * This is used even before he gave consent via the cookie banner.
     */
    protected function doesNotWantToBeTracked(): bool
    {
        if (isset($_SERVER['HTTP_DNT']) && $_SERVER['HTTP_DNT'] == '1') {
            return true;
        }
        if (isset($_SERVER['HTTP_SEC_GPC']) && $_SERVER['HTTP_SEC_GPC'] == '1') {
            return true;
        }
        return false;
    }

    /**
     * Check if the user allowed cookies and tracking (via the cookie banner).
     */
    public static function userAllowsTracking(): bool
    {
        return isset($_COOKIE['consentCookie'])
            && $_COOKIE['consentCookie'] == 'yes';
    }

    /**
     * Set if the user allows cookies and tracking (via cookie management)
     *
     * @param bool $allowed True = cookies are allowed
     */
    public static function setUserAllowsTracking($allowed): void
    {
        setcookie(
            'consentCookie',
            $allowed ? 'yes' : 'no',
            time() + 180 * 86400,
            '/',
            ''
        );
        //so that following code can check immediately
        $_COOKIE['consentCookie'] = $allowed ? 'yes' : 'no';
    }

}
