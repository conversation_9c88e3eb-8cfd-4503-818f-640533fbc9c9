<?php

namespace Mogic\Dbi\Frontend\Middleware;

use GuzzleHttp\Psr7\Utils;
use Psr\Http\Message\ResponseInterface;
use Psr\Http\Message\ServerRequestInterface;
use Psr\Http\Server\RequestHandlerInterface;

/**
 * Add the correct canonical header for partially cached FIO expose pages
 */
class CanonicalOverride implements \Psr\Http\Server\MiddlewareInterface
{
    /**
     * Modify this if you want to change it in the output
     */
    public static ?string $canonicalUrl = null;

    public function process(
        ServerRequestInterface $request,
        RequestHandlerInterface $nextHandler
    ): ResponseInterface {

        $response = $nextHandler->handle($request);

        if (static::$canonicalUrl !== null) {
            $html = (string)$response->getBody();
            $html = preg_replace(
                '#<link rel="canonical" href="[^"]*"#',
                '<link rel="canonical" href="' . htmlspecialchars(static::$canonicalUrl) . '"',
                $html
            );
            $response = $response->withBody(Utils::streamFor($html));
        }
        return $response;
    }
}
