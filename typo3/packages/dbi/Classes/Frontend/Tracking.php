<?php

namespace Mogic\Dbi\Frontend;

use Mogic\Dbi\Frontend\Middleware\UserTracking;
use Psr\Http\Message\ServerRequestInterface;
use TYPO3\CMS\Extbase\Mvc\Web\Routing\UriBuilder;

class Tracking
{
    public function __construct(
        protected readonly UriBuilder $uriBuilder,
    ) {
    }

    /**
     * etracker javascript
     */
    public function getScript(
        $content, array $configuration, ServerRequestInterface $request
    ): string {
        $rootLine = $request->getAttribute('frontend.controller')->rootLine;

        $titles = array_column($rootLine, 'title');

        $et_pagename = implode(' > ', array_reverse($titles));
        $et_areas    = 'DBI';
        $et_ilevel   = count($rootLine);
        $et_url      = $this->uriBuilder->reset()
            ->setCreateAbsoluteUri(true)
            ->build();

        $js_pagename = json_encode($et_pagename);
        $js_url      = json_encode($et_url, JSON_UNESCAPED_SLASHES);

        $css_url = '//www.etracker.de/cnt_css.php'
            . '?et=B09ohb'
            . '&v=4.0'
            . '&java=n'
            . '&et_pagename=' . urlencode($et_pagename)
            . '&et_areas='
            . '&et_url=' . urlencode($et_url)
            . '&et_ilevel=' . $et_ilevel
            . '&et_se=1';
        $html_css_url = htmlspecialchars($css_url);

        $html = <<<HTM
            <script type="text/javascript">
            var et_pagename     = $js_pagename;
            var et_areas        = "";
            var et_url          = $js_url;
            var et_ilevel       = $et_ilevel;
            var et_se           = 1;
            </script>
            <script id="_etLoader" type="text/javascript" charset="UTF-8"
                data-respect-dnt="true"
                data-block-cookies="true"
                data-secure-code="B09ohb" src="//static.etracker.com/code/e.js"></script>
            <noscript><link rel="stylesheet" media="all" href="$html_css_url" /></noscript>
            HTM;

        return $html;
    }
}
