<?php

namespace Mogic\Dbi\Frontend\EventListener;

use MASK\Mask\Event\MaskAllowedFieldsEvent;

class MaskAllowedFieldsEventListener
{
    public function __invoke(MaskAllowedFieldsEvent $event): void
    {
        // Add field
        $allowingFields = [
            'header',
            'header_layout',
            'subheader',
            'media'
        ];

        foreach ($allowingFields as $allowingField) {
            $event->addField($allowingField);
        }

        // Get all allowed fields
        $allowedFields = $event->getAllowedFields();

        // Do your magic and set allowed fields
        $event->setAllowedFields($allowedFields);
    }
}
