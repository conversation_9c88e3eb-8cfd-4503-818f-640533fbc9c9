<?php

namespace Mogic\Dbi\Frontend\EventListener;

use TYPO3\CMS\Frontend\Event\ModifyPageLinkConfigurationEvent;

/**
 * Add "MP" parameter to URLs. Needed when generating sitemaps.
 */
class SitemapLinkModifier
{
    public function __invoke(ModifyPageLinkConfigurationEvent $event): void
    {
        $configuration = $event->getConfiguration();
        if (!isset($configuration['MPvar'])) {
            return;
        }

        $queryParameters = $event->getQueryParameters();
        $queryParameters['MP'] = $configuration['MPvar'];
        $event->setQueryParameters($queryParameters);
    }
}
