<?php

namespace Mogic\Dbi\Frontend\EventListener;

use TYPO3\CMS\Core\Utility\GeneralUtility;
use TYPO3\CMS\Frontend\Controller\ErrorController;
use TYPO3\CMS\Frontend\Event\AfterPageWithRootLineIsResolvedEvent;

/**
 * Send a 404 when the source mount point page is hidden.
 * Workaround for TYPO3 bug #59225.
 *
 * @link https://forge.typo3.org/issues/59225
 */
class HiddenMountPointEventListener
{
    public function __invoke(AfterPageWithRootLineIsResolvedEvent $event): void
    {
        $params = $event->getRequest()->getQueryParams();
        if (!isset($params['MP'])) {
            //not a mount point page
            return;
        }

        list($targetUid, $mountPointUid) = explode('-', $params['MP']);

        $tsfe = $event->getController();
        $page = $tsfe->sys_page->getPage($mountPointUid);
        if (!empty($page)) {
            //mount point source page is available
            return;
        }

        $response = GeneralUtility::makeInstance(ErrorController::class)->pageNotFoundAction(
            $event->getRequest(),
            'The requested page does not exist!',
            [
                'hidden' => [
                    $mountPointUid => true
                ]
            ]
        );
        $event->setResponse($response);
    }
}
