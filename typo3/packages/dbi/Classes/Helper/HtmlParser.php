<?php

namespace Mogic\Dbi\Helper;

/**
 * preg-based HTML parser to extract <link>, <script> and <style> tags from <head>.
 */
class HtmlParser
{
    public static function getTagContent(string $html, string $tagName): ?string
    {
        return static::getAllNonEmptyTags($html, $tagName)[0]['value'] ?? null;
    }

    public static function getAllEmptyTags(string $html, string $tagName): array
    {
        $tags = [];

        preg_match_all('#<' . $tagName . '[^>]*>#s', $html, $matches, PREG_SET_ORDER);
        foreach ($matches as $tagMatch) {
            $tags[] = [
                'name'       => $tagName,
                'attributes' => static::getAttributes($tagMatch[0]),
                'value'      => null,
            ];
        }

        return $tags;
    }

    public static function getAllNonEmptyTags(string $html, string $tagName): array
    {
        $tags = [];

        preg_match_all(
            '#<' . $tagName . '[^>]*>(.+?)</' . $tagName . '>#s',
            $html, $matches, PREG_SET_ORDER
        );
        foreach ($matches as $tagMatch) {
            $tags[] = [
                'name'       => $tagName,
                'attributes' => static::getAttributes($tagMatch[0]),
                'value'      => $tagMatch[1],
            ];
        }

        return $tags;
    }

    protected static function getAttributes($tagContent): array
    {
        $attributes = [];
        preg_match_all('#([a-z-]+)="([^"]+)"#', $tagContent, $attribMatches, PREG_SET_ORDER);
        foreach ($attribMatches as $key => $attribMatch) {
            $attributes[$attribMatch[1]] = $attribMatch[2];
        }

        return $attributes;
    }
}
