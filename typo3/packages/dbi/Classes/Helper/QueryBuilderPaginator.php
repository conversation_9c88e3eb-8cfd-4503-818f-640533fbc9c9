<?php

declare(strict_types=1);

namespace Mogic\Dbi\Helper;

use TYPO3\CMS\Core\Database\Query\QueryBuilder;
use TYPO3\CMS\Core\Pagination\AbstractPaginator;

/**
 * Copy of TYPO3\CMS\Extbase\Pagination\QueryResultPaginator
 * that supports a QueryBuilder as source
 */
class QueryBuilderPaginator extends AbstractPaginator
{
    private QueryBuilder $queryBuilder;

    private array $paginatedQueryResult;

    public function __construct(
        QueryBuilder $queryBuilder,
        int $currentPageNumber = 1,
        int $itemsPerPage = 10
    ) {
        $this->queryBuilder = $queryBuilder;
        $this->setCurrentPageNumber($currentPageNumber);
        $this->setItemsPerPage($itemsPerPage);

        $this->updateInternalState();
    }

    public function getPaginatedItems(): iterable
    {
        return $this->paginatedQueryResult;
    }

    protected function updatePaginatedItems(int $limit, int $offset): void
    {
        $this->paginatedQueryResult = $this->queryBuilder
            ->setMaxResults($limit)
            ->setFirstResult($offset)
            ->execute()
            ->fetchAllAssociative();
    }

    protected function getTotalAmountOfItems(): int
    {
        return (clone $this->queryBuilder)->count('uid')->executeQuery()->fetchOne();
    }

    protected function getAmountOfItemsOnCurrentPage(): int
    {
        return count($this->paginatedQueryResult);
    }
}
