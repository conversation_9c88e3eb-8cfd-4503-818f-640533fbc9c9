<?php

declare(strict_types=1);

namespace Mogic\Dbi\Helper;

use PhpOffice\PhpSpreadsheet\Writer\BaseWriter;
use GuzzleHttp\Psr7\StreamDecoratorTrait;
use Psr\Http\Message\StreamInterface;
use TYPO3\CMS\Core\Http\SelfEmittableStreamInterface;

/**
 * Generate a spreadsheet in the last moment for downloading
 */
class SpreadsheetStream implements StreamInterface, SelfEmittableStreamInterface
{
    use StreamDecoratorTrait;

    public function __construct(
        protected readonly BaseWriter $writer
    ) {
    }

    /**
     * Emit the response to stdout, as specified in SelfEmittableStreamInterface.
     */
    public function emit()
    {
        $this->writer->save('php://output');
    }

    public function isWritable(): bool
    {
        return false;
    }

    /**
     * @throws \RuntimeException
     */
    public function write(string $string): int
    {
        throw new \RuntimeException('Cannot write to a ' . self::class, 1538331852);
    }
}
