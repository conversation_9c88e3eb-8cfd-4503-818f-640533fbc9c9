<?php

namespace Mogic\Dbi\Helper;

use kamermans\OAuth2\Persistence\TokenPersistenceInterface;
use kamermans\OAuth2\Token\TokenInterface;

/**
 * Static variable cache for use as kamermans/guzzle-oauth2-subscriber persistence
 */
class StaticOAuth2Cache implements TokenPersistenceInterface
{
    protected static array $cache;

    protected string $cacheKey;

    public function __construct($cacheKey = 'guzzle-oauth2-token')
    {
        $this->cacheKey = $cacheKey;
    }

    public function saveToken(TokenInterface $token)
    {
        $this->cache[$this->cacheKey] = $token->serialize();
    }

    public function restoreToken(TokenInterface $token)
    {
        $data = $this->cache[$this->cacheKey] ?? null;
        if (!is_array($data)) {
            return null;
        }

        return $token->unserialize($data);
    }

    public function deleteToken()
    {
        unset($this->cache[$this->cacheKey]);
    }

    public function hasToken()
    {
        return isset($this->cache[$this->cacheKey]);
    }
}
