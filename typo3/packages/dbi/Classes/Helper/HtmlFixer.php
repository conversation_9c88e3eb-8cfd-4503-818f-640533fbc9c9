<?php

namespace Mogic\Dbi\Helper;

use GuzzleHttp\Psr7\UriResolver;
use GuzzleHttp\Psr7\Utils as Psr7Utils;
use Psr\Http\Message\UriInterface;

/**
 * Modify link URLs in HTML, preg-based
 */
class HtmlFixer
{
    /**
     * Make URLs in inline CSS absolute
     */
    public static function absolutifyCssLinks(string $css, UriInterface $baseUri): string
    {
        preg_match_all('#url\(([^)]+)\)#', $css, $matches, PREG_SET_ORDER);
        foreach ($matches as $urlMatch) {
            $url = trim($urlMatch[1]);
            if (substr($url, 0, 5) == 'data:'
                || substr($url, 0, 6) == '"data:'
            ) {
                continue;
            }
            $absUrl = (string) UriResolver::resolve($baseUri, Psr7Utils::uriFor($url));
            $css = str_replace($url, $absUrl, $css);
        }

        return $css;
    }

    /**
     * Make src attributes in HTML absolute
     */
    public static function absolutifyBodyLinks(string $body, UriInterface $baseUri): string
    {
        preg_match_all('#(?:src|href)="([^"]+)"#', $body, $matches, PREG_SET_ORDER);
        foreach ($matches as $srcMatch) {
            $url    = trim($srcMatch[1]);
            $absUrl = (string) UriResolver::resolve($baseUri, Psr7Utils::uriFor($url));
            $body = str_replace($url, $absUrl, $body);
        }

        return $body;
    }

}
