<?php

namespace Mogic\Dbi\Evaluation;

/**
 * Convert an empty string value to NULL when saving the TCA form field
 */
class EmptyToNullEvaluation
{
    /**
     * Server-side validation/evaluation on saving the record
     *
     * @param string $value The field value to be evaluated
     * @param string $is_in The "is_in" value of the field configuration from TCA
     * @param bool   $set   <PERSON><PERSON><PERSON> defining if the value is written to the database or not.
     *
     * @return string Evaluated field value
     */
    public function evaluateFieldValue($value, $is_in, &$set)
    {
        if ($value === '') {
            return null;
        }
        return $value ;
    }
}
