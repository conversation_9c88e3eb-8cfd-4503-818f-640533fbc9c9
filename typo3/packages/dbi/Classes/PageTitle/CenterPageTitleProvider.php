<?php

declare(strict_types=1);

namespace Mogic\Dbi\PageTitle;

use Mogic\Dbi\ViewHelpers\CenterViewHelper;
use Psr\Http\Message\ServerRequestInterface;
use TYPO3\CMS\Core\PageTitle\AbstractPageTitleProvider;

/**
 * Set the center name as page title when we have a center
 *
 * The "subtitle" field can be used to define a custom title with the
 * center name in it: "Regionale Immobilien in {ORT}"
 */
class CenterPageTitleProvider extends AbstractPageTitleProvider
{
    public function getTitle(): string
    {
        $centerData = CenterViewHelper::getModel(null, $this->getRequest());
        if ($centerData === null) {
            return '';
        }

        $pageRow = $GLOBALS['TSFE']->page;
        if ($pageRow['subtitle'] == '') {
            return $centerData->name2;
        }

        $title = $pageRow['subtitle'];
        if (strpos($title, '{ORT}') === false) {
            $title = '{ORT}: ' . $title;
        }

        return str_replace('{ORT}', $centerData->name2, $title);
    }

    protected function getRequest(): ServerRequestInterface
    {
        return $GLOBALS['TYPO3_REQUEST'];
    }
}
