<?php

namespace Mogic\Dbi\Validator;

use TYPO3\CMS\Extbase\Validation\Validator\AbstractValidator;

/**
 * Validator with a callback function
 *
 * <AUTHOR> <<EMAIL>>
 */
class CallbackValidator extends AbstractValidator
{
    protected $callback;
    protected $message;

    /**
     * Set variables
     *
     * @param string   $message  Error message
     * @param callable $callback Function or method to use for validation
     */
    public function __construct(string $message, array $callback)
    {
        $this->message  = $message;
        $this->callback = $callback;
        if (!is_callable($callback)) {
            throw new \Exception(
                'Invalid validation callback for message: ' . $this->message
            );
        }
    }

    /**
     * Checks if the given value returns true when passed to the callback
     *
     * @param mixed $value The value that should be validated
     *
     * @return void
     * @api
     */
    public function isValid($value): void
    {
        if (!call_user_func($this->callback, $value)) {
            $this->addError($this->message, 1455088787);
        }
    }
}
