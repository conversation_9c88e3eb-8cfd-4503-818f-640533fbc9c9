<?php

namespace Mogic\Dbi\Powermail\Finisher;

use Mogic\Dbi\Service\ElandersApi;
use In2code\Powermail\Domain\Model\Mail;
use In2code\Powermail\Domain\Repository\FormRepository;
use In2code\Powermail\Finisher\AbstractFinisher;
use TYPO3\CMS\Core\Utility\GeneralUtility;
use TYPO3\CMS\Core\Messaging\FlashMessage;
use TYPO3\CMS\Core\Messaging\FlashMessageService;
use TYPO3\CMS\Core\Type\ContextualFeedbackSeverity;

/**
 * Send powermail form data to Elanders MobileSales API (MSA)
 */
class MobileSalesApiFinisher extends AbstractFinisher
{
    protected static array $requiredFieldMarkers = [
        'anrede',
        'vorname',
        'nachname',

        'email',
        'telefon',

        'objekt_strasse',
        'objekt_hausnummer',
        'objekt_plz',
        'objekt_ort',

        'datenschutz',
        'nachricht',
    ];

    protected static array $optionalFieldMarkers = [
        'titel',
    ];

    /**
     * Detect if the data shall be sent to MSA
     */
    public function handleSubmissionFinisher(): void
    {
        if (isset($this->getSettings()['main']['msa'])
            && $this->getSettings()['main']['msa'] == 1
        ) {
            $this->submitToMsa($this->getMail());
        }
    }

    /**
     * Prepare the data and send them to MSA
     *
     * @link https://swagger.service4pb.de/
     */
    protected function submitToMsa(Mail $mail): void
    {
        $formValues = $this->extractAnswerValues($mail);

        $hasTrackingParams = false;
        foreach (ElandersApi::$trackingParams as $paramName) {
            if (isset($post['tracking_' . $paramName]) && $post['tracking_' . $paramName] !== '') {
                $hasTrackingParams = true;
            }
        }

        if (!isset($post['tracking_aktion']) || $post['tracking_aktion'] === '') {
            $sender = '';
            if (!$hasTrackingParams
                && isset($this->settings['sender']) && $this->settings['sender'] != ''
            ) {
                //only use form settings when we have no tracking parameters
                $sender = $this->settings['sender'];
            }
            $post['tracking_aktion'] = $sender;
        }

        $url = ElandersApi::getBaseUrl() . '/api/tip/v1/pbi/online/insertTip';

        $data = [
            'objectStreet'       => $formValues['objekt_strasse'],
            'objectStreetNumber' => $formValues['objekt_hausnummer'],
            'objectZipCode'      => $formValues['objekt_plz'],
            'objectCity'         => $formValues['objekt_ort'],

            'gender'   => ElandersApi::mapAnredeGender($formValues['anrede']),
            'title'    => ElandersApi::mapTitelTitle($formValues['titel']),
            'forename' => $formValues['vorname'],
            'name'     => $formValues['nachname'],
            'email'    => $formValues['email'],
            'phone'    => $formValues['telefon'],

            'dataPrivacyConfirmed' => $formValues['datenschutz'],

            'remark'    => $formValues['nachricht'],
            'tipSource' => GeneralUtility::locationHeaderUrl(
                $GLOBALS['TSFE']->cObj->getTypoLink_URL($GLOBALS['TSFE']->id)
            ),
        ];

        $additionalData = [];
        foreach (ElandersApi::$trackingParams as $paramName) {
            if (isset($post['tracking_' . $paramName])
                && $post['tracking_' . $paramName] !== ''
            ) {
                $additionalData[$paramName] = $post['tracking_' . $paramName];
            }
        }
        if (count($additionalData)) {
            //yes they get double-encoded in the end
            $data['additionalData'] = json_encode($additionalData);
        }
        //debug($data);

        ElandersApi::sendRequest($url, $data);
    }

    /**
     * Get a simple key-value array of answers from a Powermail Mail object
     */
    protected function extractAnswerValues(Mail $mail): array
    {
        $answers = $mail->getAnswersByFieldMarker();
        $values = [];
        foreach ($answers as $key => $answer) {
            $values[$key] = $answer->getValue();
        }

        //convert single checkbox array to boolean
        if (isset($values['datenschutz']) && is_array($values['datenschutz'])) {
            $values['datenschutz'] = count($values['datenschutz']) > 0;
        }

        return $values;
    }

    /**
     * Validation helper to see if the form has the required fields for
     * the MobileSales API.
     *
     * Shows a flash message in the backend.
     *
     * @see TceMainHook
     */
    public static function validateRequiredFields(int $formId)
    {
        $forms = GeneralUtility::makeInstance(FormRepository::class);
        $form = $forms->findByUid($formId);

        $markers = [];
        $required = [];
        foreach ($form->getPages() as $page) {
            foreach ($page->getFields() as $field) {
                $markers[$field->getMarker()] = true;
                if ($field->isMandatory()) {
                    $required[$field->getMarker()] = true;
                }
            }
        }

        $missingReq = [];
        $missingOpt = [];
        foreach (static::$requiredFieldMarkers as $reqMarker) {
            if (!isset($markers[$reqMarker])) {
                $missingReq[] = $reqMarker;
            } elseif (!isset($required[$reqMarker])) {
                $missingReq[] = $reqMarker;
            }
        }
        foreach (static::$optionalFieldMarkers as $optMarker) {
            if (!isset($markers[$optMarker])) {
                $missingOpt[] = $optMarker;
            }
        }

        $text = '';
        $severity = ContextualFeedbackSeverity::INFO;
        if (count($missingReq) > 0) {
            $text .= 'Es fehlen folgende Pflichtfelder, die für die Elanders MSA benötigt werden:'
                . ' ' . implode(', ', $missingReq) . '.';
            $severity = ContextualFeedbackSeverity::WARNING;
        }
        if (count($missingOpt) > 0) {
            $text .= "\n" . 'Optional mögliche Felder:'
                . ' ' . implode(', ', $missingOpt) . '.';
        }

        $text = trim($text);
        if ($text === '') {
            $severity = ContextualFeedbackSeverity::OK;
            $text = 'Alle Formularfelder für die Elanders MSA vorhanden.';
        }

        $message = GeneralUtility::makeInstance(
            FlashMessage::class,
            $text, 'MobileSales API',
            $severity,
            true
        );
        $fms = GeneralUtility::makeInstance(FlashMessageService::class);
        $fms->getMessageQueueByIdentifier()->addMessage($message);
    }
}
