<?php

namespace Mogic\Dbi\Domain\Model;

/**
 * Center/group information for a detailled WebmaklerUser
 */
class WebmaklerGroup
{
    public int $id;

    public int $parentId;

    /**
     * "41020104 - HH_VG4_M_015"
     */
    public string $name;

    /**
     * "HH_VG4_M_015"
     */
    public string $shortName;

    /**
     * "41020104"
     */
    public string $number;

    /**
     * "Berlin Südwest"
     */
    public string $additionalName;

    /**
     * "20272451"
     */
    public string $mandatorId;

    /**
     * "Klosterstraße"
     */
    public string $street;

    /**
     * "42"
     */
    public string $houseNumber;

    /**
     * "13581"
     */
    public string $zip;

    /**
     * "Berlin"
     */
    public string $town;

    /**
     * "030 ********"
     */
    public ?string $phone = null;

    /**
     * "030 ********"
     */
    public string $fax;

    /**
     * "<EMAIL>"
     */
    public ?string $email = null;

    /**
     * "www.deutsche-bank-immobilien.de/spandau-charlottenburg"
     */
    public string $url;

    /**
     * "2024-07-30T18:02:47+02:00"
     */
    public \DateTime $changeDate;

    public function getCenterSlug(): ?string
    {
        $parts = explode('/', $this->url, 2);
        if (count($parts) == 2) {
            return rtrim(explode('#', $parts[1])[0], '/');
        }
        return null;
    }
}
