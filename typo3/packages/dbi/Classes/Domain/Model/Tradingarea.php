<?php

namespace Mogic\Dbi\Domain\Model;

/**
 * An area that a realtor team member is assigned to handle.
 *
 * <AUTHOR> <<EMAIL>>
 */
class Tradingarea extends \TYPO3\CMS\Extbase\DomainObject\AbstractEntity
{
    /**
     * City name
     * (from cities table)
     *
     * @internal TCA entry required for mapping!
     */
    public string $city;

    /**
     * City district name (Stadtteil, Ortsteil)
     * (from cities table)
     *
     * @internal TCA entry required for mapping!
     */
    public string $district;

    /**
     * ZIP code
     */
    public string $postalcode;

    /**
     * Number of inhabitants
     */
    public int $population;

    /**
     * Username within FIO systems
     */
    public string $fioUsername;

    /**
     * Search title shall only contain the city name, not the district
     */
    public bool $returnCityOnly = true;

    /**
     * Return correct title for search results, with or without district
     * depending on if the city name alone matches the request
     *
     * @return string Title for the search results
     */
    public function getSearchtitle(): string
    {
        if ($this->returnCityOnly || $this->district == '') {
            return sprintf(
                "%s - %s",
                $this->postalcode,
                $this->city
            );
        }

        // return $this->city . ' - ' . $this->district;
        return sprintf(
            "%s - %s (%s)",
            $this->postalcode,
            $this->city,
            $this->district
        );
    }

    /**
     * Set $returnCityOnly to true if the city name alone matches all
     * search words
     *
     * @param string[] $searchParts Split search words
     */
    public function configureSearchTitle($searchParts): void
    {
        $this->returnCityOnly = true;
        foreach ($searchParts as $part) {
            if (stripos($this->city, $part) === false) {
                $this->returnCityOnly = false;
                break;
            }
        }
    }
}
