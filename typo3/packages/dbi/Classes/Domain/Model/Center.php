<?php

namespace Mogic\Dbi\Domain\Model;

use Mogic\Dbi\Domain\Repository\TeammemberRepository;
use Psr\Http\Message\ServerRequestInterface;
use TYPO3\CMS\Core\Database\ConnectionPool;
use TYPO3\CMS\Core\Resource\File;
use TYPO3\CMS\Core\Resource\FileReference;
use TYPO3\CMS\Core\Utility\GeneralUtility;
use TYPO3\CMS\Extbase\Configuration\ConfigurationManagerInterface;
use TYPO3\CMS\Core\Resource\Exception\ResourceDoesNotExistException;

/**
 * Real estate center (Immobiliencenter)
 *
 * <AUTHOR> <<EMAIL>>
 */
class Center extends \TYPO3\CMS\Extbase\DomainObject\AbstractEntity
{
    public int $hidden;

    public int $starttime;
    public int $endtime;

    /**
     * Region the center is active in "Bad Kreuznach/Simmern/Idar-Oberstein"
     */
    public string $name2;

    /**
     * Address: Street with number
     */
    public string $addressStreet;

    /**
     * Address: Postal code (zip code)
     */
    public string $addressPostalcode;

    /**
     * Address: City name
     */
    public string $addressCity;

    /**
     * Address: Two-letter state code
     */
    public string $addressState;

    /**
     * Telephone number
     */
    public string $telephone;

    /**
     * Fax number
     */
    public string $fax;

    /**
     * E-Mail address
     */
    public string $email;

    /**
     * Full Address
     */
    public string $fullAddress;

    /**
     * Image
     */
    public File|FileReference $image;

    /**
     * Stage Image
     */
    public File|FileReference $stageImage;

    /**
     * UID of center to redirect to
     *
     * 0 = no redirect
     */
    public int $redirect;

    /**
     * Comma-separated list of FIO Webmakler node IDs
     *
     * The "@var" is needed because the symfony type detector otherwise
     * thinks that it's an array.
     *
     * @var string
     */
    public string $fioGroupIds;

    public string $fioGroupName;

    public string $url;

    public string $contactOpeninghours;

    private array $cache = [];

    public function addFioGroupId(int $fioGroupId): void
    {
        if ($this->containsFioGroupId($fioGroupId)) {
            return;
        }
        $this->fioGroupIds = trim($this->fioGroupIds . ',' . $fioGroupId, ',');
    }

    /**
     * Check if the given FIO Webmakler node ID is in the list of $fioGroupIds
     */
    public function containsFioGroupId(int $fioGroupId): bool
    {
        $groupIds = explode(',', $this->fioGroupIds);
        return in_array($fioGroupId, $groupIds);
    }

    /**
     * Get the full address of center
     */
    public function getFullAddress(): string
    {
        $address = "";
        if (!empty($this->addressStreet)) {
            $address .= $this->addressStreet . ",\n";
        }

        if (!empty($this->addressPostalcode)) {
            $address .= $this->addressPostalcode . " ";
        }

        $address .= $this->addressCity;
        return $address;
    }

    public function getMarketPriceEstimationPid(): int|false
    {
        $ts = GeneralUtility::makeInstance(ConfigurationManagerInterface::class)->getConfiguration(
            ConfigurationManagerInterface::CONFIGURATION_TYPE_FULL_TYPOSCRIPT
        );
        $allSettings = array_filter(
            $ts['plugin.']['tx_dbi.']['settings.']
        );

        if (isset($allSettings["marketPriceEstimationPid"])) {
            return (int) $allSettings["marketPriceEstimationPid"];
        }

        return false;
    }


    /**
     * Get an image for the center
     *
     * 1. Center: search result image
     * 2. Center: Main image (center)
     * 3. Fallback image (center)
     */
    public function getImage(): File|FileReference
    {
        $fileOrRef = static::findRelationOrFallbackImage(
            $this->uid,
            ["image_search", "image_mainpage"],
            'EXT:dbi/Resources/Public/Images/fallback_makler_team.png'
        );
        if ($fileOrRef instanceof FileReference) {
            $this->ensureAltText($fileOrRef);
        }
        return $fileOrRef;
    }


    /**
     * Get an stage image for the center.
     *
     * 1. Center: Main image (center)
     * 2. Center: Page Header image (center)
     * 3. Fallback image (center)
     */
    public function getStageImage(): File|FileReference
    {
        $fileOrRef = static::findRelationOrFallbackImage(
            $this->uid,
            ["image_mainpage", "image_pageheader"],
            'EXT:dbi/Resources/Public/Images/generic-team.jpg'
        );
        if ($fileOrRef instanceof FileReference) {
            $this->ensureAltText($fileOrRef);
        }
        return $fileOrRef;
    }

    protected static function findRelationOrFallbackImage(
        $uid,
        array $relationKeys,
        string $fallback
    ): File|FileReference {
        $fileRepo = GeneralUtility::makeInstance(
            \TYPO3\CMS\Core\Resource\FileRepository::class
        );

        foreach ($relationKeys as $relationKey) {
            $files = $fileRepo->findByRelation(
                'tx_dbi_center', $relationKey, $uid
            );
            if (count($files)) {
                return $files[0];
            }
        }

        $resFact = GeneralUtility::makeInstance(
            \TYPO3\CMS\Core\Resource\ResourceFactory::class
        );
        return $resFact->retrieveFileOrFolderObject($fallback);
    }

    /**
     * Get award images for the center:
     *
     * 1. Default awards (Typoscript)
     * 2. Center awards
     *
     * @return mixed[] Array of file references and file objects:
     *                 \TYPO3\CMS\Core\Resource\File
     *                 \TYPO3\CMS\Core\Resource\FileReference
     */
    public function getAllAwards(): array
    {
        $fileOrFileRefObjects = [];

        //generic award images
        $ts = GeneralUtility::makeInstance(ConfigurationManagerInterface::class)->getConfiguration(
            ConfigurationManagerInterface::CONFIGURATION_TYPE_FULL_TYPOSCRIPT
        );
        $allCenterAwardPaths = array_filter(
            $ts['plugin.']['tx_dbi.']['settings.']['allcenterawards.']
        );

        $resFact = GeneralUtility::makeInstance(
            \TYPO3\CMS\Core\Resource\ResourceFactory::class
        );
        foreach ($allCenterAwardPaths as $path) {
            try {
                $file = $resFact->retrieveFileOrFolderObject($path);
            } catch (ResourceDoesNotExistException $e) {
                //This means "File not found", and we skip then
                continue;
            }
            $fileOrFileRefObjects[] = $file;
        }

        $fileRepo = GeneralUtility::makeInstance(
            \TYPO3\CMS\Core\Resource\FileRepository::class
        );

        $fileOrFileRefObjects = array_merge(
            $fileOrFileRefObjects,
            $fileRepo->findByRelation(
                'tx_dbi_center', 'image_awards', $this->uid
            )
        );

        return $fileOrFileRefObjects;
    }

    public function getSlug(): string
    {
        $qb = GeneralUtility::makeInstance(ConnectionPool::class)
            ->getQueryBuilderForTable('pages');
        $qb->getRestrictions()
            ->removeAll();
        $slug = $qb->select('slug')
            ->from('pages')
            ->where(
                $qb->expr()->eq('pages.uid', $qb->createNamedParameter($this->getPid())),
            )
            ->execute()
            ->fetchOne();

        return ltrim($slug, '/');
    }

    public function getUrl(): string
    {
        $request = $this->getRequest();
        $pid = $this->getPid();
        $router = $request->getAttribute('site')->getRouter();

        return (string) $router->generateUri($pid);
    }

    public function getUrlRoute(): string
    {
        return sprintf(
            'https://www.google.com/maps/dir/?api=1&destination=%s',
            urlencode(
                sprintf(
                    "%s,%s %s",
                    $this->addressStreet,
                    $this->addressPostalcode,
                    $this->addressCity
                )
            )
        );
    }

    public function getSalesManager(): ?Teammember
    {
        $salesManager = $this->cache['salesManager'] ?? null;
        if ($salesManager) {
            return $salesManager;
        }

        $teamRepo = GeneralUtility::makeInstance(TeammemberRepository::class);
        $salesManager = $teamRepo->findSalesManager($this->getPid());

        $this->cache['salesManager'] = $salesManager;
        return $salesManager;
    }

    public function getTelephone(): string
    {
        return $this->telephone;
    }

    public function getFax(): string
    {
        return $this->fax;
    }

    public function getEmail(): string
    {
        return $this->email;
    }

    protected function getRequest(): ServerRequestInterface
    {
        return $GLOBALS['TYPO3_REQUEST'];
    }

    /**
     * Make sure the team photo file has a proper alt text
     */
    protected function ensureAltText(FileReference $fileRef): void
    {
        if ($fileRef->getOriginalFile()->getProperty('alternative') != '') {
            return;
        }
        $altText = 'Teamfoto: ' . $this->name2;
        $fileRef->getOriginalFile()->updateProperties(['alternative' => $altText]);
    }

    /**
     * Sales managers from the first group only are relevant for address sync
     */
    public function isFirstGroupId(int $fioGroupId): bool
    {
        $groupIds = explode(',', $this->fioGroupIds);

        return isset($groupIds[0]) && $groupIds[0] == $fioGroupId;
    }
}
