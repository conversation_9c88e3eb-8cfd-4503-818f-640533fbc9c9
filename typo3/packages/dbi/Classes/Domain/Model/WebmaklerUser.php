<?php

namespace Mogic\Dbi\Domain\Model;

/**
 * Detailled user record from the FIO webmakler API
 */
class WebmaklerUser
{
    public int $id;

    /**
     * "hase.martin"
     */
    public string $userName;

    /**
     * "Selbstständiger Immobilienberater"
     */
    public string $identification;

    /**
     * "0777/208050.2"
     */
    public string $vgeNumber;

    /**
     * "Herr"
     */
    public string $salutation;

    /**
     * "<PERSON><PERSON>, <PERSON>"
     */
    public string $name;

    /**
     * "Dr. ", "Dr." (free text)
     */
    public string $title;

    /**
     * "Klosterstraße"
     */
    public string $street;

    /**
     * "42"
     */
    public string $houseNumber;

    /**
     * Postal code
     * "13581"
     */
    public string $zip;

    /**
     * "Berlin"
     */
    public string $town;

    /**
     * "030 12345"
     */
    public ?string $phone = null;

    /**
     * "030 12345"
     */
    public ?string $fax = null;

    /**
     * E-Mail address of the realtor
     *
     * "<EMAIL>"
     */
    public string $email;

    /**
     * E-Mail address to show on the website
     *
     * "<EMAIL>"
     */
    public string $emailPortal;

    /**
     * "0163 12345"
     */
    public ?string $mobile = null;

    /**
     * "www.deutsche-bank-immobilien.de/spandau-charlottenburg"
     */
    public string $url;

    /**
     * FIXME
     * "pufukuf"
     */
    public string $additionalNumber;

    /**
     * "GBL" - Gebietsleiter(in)
     * "VTL" - Vertriebsleiter(in)
     */
    public string $grade;

    /**
     * FIXME
     */
    public string $qualification;

    /**
     * "Vertriebsleiter"
     */
    public string $position;

    /**
     * Base64 encoded image
     *
     * @see getDecodedPicture()
     */
    public ?string $picture = null;

    public bool $showInBrokerSearch;

    /**
     * "2021-09-14T13:54:51.767+02:00"
     */
    public string $changeDate;

    public WebmaklerGroup $group;

    public function getLastname(): string
    {
        list($last, $first) = explode(',', $this->name, 2);
        return trim($last);
    }

    public function getFirstname(): string
    {
        list($last, $first) = explode(',', $this->name, 2);
        return trim($first);
    }

    public function getDecodedPicture(): string
    {
        return base64_decode($this->picture);
    }

    public function getTitleKey(): string
    {
        $titleMap = [
            'dr'  => 'dr',
            'dr.' => 'dr',

            'prof'  => 'prof',
            'prof.' => 'prof',

            'prof dr'  => 'profdr',
            'prof. dr.' => 'profdr',
        ];

        return $titleMap[strtolower($this->title)] ?? '';
    }
}
