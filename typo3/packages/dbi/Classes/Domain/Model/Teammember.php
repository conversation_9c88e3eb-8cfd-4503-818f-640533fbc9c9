<?php

namespace Mogic\Dbi\Domain\Model;

use Mogic\Dbi\Domain\Repository\CenterRepository;
use TYPO3\CMS\Core\Resource\File;
use TYPO3\CMS\Core\Resource\FileReference;
use TYPO3\CMS\Core\Utility\GeneralUtility;
use TYPO3\CMS\Extbase\Configuration\ConfigurationManagerInterface;
use TYPO3\CMS\Core\Resource\Exception\ResourceDoesNotExistException;

/**
 * Member of a real estate center.
 *
 * <AUTHOR> <<EMAIL>>
 */
class Teammember extends \TYPO3\CMS\Extbase\DomainObject\AbstractEntity
{
    /**
     * The center this member belongs to
     */
    public ?Center $center = null;

    /**
     * Nachname
     */
    public string $nameLastname;

    /**
     * Vorname
     */
    public string $nameFirstname;

    /**
     * Anrede ("frau"/"herr")
     */
    public string $nameSalutation;

    /**
     * Academic title ("Prof.", "Dr.", "Prof. Dr.")
     */
    public string $nameDegree;

    /**
     * Username within FIO systems
     */
    public string $fioUsername;

    /**
     * ID of FIO webmakler user record
     */
    public int $fioUserId;

    /**
     * ID of FIO webmakler node ID
     */
    public int $fioGroupId;

    /**
     * "Vertriebsleiter"
     */
    public string $function;

    /**
     * Job Titel
     * "Selbstständige Immobilienberaterin"
     */
    public string $identification;

    /**
     * Telephone number
     */
    public string $contactTelephone;

    /**
     * Mobile phone number
     */
    public string $contactMobile;

    /**
     * FAX number
     */
    public string $contactFax;

    /**
     * Personal e-mail address
     */
    public string $contactEmail;

    /**
     * Comma-separated list of iso-653 two-letter language codes
     */
    public string $languages;

    /**
     * Personal message
     */
    public string $text;

    /**
     * Number of profile images
     */
    public int $image = 0;

    /**
     * Makler-Empfehlungs-ID
     */
    public ?string $awardsMaklerempfehlungId = null;

    /**
     * Do not show link to Maklerempfehlung
     */
    public bool $awardsMaklerempfehlungHide = false;

    /**
     * Do not show capital image
     */
    public bool $awardsCapitalHide = false;

    /**
     * Number of "Capital" award images
     */
    public int $awardsCapitalImage = 0;

    /**
     * Number of "award" images
     */
    public int $imageAwards = 0;

    /**
     * Icon Sources from used languages
     *
     */
    public array $languageSvgSources;

    /**
     * Image or Fallback Image
     */
    public File|FileReference $avatar;

    public \DateTime $tstamp;

    public bool $deleted;

    public bool $hidden;

    /**
     * Get award images for the member:
     *
     * 1. Default awards (Typoscript)
     * 2. Center awards
     * 3. Member awards
     *
     * @return mixed[] Array of file references and file objects:
     *                 \TYPO3\CMS\Core\Resource\File
     *                 \TYPO3\CMS\Core\Resource\FileReference
     */
    public function getAllAwards(): array
    {
        $fileOrFileRefObjects = [];

        //generic award images
        $ts = GeneralUtility::makeInstance(ConfigurationManagerInterface::class)->getConfiguration(
            ConfigurationManagerInterface::CONFIGURATION_TYPE_FULL_TYPOSCRIPT
        );
        $allCenterAwardPaths = array_filter(
            $ts['plugin.']['tx_dbi.']['settings.']['allcenterawards.']
        );

        $resFact = GeneralUtility::makeInstance(
            \TYPO3\CMS\Core\Resource\ResourceFactory::class
        );
        foreach ($allCenterAwardPaths as $path) {
            try {
                $file = $resFact->retrieveFileOrFolderObject($path);
            } catch (ResourceDoesNotExistException $e) {
                //This means "File not found", and we skip then
                continue;
            }
            $fileOrFileRefObjects[] = $file;
        }

        $fileRepo = GeneralUtility::makeInstance(
            \TYPO3\CMS\Core\Resource\FileRepository::class
        );

        //center award images
        $center = $this->getCenter();
        if ($center) {
            $fileOrFileRefObjects = array_merge(
                $fileOrFileRefObjects,
                $fileRepo->findByRelation(
                    'tx_dbi_center', 'image_awards', $center->uid
                )
            );
        }

        //team member award images
        $fileOrFileRefObjects = array_merge(
            $fileOrFileRefObjects,
            $this->getAwards()
        );

        return $fileOrFileRefObjects;
    }

    /**
     * Get direct award images + "Capital" award image for the member.
     *
     * No center awards or central awards used.
     *
     * @return \TYPO3\CMS\Core\Resource\FileReference[] Array of file references
     */
    public function getAwards(): array
    {
        $fileRepo = GeneralUtility::makeInstance(
            \TYPO3\CMS\Core\Resource\FileRepository::class
        );

        $fileOrFileRefObjects = [];

        if ($this->awardsCapitalImage > 0 && !$this->awardsCapitalHide) {
            $fileOrFileRefObjects = array_merge(
                $fileOrFileRefObjects,
                $fileRepo->findByRelation(
                    'tx_dbi_teammember', 'awards_capital_image', $this->uid
                )
            );
        }

        if ($this->imageAwards > 0) {
            $fileOrFileRefObjects = array_merge(
                $fileOrFileRefObjects,
                $fileRepo->findByRelation(
                    'tx_dbi_teammember', 'image_awards', $this->uid
                )
            );
        }

        return $fileOrFileRefObjects;
    }

    /**
     * Find the center for this team member
     *
     * This is not ideal. See https://stackoverflow.com/q/69158857/282601
     */
    public function getCenter(): ?Center
    {
        if ($this->center !== null) {
            return $this->center;
        }

        $centerRepo = GeneralUtility::makeInstance(CenterRepository::class);
        $this->center = $centerRepo->findByPid($this->pid);
        return $this->center;
    }

    /**
     * Combined degree + firstname + lastname
     */
    public function getFullName(): string
    {
        $degree = '';
        if ($this->nameDegree != '') {
            $degree = \TYPO3\CMS\Extbase\Utility\LocalizationUtility::translate(
                $this->nameDegree, 'dbi'
            );
        }
        return trim(
            $degree
            . ' ' . $this->nameFirstname . ' ' . $this->nameLastname
        );
    }

    /**
     * Get the first available telephone number:
     * 1. Telephone
     * 2. Mobile phone
     * 3. Center phone
     */
    public function getFirstTelephone(): string
    {
        if ($this->contactTelephone != '') {
            return $this->contactTelephone;
        }
        if ($this->contactMobile != '') {
            return $this->contactMobile;
        }

        return $this->getCenter()->telephone;
    }

    /**
     * Get the first available telephone number, but not the mobile number.
     * 1. Telephone
     * 2. Center phone
     */
    public function getFirstTelephoneNoMobile(): string
    {
        if ($this->contactTelephone != '') {
            return $this->contactTelephone;
        }

        return $this->getCenter()->telephone;
    }

    /**
     * Get an image for the team member.
     *
     * 1. Team member image
     * 2. Center: search result image
     * 3. Center: Main image (contact)
     * 4. Fallback image (center)
     */
    public function getImageWithFallback(): File|FileReference
    {
        $fallback = 'EXT:dbi/Resources/Public/Images/fallback_makler_team.png';

        $fileRepo = GeneralUtility::makeInstance(
            \TYPO3\CMS\Core\Resource\FileRepository::class
        );

        $files = $fileRepo->findByRelation(
            'tx_dbi_teammember', 'image', $this->uid
        );
        if (count($files)) {
            return $files[0];
        }

        $center = $this->getCenter();
        if ($center) {
            $files = $fileRepo->findByRelation(
                'tx_dbi_center', 'image_search', $center->uid
            );
            if (count($files)) {
                return $files[0];
            }

            $files = $fileRepo->findByRelation(
                'tx_dbi_center', 'image_mainpage', $center->uid
            );
            if (count($files)) {
                return $files[0];
            }
        }

        $resFact = GeneralUtility::makeInstance(
            \TYPO3\CMS\Core\Resource\ResourceFactory::class
        );
        return $resFact->retrieveFileOrFolderObject($fallback);
    }

    /**
     * Get an image for the team member.
     *
     * 1. Team member image
     * 4. Fallback image (center)
     */
    public function getAvatar(): File|FileReference
    {
        $fallback = 'EXT:dbi/Resources/Public/Images/fallback_makler_team.png';

        $fileRepo = GeneralUtility::makeInstance(
            \TYPO3\CMS\Core\Resource\FileRepository::class
        );

        $files = $fileRepo->findByRelation(
            'tx_dbi_teammember', 'image', $this->uid
        );
        if (count($files)) {
            /** @var $file \TYPO3\CMS\Core\Resource\FileReference */
            $file = $files[0];
            $this->ensureAltText($file);
            return $file;
        }

        $resFact = GeneralUtility::makeInstance(
            \TYPO3\CMS\Core\Resource\ResourceFactory::class
        );
        return $resFact->retrieveFileOrFolderObject($fallback);
    }

    /**
     * Get the team member image (without fallback).
     */
    public function getImageNoFallback(): ?FileReference
    {
        $fileRepo = GeneralUtility::makeInstance(
            \TYPO3\CMS\Core\Resource\FileRepository::class
        );

        $files = $fileRepo->findByRelation(
            'tx_dbi_teammember', 'image', $this->uid
        );
        if (count($files)) {
            return $files[0];
        }

        return null;
    }

    /**
     * Get flag icons sources for languages.
     */
    public function getLanguageSvgSources(): array
    {
        $format = 'EXT:dbi/Resources/Public/Icons/Language/%s.svg';

        $resFact = GeneralUtility::makeInstance(
            \TYPO3\CMS\Core\Resource\ResourceFactory::class
        );

        $languages = explode(",", $this->languages);
        return array_map(
            function ($languageKey) use ($format, $resFact) {
                try {
                    $filePath = sprintf($format, $languageKey);

                    //check if the file exists and throw exception
                    $resFact->retrieveFileOrFolderObject($filePath);

                    return (object) [
                        "key" => $languageKey,
                        "path" => $filePath,
                        'title' => \TYPO3\CMS\Extbase\Utility\LocalizationUtility::translate(
                            $languageKey, 'dbi'
                        ),
                    ];
                } catch (\InvalidArgumentException $e) {
                    return (object) [
                        "key" => $languageKey,
                        "path" => sprintf($format, "fallback"),
                        'title' => $languageKey,
                    ];
                }
            },
            $languages
        );
    }

    /**
     * Get contact infos
     */
    public function getContactInfos(): array
    {
        return array_filter(
            array_map(
                function ($variableSuffix) {
                    $variable = "contact" . ucfirst($variableSuffix);

                    if (!!$this->{$variable} && !empty($this->{$variable})) {
                        return (object) [
                            "type" => $variableSuffix,
                            "info" => $this->{$variable}
                        ];
                    }
                    return null;
                },
                [
                    "telephone",
                    "mobile",
                    "fax",
                    "email"
                ]
            )
        );
    }

    /**
     * Check if the team member is a "Vertriebsleiter"
     *
     * @see TeammemberRepository::findSalesManager()
     */
    public function isSalesManager(): bool
    {
        return $this->function == 'Vertriebsleiter'
            || $this->function == 'Vertriebsleiterin';
    }

    /**
     * To help sorting by function + name
     *
     * Directors are first, sales managers ("Vertriebsleiter") directly after.
     */
    public function getSortKey(): string
    {
        $functionMap = [
            'Direktorin Immobilienvertrieb' => '00',
            'Direktor Immobilienvertrieb'   => '01',
            'Vertriebsleiterin'             => '02',
            'Vertriebsleiter'               => '03',
            'Senior Immobilienberaterin'    => '04',
            'Senior Immobilienberater'      => '05',
        ];
        $key = $functionMap[$this->function] ?? '99';
        $key .= '-' . strtolower($this->nameLastname . '-' . $this->nameFirstname);
        return $key;
    }

    /**
     * Make sure the member's portrait file has a proper alt text
     */
    protected function ensureAltText(FileReference $fileRef): void
    {
        if ($fileRef->getOriginalFile()->getProperty('alternative') != '') {
            return;
        }
        $altText = 'Portraitfoto: '
            . $this->getFullName()
            . ', ' . $this->function
            . ', Deutsche Bank Immobilien GmbH';
        $altText = str_replace(', ,', ',', $altText);
        $fileRef->getOriginalFile()->updateProperties(['alternative' => $altText]);
    }
}
