<?php

namespace Mogic\Dbi\Domain\Repository;

use Mogic\Dbi\Domain\Model\Teammember;
use TYPO3\CMS\Extbase\Persistence\Generic\Typo3QuerySettings;
use TYPO3\CMS\Extbase\Persistence\QueryInterface;
use TYPO3\CMS\Core\Utility\GeneralUtility;

/**
 * Database table access for team members
 *
 * Database table name is configured in Configuration/Extbase/Persistence/Classes.php
 *
 * <AUTHOR> <<EMAIL>>
 */
class TeammemberRepository extends \TYPO3\CMS\Extbase\Persistence\Repository
{
    public function initializeObject()
    {
        /** @var QuerySettingsInterface $querySettings */
        $querySettings = GeneralUtility::makeInstance(Typo3QuerySettings::class);
        $querySettings->setRespectStoragePage(false);
        $this->setDefaultQuerySettings($querySettings);
    }

    /**
     * Let the repository return hidden team members
     * Useful for the webmakler API sync.
     */
    public function setIncludeHidden()
    {
        /** @var QuerySettingsInterface $querySettings */
        $querySettings = GeneralUtility::makeInstance(Typo3QuerySettings::class);
        $querySettings->setRespectStoragePage(false);
        $querySettings->setIgnoreEnableFields(true);

        $this->setDefaultQuerySettings($querySettings);
    }

    /**
     * Get the team member assigned to the given trading area
     *
     * Since we do not have team member IDs in trading areas,
     * extbase does not join the tables automatically,
     * and we have to rely on the statement().
     *
     * @param string $postalCode 5-digit german zip code
     *
     * @return Teammember|null
     */
    public function findByPostalCode(string $postalCode)
    {
        $query = $this->createQuery();
        $sql = <<<SQL
SELECT tm.*
FROM tx_dbi_tradingareas AS ta,
  tx_dbi_teammember AS tm,
  pages
WHERE
  ta.fio_username = tm.fio_username
  AND pages.uid = tm.pid
  AND tm.deleted = 0
  AND tm.hidden = 0
  AND (tm.starttime = 0 OR tm.starttime <= UNIX_TIMESTAMP())
  AND (tm.endtime = 0 OR tm.endtime >= UNIX_TIMESTAMP())
  AND pages.deleted = 0
  AND pages.hidden = 0
  AND (pages.starttime = 0 OR pages.starttime <= UNIX_TIMESTAMP())
  AND (pages.endtime = 0 OR pages.endtime >= UNIX_TIMESTAMP())
  AND ta.postalcode = ?
LIMIT 1
SQL;
        $query = $query->statement(
            $sql,
            [$postalCode]
        );

        return $query->execute();
    }

    /**
     * Find multiple members by their name.
     *
     * The query can be firstname and lastname, or the beginning of
     * firstname and lastname.
     *
     * We return exact matches first, "Alex" before "Alexander".
     *
     * @param array $parts Pre-split search query parts:
     *                     ['name'] or ['firstname', 'lastname']
     * @param int   $limit Maximum number of members to return
     *
     * @return Teammember[]
     */
    public function findByName(array $parts, int $limit = 10)
    {
        $whereParams = [];
        $orderParams = [];
        $whereParts = [];
        $orderParts = [];

        foreach ($parts as $part) {
            // $whereParts[] = 'name_firstname LIKE ? OR name_lastname LIKE ?'
            // $whereParams[] = $part . '%';
            // $whereParams[] = $part . '%';
            // $whereParams[] = '% ' . $part . '%';//"de vogt"

            // DBI-319: make firstname and lastname better searchable
            // to find not the correct input part, some part must be contain
            $whereParts[] = 'name_firstname LIKE ? OR name_lastname LIKE ?';
            $whereParams[] = '%' . $part . '%';
            $whereParams[] = '%' . $part . '%';

            $orderParts[] = '(name_firstname = ?)';
            $orderParts[] = '(name_lastname = ?)';
            $orderParams[] = $part;
            $orderParams[] = $part;
        }

        $whereSql = '(' . implode(') AND (', $whereParts) . ')';
        $orderSql = '(' . implode(' + ', $orderParts) . ') DESC';

        $query = $this->createQuery();
        $sql = <<<SQL
SELECT DISTINCT tm.*
FROM
  tx_dbi_teammember AS tm,
  pages
WHERE
  pages.uid = tm.pid
  AND tm.deleted = 0
  AND tm.hidden = 0
  AND (tm.starttime = 0 OR tm.starttime <= UNIX_TIMESTAMP())
  AND (tm.endtime = 0 OR tm.endtime >= UNIX_TIMESTAMP())
  AND pages.deleted = 0
  AND pages.hidden = 0
  AND (pages.starttime = 0 OR pages.starttime <= UNIX_TIMESTAMP())
  AND (pages.endtime = 0 OR pages.endtime >= UNIX_TIMESTAMP())
  AND {$whereSql}
ORDER BY {$orderSql}
LIMIT {$limit}
SQL;

        $query = $query->statement(
            $sql,
            array_merge($whereParams, $orderParams)
        );

        return $query->execute()->toArray();
    }

    /**
     * Get random team members that have an assigned trading area.
     *
     * @param int $limit Number of records to return
     *
     * @return Teammember[]
     */
    public function findRandom(int $limit)
    {
        $query = $this->createQuery();
        $query = $query->statement(
            <<<SQL
SELECT *
FROM (
  SELECT tm.*
  FROM tx_dbi_tradingareas AS ta,
    tx_dbi_teammember AS tm,
    pages
  WHERE
    ta.fio_username = tm.fio_username
    AND pages.uid = tm.pid
    AND tm.deleted = 0
    AND tm.hidden = 0
    AND (tm.starttime = 0 OR tm.starttime <= UNIX_TIMESTAMP())
    AND (tm.endtime = 0 OR tm.endtime >= UNIX_TIMESTAMP())
    AND pages.deleted = 0
    AND pages.hidden = 0
    AND (pages.starttime = 0 OR pages.starttime <= UNIX_TIMESTAMP())
    AND (pages.endtime = 0 OR pages.endtime >= UNIX_TIMESTAMP())
) as tm2
GROUP BY tm2.uid
ORDER BY RAND()
LIMIT {$limit}
SQL
        );

        return $query->execute()->toArray();
    }

    /**
     * Find the "Vertriebsleiter" for a center
     *
     * @see Teammember::isSalesManager()
     */
    public function findSalesManager(int $pid): ?Teammember
    {
        $query = $this->createQuery();
        return $query
            ->matching(
                $query->logicalAnd(
                    $query->equals('pid', $pid),
                    $query->logicalOr(
                        $query->equals('function', 'Vertriebsleiter'),
                        $query->equals('function', 'Vertriebsleiterin'),
                    )
                )
            )
            ->setLimit(1)
            ->execute()
            ->getFirst();
    }

    /**
     * Get all team members for a center pid, sorted by their sorting parameter
     *
     * Also returns hidden/disabled ones.
     */
    public function findByPid(int $pid): array
    {
        /* @var $query \TYPO3\CMS\Extbase\Persistence\Generic\Query */
        $query = $this->createQuery();
        return $query
            ->matching(
                $query->logicalAnd(
                    $query->equals('pid', $pid),
                )
            )
            ->setOrderings(
                ['sorting' => QueryInterface::ORDER_ASCENDING]
            )
            ->execute()
            ->toArray();
    }

    /**
     * Also returns disabled members.
     */
    public function findOneByFioUserId(int $fioUserId): ?Teammember
    {
        return $this->findOneBy(['fio_user_id' => $fioUserId]);
    }

    public function findOneByFioUserName(string $fioUserName): ?Teammember
    {
        return $this->findOneBy(['fio_username' => $fioUserName]);
    }

    /**
     * Find non-hidden and non-deleted team members that are not part
     * of the list of given UIDs.
     */
    public function findMissing(array $uids): array
    {
        /* @var $query \TYPO3\CMS\Extbase\Persistence\Generic\Query */
        $query = $this->createQuery();
        $query->getQuerySettings()->setIgnoreEnableFields(false);
        $query->getQuerySettings()->setIncludeDeleted(false);

        return $query
            ->matching($query->logicalNot($query->in('uid', $uids)))
            ->execute()
            ->toArray();
    }
}
