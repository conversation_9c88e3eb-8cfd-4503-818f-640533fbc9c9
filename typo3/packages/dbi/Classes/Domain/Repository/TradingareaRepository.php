<?php

namespace Mogic\Dbi\Domain\Repository;

/**
 * Database table access for trading areas
 *
 * Database table name is configured in Configuration/Extbase/Persistence/Classes.php
 *
 * <AUTHOR> <<EMAIL>>
 */
class TradingareaRepository extends \TYPO3\CMS\Extbase\Persistence\Repository
{
    /**
     * Get the trading areas that begin with the given digits
     *
     * Since we do not have team member IDs in trading areas,
     * extbase does not join the tables automatically,
     * and we have to rely on the statement().
     *
     * @param string $postalCode Up to 5 digits of a german zip code
     * @param int    $limit      Maximum number of trading areas to return
     *
     * @return Tradingarea[]
     */
    public function findByPostalCode(string $postalCode, int $limit = 10)
    {
        $query = $this->createQuery();
        $sql = <<<SQL
SELECT DISTINCT tc.uid, ta.postalcode, ta.fio_username,
  tc.city, tc.district, ta.population
FROM
  pages,
  tx_dbi_teammember AS tm,
  tx_dbi_tradingareas AS ta,
  tx_dbi_cities AS tc
WHERE
      pages.uid = tm.pid
  AND tm.fio_username = ta.fio_username
  AND ta.postalcode = tc.postalcode

  AND pages.deleted = 0 AND pages.hidden = 0
  AND (pages.starttime = 0 OR pages.starttime <= UNIX_TIMESTAMP())
  AND (pages.endtime = 0 OR pages.endtime >= UNIX_TIMESTAMP())
  AND tm.deleted = 0 AND tm.hidden = 0
  AND (tm.starttime = 0 OR tm.starttime <= UNIX_TIMESTAMP())
  AND (tm.endtime = 0 OR tm.endtime >= UNIX_TIMESTAMP())
  AND ta.deleted = 0 AND ta.hidden = 0
  AND tc.deleted = 0 AND tc.hidden = 0

  AND tc.postalcode LIKE ?
GROUP BY ta.postalcode
ORDER BY population DESC, tc.postalcode ASC
LIMIT ?
SQL;
        $query = $query->statement(
            $sql,
            [$postalCode . '%', $limit]
        );

        return $query->execute()->toArray();
    }

    /**
     * Find multiple trading areas by their city name.
     *
     * - Search "city" and "postalcode" fields.
     * - Exact city name matches are first
     * - City names that begin with the search term are second
     * - In-word matches are last
     *
     * @param array $parts Pre-split search query parts:
     *                     ['city'] or ['halle', 'saale', '04']
     * @param int   $limit Maximum number of areas to return
     *
     * @return Tradingarea[]
     */
    public function findByName($parts, int $limit = 10)
    {
        if (count($parts) === 0) {
            return [];
        }

        $matchParts = [];
        $whereParams = [];
        $orderParamsExact = [];
        $orderParamsStarts = [];
        $whereParts = [];
        $orderPartsExact = [];
        $orderPartsStarts = [];

        $atLeastOne = false;
        foreach ($parts as $part) {
            if (ctype_digit($part)) {
                $whereParts[] = 'tc.postalcode LIKE ?';
                $whereParams[] = $part . '%';

                $orderPartsExact[] = '(tc.postalcode = ?)';
                $orderParamsExact[] = $part;
                $atLeastOne = true;
            } else {
                $cleanPart = str_replace(['@', '*', '"', "'", '(', ')', '<', '>', '+'], '', $part);
                $cleanPart = trim($cleanPart, '-+');

                $orderCleanPart = preg_replace('[^a-zA-ZäöüÄÖÜ0-9+-]', '', $part);
                $orderCleanPart = trim($orderCleanPart, '+-');

                if ($cleanPart != '') {
                    $matchParts[] = '+' . $cleanPart . '*';

                    $orderPartsExact[] = '(city = ?)';
                    $orderParamsExact[] = $orderCleanPart;

                    $orderPartsStarts[] = '(city LIKE ?)';
                    $orderParamsStarts[] = $orderCleanPart . '%';
                    $atLeastOne = true;
                }
            }
        }

        if (!$atLeastOne) {
            return [];
        }

        if (count($matchParts)) {
            $whereParts[]  = 'MATCH(city, district) AGAINST (? IN BOOLEAN MODE)';
            $whereParams[] = implode(' ', $matchParts);
        }

        $whereSql = '(' . implode(') AND (', $whereParts) . ')';
        $orderExactSql = '(' . implode(' + ', $orderPartsExact) . ') DESC';

        if (count($orderPartsStarts) > 0) {
            $orderStartsSql = '(' . implode(' + ', $orderPartsStarts) . ') DESC';
        } else {
            $orderStartsSql = '1';
        }

        $query = $this->createQuery();
        $sql = <<<SQL
SELECT DISTINCT tc.uid, ta.postalcode, ta.fio_username,
  tc.city, tc.district, ta.population
FROM
  pages,
  tx_dbi_teammember AS tm,
  tx_dbi_tradingareas AS ta,
  tx_dbi_cities AS tc
WHERE
      pages.uid = tm.pid
  AND tm.fio_username = ta.fio_username
  AND ta.postalcode = tc.postalcode

  AND pages.deleted = 0 AND pages.hidden = 0
  AND (pages.starttime = 0 OR pages.starttime <= UNIX_TIMESTAMP())
  AND (pages.endtime = 0 OR pages.endtime >= UNIX_TIMESTAMP())
  AND tm.deleted = 0 AND tm.hidden = 0
  AND (tm.starttime = 0 OR tm.starttime <= UNIX_TIMESTAMP())
  AND (tm.endtime = 0 OR tm.endtime >= UNIX_TIMESTAMP())
  AND ta.deleted = 0 AND ta.hidden = 0
  AND tc.deleted = 0 AND tc.hidden = 0

  AND {$whereSql}
GROUP BY ta.postalcode
ORDER BY {$orderExactSql}, {$orderStartsSql}, population DESC, city ASC
LIMIT ?
SQL;

        $query = $query->statement(
            $sql,
            array_merge(
                $whereParams,
                $orderParamsExact, $orderParamsStarts,
                [$limit]
            )
        );

        return $query->execute()->toArray();
    }
}
