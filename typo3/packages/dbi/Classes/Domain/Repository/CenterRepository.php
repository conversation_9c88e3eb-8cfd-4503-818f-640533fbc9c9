<?php

namespace Mogic\Dbi\Domain\Repository;

use Mogic\Dbi\Domain\Model\Center;
use TYPO3\CMS\Core\Database\ConnectionPool;
use TYPO3\CMS\Core\Utility\GeneralUtility;
use TYPO3\CMS\Extbase\Persistence\Generic\Typo3QuerySettings;

/**
 * Database table access for centers
 *
 * Database table name is configured in Configuration/Extbase/Persistence/Classes.php
 *
 * <AUTHOR> <<EMAIL>>
 */
class CenterRepository extends \TYPO3\CMS\Extbase\Persistence\Repository
{
    protected int $centerFolderUid = 17;

    public function initializeObject()
    {
        /** @var QuerySettingsInterface $querySettings */
        $querySettings = GeneralUtility::makeInstance(Typo3QuerySettings::class);
        $querySettings->setRespectStoragePage(false);
        $this->setDefaultQuerySettings($querySettings);
    }

    /**
     * Get a center by its page ID.
     *
     * @param int $pid Page ID
     */
    public function findByPid(int $pid): ?Center
    {
        $query = $this->createQuery();
        $query->matching($query->equals('pid', $pid));
        return $query->execute()->getFirst();
    }

    /**
     * Find the nearest center in the rootline
     *
     * @param int[] $pids Array of page UIDs, deepest page UID first
     */
    public function findByRootline(array $pids): ?Center
    {
        $query = $this->createQuery();
        $query->getQuerySettings()->setIgnoreEnableFields(true);

        $pageIdStringList = implode(',', $pids);

        $sql = <<<SQL
            SELECT *
            FROM tx_dbi_center
            WHERE (pid IN ($pageIdStringList))
            AND (deleted = 0)
            ORDER BY FIELD (pid, $pageIdStringList)
        SQL;

        $query->statement($sql);

        return $query->execute()->getFirst();

    }

    /**
     * Get a center by its FIO node ID
     *
     * @param int $pid FIO node ID (group ID)
     */
    public function findByFioGroupId(int $fioGroupId): ?Center
    {
        $query = $this->createQuery();
        $query->matching(
            $query->logicalOr(
                $query->equals('fio_group_ids', $fioGroupId),
                $query->like('fio_group_ids', '%,' . $fioGroupId . ',%'),
                $query->like('fio_group_ids', $fioGroupId . ',%'),
                $query->like('fio_group_ids', '%,' . $fioGroupId)
            )
        );
        return $query->execute()->getFirst();
    }

    /**
     * Load all the centers given by their UID, in the order as
     * defined in the $uids array.
     */
    public function findByIds(array $uids): array
    {
        $query = $this->createQuery();

        $uidConstraints = [];
        foreach ($uids as $uid) {
            $uidConstraints[] = $query->equals('uid', $uid);
        }

        $results = $query
            ->matching(
                $query->logicalOr(...$uidConstraints)
            )
            ->execute()
            ->toArray();

        $keyedResults = [];
        foreach ($results as $center) {
            $keyedResults[$center->getUid()] = $center;
        }

        $retval = [];
        foreach ($uids as $uid) {
            if (isset($keyedResults[$uid])) {
                $retval[$uid] = $keyedResults[$uid];
            }
        }
        return $retval;
    }

    /**
     * Find a center page ID by its slug
     */
    public function findBySlug(string $slug): ?Center
    {
        $fullSlug = '/' . ltrim($slug, '/');

        $query = $this->createQuery();
        $sql = <<<SQL
            SELECT tx_dbi_center.*
            FROM tx_dbi_center
            JOIN pages ON pages.uid = tx_dbi_center.pid
            WHERE
              pages.slug = ?
              AND pages.pid = ?
            LIMIT 1
            SQL;

        $query = $query->statement($sql, [$fullSlug, $this->centerFolderUid]);
        return $query->execute()->getFirst();
    }

    /**
     * Find a center page ID by its slug
     */
    public function findPidBySlug(string $slug): ?int
    {
        $fullSlug = '/' . ltrim($slug, '/');

        $qb = GeneralUtility::makeInstance(ConnectionPool::class)
            ->getQueryBuilderForTable('pages');
        $pid = $qb->select('uid')
            ->from('pages')
            ->where(
                $qb->expr()->eq('slug', $qb->createNamedParameter($fullSlug)),
                $qb->expr()->eq('pid', $qb->createNamedParameter($this->centerFolderUid)),
            )
            ->execute()
            ->fetchOne();

        return $pid !== false ? $pid : null;
    }

    /**
     * Get all center page UIDs
     */
    public function findAllPids(): array
    {
        $qb = GeneralUtility::makeInstance(ConnectionPool::class)
            ->getQueryBuilderForTable('pages');

        return $qb->select('uid')
            ->from('pages')
            ->where(
                $qb->expr()->eq('pid', $qb->createNamedParameter($this->centerFolderUid)),
            )
            ->execute()
            ->fetchFirstColumn();
    }

    /**
     * Find non-hidden and non-deleted centers that are not part
     * of the list of given UIDs and that are not redirects.
     */
    public function findMissing(array $uids): array
    {
        /* @var $query \TYPO3\CMS\Extbase\Persistence\Generic\Query */
        $query = $this->createQuery();
        $query->getQuerySettings()->setIgnoreEnableFields(false);
        $query->getQuerySettings()->setIncludeDeleted(false);

        return $query
            ->matching(
                $query->logicalAnd(
                    $query->logicalNot($query->in('uid', $uids)),
                    $query->equals('redirect', 0)
                )
            )
            ->execute()
            ->toArray();
    }
}
