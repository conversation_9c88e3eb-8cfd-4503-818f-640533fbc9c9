<?php

namespace Mogic\Dbi\Domain\Repository;

use TYPO3\CMS\Core\Configuration\ExtensionConfiguration;
use TYPO3\CMS\Core\Core\Environment;
use TYPO3\CMS\Core\Http\RequestFactory;

/**
 * Cached access to Group data in the FIO webmakler API
 */
class WebGroupRepositoryCached extends WebGroupRepository
{
    protected string $cacheDir;

    /**
     * How old a cache file may be.
     */
    protected ?int $maxAgeSeconds = null;

    public function __construct(
        RequestFactory $requestFactory,
        ExtensionConfiguration $extensionConfiguration
    ) {
        parent::__construct($requestFactory, $extensionConfiguration);

        $this->cacheDir = Environment::getVarPath() . '/webmakler/';
        if (!is_dir($this->cacheDir)) {
            mkdir($this->cacheDir, 0770);
        }
    }

    protected function sendRequestUndecoded(
        string $relPath, $method = 'GET', array $json = null
    ): string {
        $url = $this->webmaklerConf['base_url'] . $relPath;

        $cachePath = $this->cacheDir . urlencode($url);
        if ($json !== null) {
            $cachePath .= '-' . md5(json_encode($json));
        }

        if (file_exists($cachePath)) {
            $age = time() - filemtime($cachePath);
            if ($this->maxAgeSeconds === null
                || $this->maxAgeSeconds >= $age
            ) {
                return file_get_contents($cachePath);
            }
        }

        $content = parent::sendRequestUndecoded($relPath, $method, $json);
        file_put_contents($cachePath, $content);

        return $content;
    }

    public function setMaxAgeSeconds(int $maxAgeSeconds)
    {
        $this->maxAgeSeconds = $maxAgeSeconds;
    }
}
