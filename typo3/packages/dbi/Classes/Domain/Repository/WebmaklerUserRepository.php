<?php

namespace Mogic\Dbi\Domain\Repository;

use Mogic\Dbi\Domain\Model\WebmaklerListUser;
use Mogic\Dbi\Domain\Model\WebmaklerUser;
use kamermans\OAuth2\GrantType\PasswordCredentials;
use kamermans\OAuth2\OAuth2Middleware;
use TYPO3\CMS\Core\Configuration\ExtensionConfiguration;
use TYPO3\CMS\Core\Core\Environment;
use TYPO3\CMS\Core\Http\RequestFactory;

/**
 * Access team member user data in the FIO webmakler API
 *
 * Login data are available in keeper
 * - stage: https://keepersecurity.eu/vault/#detail/4jp8fu-j24AY1N1er01Nww
 * - prod: https://keepersecurity.eu/vault/#detail/cfFKdi3cYYC3HE07Xqq1LA
 *
 * @link https://api-portal-staging.fio.de/
 * @link https://api-portal.fio.de/
 */
class WebmaklerUserRepository
{
    protected static array $httpClientOptions = [
        'headers' => [
            'User-Agent' => 'TYPO3',
        ],
    ];

    protected array $webmaklerConf;

    protected \JsonMapper $mapper;

    public function __construct(
        protected readonly RequestFactory $requestFactory,
        ExtensionConfiguration $extensionConfiguration
    ) {
        $this->webmaklerConf = $extensionConfiguration->get('dbi')['fio']['webmakler'];

        $context = (string) Environment::getContext();

        $this->mapper = new \JsonMapper();
        $this->mapper->bExceptionOnUndefinedProperty = !($context === 'Production');

        $this->setupOAuth2Middleware();
    }

    public function getApiBaseUrl(): string
    {
        return $this->webmaklerConf['base_url'];
    }

    /**
     * Get all users for a given center
     *
     * @return WebmaklerListUser[]
     */
    public function findUsersByGroupId(int $fioGroupId): array
    {
        $data = $this->sendRequest(
            'users/search', 'POST',
            [
                'MaxNumberOfResults' => 100,
                'StartWithRowNumber' => 0,
                'SearchCriteria' => [
                    'GroupId' => $fioGroupId,
                    'ShowInBrokerSearch' => true,
                ]
            ]
        );

        return $this->mapper->mapArray($data->resultList, [], WebmaklerListUser::class);
    }

    /**
     * Iterate over all list users that shall be shown on the website
     *
     * @return WebmaklerListUser[]
     */
    public function findAllVisible(): iterable
    {
        $start = 0;
        $pageSize = 50;
        do {
            $data = $this->sendRequest(
                'users/search', 'POST',
                [
                    'MaxNumberOfResults' => $pageSize,
                    'StartWithRowNumber' => $start,
                    'SearchCriteria' => [
                        'ShowInBrokerSearch' => true,
                    ]
                ]
            );

            foreach ($data->resultList as $row) {
                yield $this->mapper->map($row, WebmaklerListUser::class);
            }

            $start += $pageSize;
            $hasMore = $start < $data->totalCount;
        } while ($hasMore);
    }

    /**
     * Iterate over all users that had changes since the given timestamp
     * Also returns realtors that shall not be shown on the website
     *
     * @return WebmaklerListUser[]
     */
    public function findAllChanged(int $since): iterable
    {
        $start = 0;
        $pageSize = 50;
        do {
            $data = $this->sendRequest(
                'users/search', 'POST',
                [
                    'MaxNumberOfResults' => $pageSize,
                    'StartWithRowNumber' => $start,
                    'SearchCriteria' => [
                        'ChangeDate' => date('c', $since),
                    ]
                ]
            );

            foreach ($data->resultList as $row) {
                yield $this->mapper->map($row, WebmaklerListUser::class);
            }

            $start += $pageSize;
            $hasMore = $start < $data->totalCount;
        } while ($hasMore);
    }

    /**
     * Get detailed user information
     */
    public function findOneById(int $fioUserId): ?WebmaklerUser
    {
        $data = $this->sendRequest('users/' . $fioUserId, 'GET');
        if ($data === null) {
            return null;
        }
        return $this->mapper->map($data, WebmaklerUser::class);
    }

    protected function sendRequest(string $relPath, $method = 'GET', array $json = null): mixed
    {
        return json_decode((string) $this->sendRequestUndecoded($relPath, $method, $json));
    }

    protected function sendRequestUndecoded(
        string $relPath, $method = 'GET', array $json = null
    ): string {
        $requestOptions = array_merge_recursive(
            static::$httpClientOptions,
            [
                'http_errors' => true,
                'timeout' => 30,
                'auth' => 'oauth',//activate oauth2 middleware
                'headers' => [
                    'Accept' => 'application/json',
                ],
                'json' => $json,
            ]
        );


        $url = $this->webmaklerConf['base_url'] . $relPath;
        $fioRes = $this->requestFactory->request(
            $url, $method, $requestOptions
        );

        return (string) $fioRes->getBody();
    }

    /**
     * Configures everything so that the TYPO3 HTTP request factory is able
     * to send requests with oauth2 bearer tokens
     */
    protected function setupOAuth2Middleware(): void
    {
        $tokenClientOptions = array_merge_recursive(
            static::$httpClientOptions,
            ['base_uri' => $this->webmaklerConf['token_url']]
        );

        $pwGrant = new PasswordCredentials(
            new \GuzzleHttp\Client($tokenClientOptions),
            [
                'client_id'     => $this->webmaklerConf['client_id'],
                'client_secret' => $this->webmaklerConf['client_secret'],
                'username'      => $this->webmaklerConf['username'],
                'password'      => $this->webmaklerConf['password'],
            ]
        );
        $oauth2Middleware = new OAuth2Middleware($pwGrant);
        $oauth2Middleware->setTokenPersistence(new \Mogic\Dbi\Helper\StaticOAuth2Cache());

        $GLOBALS['TYPO3_CONF_VARS']['HTTP']['handler']['webmakler-oauth2'] = $oauth2Middleware;
    }

}
