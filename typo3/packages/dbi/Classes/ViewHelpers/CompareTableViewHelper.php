<?php

namespace Mogic\Dbi\ViewHelpers;

use TYPO3Fluid\Fluid\Core\Rendering\RenderingContextInterface;
use TYPO3Fluid\Fluid\Core\ViewHelper\AbstractViewHelper;
use TYPO3\CMS\Core\Utility\CsvUtility;

/**
 * Convert a CSV string into an array. Separator is "|".
 *
 * <AUTHOR> <<EMAIL>>
 */
class CompareTableViewHelper extends AbstractViewHelper
{
    /**
     * Initialize arguments
     *
     * @return void
     */
    public function initializeArguments()
    {
        $this->registerArgument(
            'table', 'string', 'table which is to be converted', true
        );
    }

    /**
     * Convert a string into a multidimensional array
     *
     * @param array                     $arguments
     * @param \Closure                  $renderChildrenClosure
     * @param RenderingContextInterface $renderingContext
     *
     * @return array
     */
    public static function renderStatic(
        array $arguments,
        \Closure $renderChildrenClosure,
        RenderingContextInterface $renderingContext
    ) {
        $fieldDelimiter = '|';
        $table = CsvUtility::csvToArray($arguments['table'], $fieldDelimiter);

        return array_map(
            function ($row, $cols) {
                return array_map(
                    function ($col, $cell) use ($row) {
                        if ($row == 0 && $col > 0 && preg_match("/^[A-Z][0-9]{2}-[0-9]{3}-[0-9]{3}$/", $cell)) {
                            return (object) [
                                "type" => "expose",
                                "value" => $cell
                            ];
                        }

                        return (object) [
                            "type" => "default",
                            "value" => $cell
                        ];
                    },
                    array_keys($cols),
                    $cols
                );
            },
            array_keys($table),
            $table
        );
    }
}
