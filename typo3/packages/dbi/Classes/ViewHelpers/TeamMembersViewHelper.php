<?php

namespace Mogic\Dbi\ViewHelpers;

use Mogic\Dbi\Domain\Repository\TeammemberRepository;
use Mogic\Dbi\Domain\Model\Teammember;
use TYPO3\CMS\Core\Utility\GeneralUtility;
use TYPO3\CMS\Extbase\Persistence\Generic\Mapper\DataMapper;
use TYPO3Fluid\Fluid\Core\Rendering\RenderingContextInterface;
use TYPO3Fluid\Fluid\Core\ViewHelper\AbstractViewHelper;

/**
 * Fetch real estate center data from fluid.
 *
 * Example:
 *
 *   {dbi:teamMembers() -> v:variable.set(name: 'members')}
 *   {member.name_firstname}
 *
 * <AUTHOR> <<EMAIL>>
 */
class TeamMembersViewHelper extends AbstractViewHelper
{
    /**
     * Declare view helper arguments
     */
    public function initializeArguments(): void
    {
        parent::initializeArguments();
        $this->registerArgument(
            'recordRows', 'array',
            'Center or team member records',
            false,
            []
        );
    }

    /**
     * Fetches the team members from the next center in the rootline
     *
     * @return array Array of team member records
     */
    public static function renderStatic(
        array $arguments,
        \Closure $renderChildrenClosure,
        RenderingContextInterface $renderingContext
    ): ?array {

        $request = $renderingContext->getRequest();
        list($centerRows, $memberRows) = static::splitRecordRows($arguments['recordRows']);

        if (count($memberRows) > 0) {
            return static::getMembersFromRecords($memberRows);
        }

        if (count($centerRows)) {
            $centerData = $centerRows[0];
            if ($centerData === null) {
                return [];
            }
            $centerPid = $centerData['pid'];

        } else {
            $center = CenterViewHelper::getModel(null, $request);
            if ($center === null) {
                return [];
            }
            $centerPid = $center->getPid();
        }
        return static::getMembers($centerPid);
    }

    /**
     * Load team members
     */
    protected static function getMembers(int $pid): array
    {
        $teamRepo = GeneralUtility::makeInstance(TeammemberRepository::class);
        return $teamRepo->findByPid($pid);
    }

    /**
     * Load team member objects from records loaded by mask
     */
    protected static function getMembersFromRecords(array $memberRows): array
    {
        $dataMapper = GeneralUtility::makeInstance(DataMapper::class);
        return $dataMapper->map(Teammember::class, $memberRows);
    }

    /**
     * Group the given records by type: center rows and member rows
     */
    protected static function splitRecordRows(array $recordRows): array
    {
        $centerRows = [];
        $memberRows = [];
        foreach ($recordRows as $row) {
            if (isset($row['name2'])) {
                $centerRows[] = $row;
            } else {
                $memberRows[] = $row;
            }
        }

        return [$centerRows, $memberRows];
    }
}
