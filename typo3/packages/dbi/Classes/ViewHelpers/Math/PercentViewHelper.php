<?php

namespace Mogic\Dbi\ViewHelpers\Math;

use TYPO3Fluid\Fluid\Core\ViewHelper\AbstractViewHelper;

class PercentViewHelper extends AbstractViewHelper
{
    /**
     * Initializes the arguments.
     */
    public function initializeArguments()
    {
        $this->registerArgument('value', 'float', 'The Float Number to generate percentage', true);
        $this->registerArgument('of', 'int', 'The max integer number', false, 100);
    }

    /**
     * @inheritDoc
     */
    public function render()
    {
        $value = $this->arguments['value'];
        $of = $this->arguments['of'];
        return $this->getPercentage($value, $of);
    }

    /**
     * Make a percentage of a float number of other number
     * 5 of 5 is 100
     * 2.5 of 5 is 50
     * 0 of 0 is 0
     * @param float $value The number or score
     * @param int   $of The max number
     * @return int The percentage
     */
    public function getPercentage(
        float $value,
        int $of
    ): int {
        if ($value >= $of) {
            return 100;
        }
        return ($value / $of) * 100;
    }
}
