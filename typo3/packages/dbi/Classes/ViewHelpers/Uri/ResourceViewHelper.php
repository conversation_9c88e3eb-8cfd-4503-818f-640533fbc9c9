<?php

namespace Mogic\Dbi\ViewHelpers\Uri;

use TYPO3\CMS\Core\Resource\Exception\InvalidFileException;
use TYPO3\CMS\Core\Utility\GeneralUtility;
use TYPO3\CMS\Core\Utility\PathUtility;
use TYPO3\CMS\Extbase\Mvc\RequestInterface;
use TYPO3\CMS\Fluid\Core\Rendering\RenderingContext;
use TYPO3Fluid\Fluid\Core\Rendering\RenderingContextInterface;
use TYPO3Fluid\Fluid\Core\ViewHelper\AbstractViewHelper;
use TYPO3Fluid\Fluid\Core\ViewHelper\Traits\CompileWithRenderStatic;

/**
 * Copy of TYPO3\CMS\Fluid\ViewHelpers\Uri\ResourceViewHelper with cache busting support.
 *
 * @see https://forge.typo3.org/issues/103617
 * Feature #103617: Cache busting parameter for f:uri.resource via createVersionNumberedFilename()
 */
class ResourceViewHelper extends AbstractViewHelper
{
    use CompileWithRenderStatic;

    public function initializeArguments(): void
    {
        $this->registerArgument('path', 'string', 'The path and filename of the resource (relative to Public resource directory of the extension).', true);
        $this->registerArgument('extensionName', 'string', 'Target extension name. If not set, the current extension name will be used');
        $this->registerArgument('absolute', 'bool', 'If set, an absolute URI is rendered', false, false);

        $this->registerArgument('versionNumber', 'bool', 'Add a version number to the URI', false, false);
    }

    /**
     * Render the URI to the resource. The filename is used from child content.
     *
     * @return string The URI to the resource
     * @throws InvalidFileException
     * @throws \RuntimeException
     */
    public static function renderStatic(array $arguments, \Closure $renderChildrenClosure, RenderingContextInterface $renderingContext): string
    {
        $uri = PathUtility::getPublicResourceWebPath(self::resolveExtensionPath($arguments, $renderingContext));
        if ($arguments['versionNumber']) {
            $uri = GeneralUtility::createVersionNumberedFilename($uri);
        }
        if ($arguments['absolute']) {
            $uri = GeneralUtility::locationHeaderUrl($uri);
        }
        return $uri;
    }

    /**
     * Resolves the extension path, either directly when possible, or from extension name and request
     */
    private static function resolveExtensionPath(array $arguments, RenderingContextInterface $renderingContext): string
    {
        $path = $arguments['path'];
        if (PathUtility::isExtensionPath($path)) {
            return $path;
        }
        return sprintf(
            'EXT:%s/Resources/Public/%s',
            self::resolveExtensionKey($arguments, $renderingContext),
            ltrim($path, '/')
        );
    }

    /**
     * Resolves extension key either from given extension name argument or from request
     */
    private static function resolveExtensionKey(array $arguments, RenderingContextInterface $renderingContext): string
    {
        $extensionName = $arguments['extensionName'];
        if ($extensionName === null) {
            return self::resolveValidatedRequest($arguments, $renderingContext)->getControllerExtensionKey();
        }
        return GeneralUtility::camelCaseToLowerCaseUnderscored($extensionName);
    }

    /**
     * Resolves and validates the request from rendering context
     */
    private static function resolveValidatedRequest(array $arguments, RenderingContextInterface $renderingContext): RequestInterface
    {
        if (!$renderingContext instanceof RenderingContext) {
            throw new \RuntimeException(
                sprintf(
                    'RenderingContext must be instance of "%s", but is instance of "%s"',
                    RenderingContext::class,
                    get_class($renderingContext)
                ),
                1640095993
            );
        }
        $request = $renderingContext->getRequest();
        if (!$request instanceof RequestInterface) {
            throw new \RuntimeException(
                sprintf(
                    'ViewHelper f:uri.resource needs an Extbase Request object to resolve extension name for given path "%s".'
                    . ' If not in Extbase context, either set argument "extensionName",'
                    . ' or (better) use the standard EXT: syntax for path attribute like \'path="EXT:indexed_search/Resources/Public/Icons/Extension.svg"\'.',
                    $arguments['path']
                ),
                1639672666
            );
        }
        if ($request->getControllerExtensionKey() === '') {
            throw new \RuntimeException(
                sprintf(
                    'Can not resolve extension key for given path "%s".'
                    . ' If not in Extbase context, either set argument "extensionName",'
                    . ' or (better) use the standard EXT: syntax for path attribute like \'path="EXT:indexed_search/Resources/Public/Icons/Extension.svg"\'.',
                    $arguments['path']
                ),
                1640097205
            );
        }
        return $request;
    }
}
