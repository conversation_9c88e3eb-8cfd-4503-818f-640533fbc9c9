<?php

namespace Mogic\Dbi\ViewHelpers\Hash;

use TYPO3Fluid\Fluid\Core\ViewHelper\AbstractViewHelper;

class Md5ViewHelper extends AbstractViewHelper
{
    /**
     * Initializes the arguments.
     */
    public function initializeArguments()
    {
        $this->registerArgument('string', 'string', 'String to hash', true);
    }

    /**
     * @inheritDoc
     */
    public function render()
    {
        return md5($this->arguments['string']);
    }
}
