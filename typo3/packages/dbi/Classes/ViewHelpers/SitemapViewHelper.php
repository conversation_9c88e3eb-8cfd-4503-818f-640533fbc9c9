<?php

namespace Mogic\Dbi\ViewHelpers;

use TYPO3Fluid\Fluid\Core\ViewHelper\AbstractViewHelper;
use TYPO3\CMS\Core\Utility\GeneralUtility;
use TYPO3\CMS\Frontend\ContentObject\ContentObjectRenderer;
use Psr\Http\Message\ServerRequestInterface;
use Mogic\Dbi\XmlSitemapCenterProvider;

class SitemapViewHelper extends AbstractViewHelper
{
    protected $escapeOutput = false;

    public function initializeArguments()
    {
        $this->registerArgument('as', 'string', 'Variable name to assign the result to', false, 'sitemapItems');
    }

    public function render()
    {
        $request = $GLOBALS['TYPO3_REQUEST'];
        $cObj = GeneralUtility::makeInstance(ContentObjectRenderer::class);

        $provider = GeneralUtility::makeInstance(XmlSitemapCenterProvider::class, $request, 'centers', [], $cObj);
        $items = $provider->getItems();


        foreach ($items as &$item) {
            $item['url'] = $item['loc'];
        }

        $as = $this->arguments['as'];
        $this->templateVariableContainer->add($as, $items);
        $output = $this->renderChildren();
        $this->templateVariableContainer->remove($as);

        return $output;
    }
}
