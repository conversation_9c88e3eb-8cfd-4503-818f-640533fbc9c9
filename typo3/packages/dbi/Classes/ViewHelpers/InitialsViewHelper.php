<?php

namespace Mogic\Dbi\ViewHelpers;

use TYPO3Fluid\Fluid\Core\ViewHelper\AbstractViewHelper;

class InitialsViewHelper extends AbstractViewHelper
{
    /**
     * Initializes the arguments.
     */
    public function initializeArguments()
    {
        $this->registerArgument('value', 'string', 'The Text', true);
    }

    /**
     * @inheritDoc
     */
    public function render()
    {
        $value = $this->arguments['value'];
        return $this->getDefaultInitials($value);
    }

    /**
     * Make a abbriviated name like '<PERSON>' to 'JP'
     * to use as Avatar text
     * using  new version <initials:initials value="personName" method="getDefaultInitials" />
     * old version does not work {initials:initials(value: personName, method: 'getDefaultInitials')}
     *
     * @param string $value The full name
     * @return string Avatar Text
     */
    public function getDefaultInitials(
        string $value
    ): string {
        $splittedWords = explode(" ", $value);

        $initials = "";
        while ($splittedWord = array_shift($splittedWords)) {
            $initials .= substr($splittedWord, 0, 1);
        }

        return substr($initials, 0, 2);
    }
}
