<?php

namespace Mogic\Dbi\ViewHelpers;

use Mogic\Dbi\Domain\Model\Center;
use Mogic\Dbi\Domain\Repository\CenterRepository;
use TYPO3\CMS\Backend\Utility\BackendUtility;
use TYPO3\CMS\Core\Database\ConnectionPool;
use TYPO3\CMS\Core\Database\Query\Restriction\EndTimeRestriction;
use TYPO3\CMS\Core\Database\Query\Restriction\HiddenRestriction;
use TYPO3\CMS\Core\Database\Query\Restriction\StartTimeRestriction;
use TYPO3\CMS\Core\Http\ApplicationType;
use TYPO3\CMS\Core\Http\ImmediateResponseException;
use TYPO3\CMS\Core\Http\PropagateResponseException;
use TYPO3\CMS\Core\Http\ResponseFactory;
use TYPO3\CMS\Core\Utility\GeneralUtility;
use TYPO3\CMS\Frontend\Controller\ErrorController;
use TYPO3Fluid\Fluid\Core\Rendering\RenderingContextInterface;
use TYPO3Fluid\Fluid\Core\ViewHelper\AbstractViewHelper;

/**
 * Fetch a real estate center record from fluid.
 *
 * Example:
 *
 *   {dbi:center() -> v:variable.set(name: 'center')}
 *   {center.telephone}
 */
class CenterViewHelper extends AbstractViewHelper
{
    /**
     * Cache for center data rows
     *
     * @var array Key is the PID, value is the row
     */
    protected static $cache = [];

    /**
     * Declare view helper arguments
     */
    public function initializeArguments(): void
    {
        parent::initializeArguments();
        $this->registerArgument(
            'pid', 'int',
            'Page ID of center. Autodetection if not given.',
            false
        );
    }

    /**
     * Fetches the nearest center in the rootline.
     */
    public static function renderStatic(
        array $arguments,
        \Closure $renderChildrenClosure,
        RenderingContextInterface $renderingContext
    ): ?Center {

        $request = $renderingContext->getRequest();

        $center = static::getModel($arguments['pid'], $request);
        static::handleRedirectAndHidden($center, $request);

        return $center;
    }

    /**
     * Fetches the nearest center in the rootline and returns it as domain model
     * same as getData but returns the model instead of sql result array.
     *
     * @param int $pid Page ID of center. Autodetection if not given.
     */
    public static function getModel(?int $pid, $request): ?Center
    {
        if (array_key_exists($pid, static::$cache)) {
            return static::$cache[$pid];
        }

        $pageIds = [];
        if ($pid === null) {
            $pageIds = static::loadRootLineUids($request);
        } else {
            $pageIds[] = intval($pid);
        }

        $centerRepo = GeneralUtility::makeInstance(CenterRepository::class);
        $center = $centerRepo->findByRootline($pageIds);

        if ($center === false) {
            $center = null;
            static::$cache[$pid] = $center;
            return $center;
        }

        static::$cache[$pid] = $center;
        return $center;
    }

    /**
     * Redirect to the center configured in the 'redirect' column,
     * and takes care of hidden/starttime/endtime.
     *
     * Only works in frontend.
     * If the target center is not enabled, redirect to homepage.
     */
    protected static function handleRedirectAndHidden(?Center $center, $request): void
    {
        if (!ApplicationType::fromRequest($request)->isFrontend()) {
            return;
        }

        if ($center === null) {
            return;
        }

        $disabled = false;
        $now = time();
        if ($center->hidden == 1) {
            $disabled = true;
        } elseif ($center->starttime > 0 && $center->starttime > $now) {
            $disabled = true;
        } elseif ($center->endtime > 0 && $center->endtime < $now) {
            $disabled = true;
        }

        if ($disabled) {
            $ec = GeneralUtility::makeInstance(ErrorController::class);
            $response = $ec->pageNotFoundAction(
                $GLOBALS['TYPO3_REQUEST'], 'Center deaktiviert'
            );
            throw new ImmediateResponseException($response, 1704787252);
        }

        if ($center->redirect == 0) {
            return;
        }

        $redirCenterUid = intval($center->redirect);
        $redirCenterPid = static::getCenterPid($redirCenterUid);

        $redirectCenter = static::getModel($redirCenterPid, $request);
        if ($redirectCenter === null) {
            //disabled or not found
            static::redirectTo('/');
            return;
        }

        static::redirectTo($redirectCenter->getUrl());
    }

    /**
     * Get the page ID for a given center UID
     */
    protected static function getCenterPid(int $centerUid): ?int
    {
        $queryBuilder = GeneralUtility::makeInstance(ConnectionPool::class)
            ->getQueryBuilderForTable('tx_dbi_center');
        $queryBuilder->getRestrictions()
            ->removeByType(HiddenRestriction::class)
            ->removeByType(StartTimeRestriction::class)
            ->removeByType(EndTimeRestriction::class);

        $centerPid = $queryBuilder->select('pid')
            ->from('tx_dbi_center')
            ->where($queryBuilder->expr()->eq('uid', $centerUid))
            ->executeQuery()
            ->fetchOne();
        return $centerPid ? intval($centerPid) : null;
    }

    /**
     * Load and return the root line - depending on backend or frontend context.
     *
     * We inject mountpoint-overridden original pages into the rootline:
     * page   tree
     * ====== ====================
     * 1      Root
     * 2      ├── Basic Mount Point    <- mount point, mounting page 3
     * 3      └── Company              <- mounted by page 2
     * 4          └── About us
     *
     * Normal rootline for #2 is #3,#1, completely leaving out #2.
     * We inject #2 into it: #2,#3,#1.
     *
     * For subpages, the mounted page gets injected as well:
     * URL path /basic-mount-point/about-us
     * will give rootline #4,#2,#3,#1
     *
     * @return array Page uids of the rootline, deepest first, root last
     */
    protected static function loadRootLineUids($request): array
    {
        if (ApplicationType::fromRequest($request)->isFrontend()) {
            $rootLine = $request->getAttribute('frontend.controller')->rootLine;

        } else {
            $rootLine = BackendUtility::BEgetRootLine(
                $request->getQueryParams()['id']
            );
        }

        $rootLineUids = [];
        foreach ($rootLine as $row) {
            if (isset($row['_MOUNT_OL']) && $row['_MOUNT_OL']) {
                $rootLineUids[] = $row['_MOUNT_PAGE']['uid'];
            }
            $rootLineUids[] = $row['uid'];
        }

        return $rootLineUids;
    }

    protected static function redirectTo(string $url): void
    {
        $response = GeneralUtility::makeInstance(ResponseFactory::class)
            ->createResponse(302)
            ->withAddedHeader('location', $url);
        throw new PropagateResponseException($response);
    }
}
