<?php

namespace Mogic\Dbi\ViewHelpers;

use TYPO3Fluid\Fluid\Core\Rendering\RenderingContextInterface;
use TYPO3Fluid\Fluid\Core\ViewHelper\AbstractViewHelper;

/**
 * Creates a tel: URL from a phone number string
 *
 * Example:
 *
 *   {wsb:telUrl(number: '+49 234 313')}
 *
 * <AUTHOR> <<EMAIL>>
 */
class TelUrlViewHelper extends AbstractViewHelper
{
    /**
     * Declare view helper arguments
     */
    public function initializeArguments(): void
    {
        parent::initializeArguments();
        $this->registerArgument(
            'number', 'string',
            'Telephone number',
            true
        );
        $this->registerArgument(
            'countryCode', 'int',
            'Default country code',
            false,
            49
        );
    }

    /**
     * Create a tel: URL
     *
     * @return string Full RFC 3966-compatible telephone number with tel: prefix
     */
    public static function renderStatic(
        array $arguments, \Closure $renderChildrenClosure,
        RenderingContextInterface $renderingContext
    ): string {
        return static::getTelUrl(
            $arguments['number'], $arguments['countryCode'], '341'
        );
    }

    /**
     * Create a tel: URL
     *
     * @param string  $number      Telephone number
     * @param int     $countryCode Default country code
     * @param int     $areaCode    Default area code
     *
     * @return string Full RFC 3966-compatible telephone number with tel: prefix
     */
    public static function getTelUrl(
        $number, $countryCode = '49', $areaCode = '341'
    ): string {
        $number = preg_replace('#[^+0-9]#', '', $number);

        if (substr($number, 0, 1) == '+') {
            //full telephone number
            $tel = $number;
        } elseif (substr($number, 0, 2) == '00') {
            //full number with country code, but 00 instead of +
            $tel = '+' . substr($number, 2);
        } elseif (substr($number, 0, 1) == '0') {
            //full number without country code
            $tel = '+' . $countryCode . substr($number, 1);
        } else {
            //partial number, no country or area code
            $tel = '+' . $countryCode . $areaCode . $number;
        }

        return 'tel:' . $tel;
    }
}
