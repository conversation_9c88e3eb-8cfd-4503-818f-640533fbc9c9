<?php

namespace Mogic\Dbi\ViewHelpers;

use TYPO3Fluid\Fluid\Core\Rendering\RenderingContextInterface;
use TYPO3Fluid\Fluid\Core\ViewHelper\AbstractViewHelper;
use TYPO3\CMS\Core\Utility\CsvUtility;

/**
 * Convert a CSV string into an array. Separator is "|".
 *
 * <AUTHOR> <<EMAIL>>
 */
class TableViewHelper extends AbstractViewHelper
{
    /**
     * Initialize arguments
     *
     * @return void
     */
    public function initializeArguments()
    {
        $this->registerArgument(
            'table', 'string', 'table which is to be converted', true
        );
    }

    /**
     * Convert a string into a multidimensional array
     *
     * @param array                     $arguments
     * @param \Closure                  $renderChildrenClosure
     * @param RenderingContextInterface $renderingContext
     *
     * @return array
     */
    public static function renderStatic(
        array $arguments,
        \Closure $renderChildrenClosure,
        RenderingContextInterface $renderingContext
    ) {
        $fieldDelimiter = '|';
        return CsvUtility::csvToArray($arguments['table'], $fieldDelimiter);
    }
}
