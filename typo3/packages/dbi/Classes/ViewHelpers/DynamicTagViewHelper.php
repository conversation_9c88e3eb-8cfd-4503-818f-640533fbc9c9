<?php

namespace Mogic\Dbi\ViewHelpers;

use TYPO3Fluid\Fluid\Core\Rendering\RenderingContextInterface;
use TYPO3Fluid\Fluid\Core\ViewHelper\AbstractViewHelper;
use TYPO3Fluid\Fluid\Core\ViewHelper\Traits\CompileWithRenderStatic;

class DynamicTagViewHelper extends AbstractViewHelper
{
    use CompileWithRenderStatic;

    protected $escapeOutput = false;

    public function initializeArguments(): void
    {
        parent::initializeArguments();

        $this->registerArgument('class', 'string', 'the class attribute');
        $this->registerArgument('as', 'string', 'the tag');
    }

    public static function renderStatic(
        array $arguments,
        \Closure $renderChildrenClosure,
        RenderingContextInterface $renderingContext
    ): string {
        $tagName = 'div';
        if (isset($arguments['as'])) {
            $tagName = $arguments['as'];
        }

        $classes = "";
        if (isset($arguments['class'])) {
            $classes = $arguments['class'];
        }

        return sprintf(
            '<%s class="%s">%s</%s>',
            $tagName,
            $classes,
            $renderChildrenClosure(),
            $tagName
        );
    }
}
