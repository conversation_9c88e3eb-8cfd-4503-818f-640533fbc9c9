<?php

namespace Mogic\Dbi\ViewHelpers;

use Mogic\Dbi\Domain\Repository\TeammemberRepository;
use TYPO3\CMS\Backend\Utility\BackendUtility;
use TYPO3\CMS\Core\Database\ConnectionPool;
use TYPO3\CMS\Core\Database\Query\Restriction\EndTimeRestriction;
use TYPO3\CMS\Core\Database\Query\Restriction\HiddenRestriction;
use TYPO3\CMS\Core\Database\Query\Restriction\StartTimeRestriction;
use TYPO3\CMS\Core\Http\ApplicationType;
use TYPO3\CMS\Core\Http\ImmediateResponseException;
use TYPO3\CMS\Core\Http\PropagateResponseException;
use TYPO3\CMS\Core\Http\ResponseFactory;
use TYPO3\CMS\Core\Resource\Exception\ResourceDoesNotExistException;
use TYPO3\CMS\Core\Utility\GeneralUtility;
use TYPO3\CMS\Core\Utility\HttpUtility;
use TYPO3\CMS\Extbase\Configuration\ConfigurationManagerInterface;
use TYPO3\CMS\Frontend\Controller\ErrorController;
use TYPO3Fluid\Fluid\Core\Rendering\RenderingContextInterface;
use TYPO3Fluid\Fluid\Core\ViewHelper\AbstractViewHelper;

/**
 * Fetch real estate center data from fluid.
 *
 * Example:
 *
 *   {dbi:centerData() -> v:variable.set(name: 'centerData')}
 *   {centerData.telephone}
 *
 * <AUTHOR> Weiske <<EMAIL>>
 */
class CenterDataViewHelper extends AbstractViewHelper
{
    /**
     * Cache for center data rows
     *
     * @var array Key is the PID, value is the row
     */
    protected static $cache = [];

    /**
     * Declare view helper arguments
     */
    public function initializeArguments(): void
    {
        parent::initializeArguments();
        $this->registerArgument(
            'pid', 'int',
            'Page ID of center. Autodetection if not given.',
            false
        );
    }

    /**
     * Fetches the nearest center in the rootline.
     *
     * @return array Database row with center information, NULL if nothing found
     */
    public static function renderStatic(
        array $arguments,
        \Closure $renderChildrenClosure,
        RenderingContextInterface $renderingContext
    ): ?array {

        $request = $renderingContext->getRequest();

        $centerData = static::getData($arguments['pid'], $request);
        static::handleRedirectAndHidden($centerData, $request);

        return $centerData;
    }

    /**
     * Fetches the nearest center in the rootline and returns it.
     *
     * @param int $pid Page ID of center. Autodetection if not given.
     *
     * @return array Database row with center information, NULL if not found
     */
    public static function getData(?int $pid, $request): ?array
    {
        if (array_key_exists($pid, static::$cache)) {
            return static::$cache[$pid];
        }

        $pageIds = [];
        if ($pid === null) {
            $pageIds = static::loadRootLineUids($request);
        } else {
            $pageIds[] = intval($pid);
        }

        $queryBuilder = GeneralUtility::makeInstance(ConnectionPool::class)
            ->getQueryBuilderForTable('tx_dbi_center');
        $queryBuilder->getRestrictions()
            ->removeByType(HiddenRestriction::class)
            ->removeByType(StartTimeRestriction::class)
            ->removeByType(EndTimeRestriction::class);

        $row = $queryBuilder->select(
            'uid', 'pid', 'name2',
            'address_street', 'address_postalcode', 'address_city', 'address_state',
            'telephone', 'fax', 'email', 'contact_openinghours',
            'loc_lat', 'loc_long',
            'image_pageheader', 'image_mainpage', 'image_search',
            'search_radius', 'is_headquarter', 'redirect',
            'hidden', 'starttime', 'endtime'
        )
            ->from('tx_dbi_center')
            ->where($queryBuilder->expr()->in('pid', $pageIds))
            //$pageIds begins with the leaf first, and the root last.
            // so we can use its order to get the leaf-nearest row
            // MySQL-specific code.
            ->add('orderBy', 'FIELD (pid, ' . implode(',', $pageIds) . ')')
            ->executeQuery()
            ->fetchAssociative();

        if ($row === false) {
            $row = null;
            static::$cache[$pid] = $row;
            return $row;
        }

        $url = (string) $request->getAttribute('site')->getRouter()->generateUri($row['pid']);
        $pathParts = array_filter(explode('/', parse_url($url, PHP_URL_PATH)));
        $row['url']      = $url;
        $row['url_slug'] = end($pathParts);

        $row['url_route'] = 'https://www.google.com/maps/dir/?api=1'
            . '&destination=' . urlencode(
                $row['address_street']
                    . ',' . $row['address_postalcode']
                    . ' ' . $row['address_city']
            );

        $row['static_image_awards'] = static::getStaticAwardImages();

        $teamRepo = GeneralUtility::makeInstance(TeammemberRepository::class);
        $salesManager = $teamRepo->findSalesManager($row['pid']);
        if ($salesManager) {
            $row['telephone'] = $salesManager->contactTelephone;
            $row['fax']       = $salesManager->contactFax;
            $row['email']     = $salesManager->contactEmail;
        }

        static::$cache[$pid] = $row;
        return $row;
    }

    /**
     * get the static award images from the fileadmin/storage
     * via the typoscript constants
     */
    protected static function getStaticAwardImages(): array
    {
        $staticAwardImages = [];
        $ts = GeneralUtility::makeInstance(ConfigurationManagerInterface::class)->getConfiguration(
            ConfigurationManagerInterface::CONFIGURATION_TYPE_FULL_TYPOSCRIPT
        );
        $staticAwardImagePaths = array_filter(
            $ts['plugin.']['tx_dbi.']['settings.']['allcenterawards.']
        );

        $resFact = GeneralUtility::makeInstance(
            \TYPO3\CMS\Core\Resource\ResourceFactory::class
        );
        foreach ($staticAwardImagePaths as $path) {
            try {
                $file = $resFact->retrieveFileOrFolderObject($path);
            } catch (ResourceDoesNotExistException $e) {
                //This means "File not found", and we skip then
                continue;
            }
            $staticAwardImages[] = $file;
        }

        return $staticAwardImages;
    }

    /**
     * Redirect to the center configured in the 'redirect' column,
     * and takes care of hidden/starttime/endtime.
     *
     * Only works in frontend.
     * If the target center is not enabled, redirect to homepage.
     */
    protected static function handleRedirectAndHidden(?array $centerData, $request): void
    {
        if (!ApplicationType::fromRequest($request)->isFrontend()) {
            return;
        }
        if ($centerData === null) {
            return;
        }

        $disabled = false;
        $now = time();
        if ($centerData['hidden'] == 1) {
            $disabled = true;
        } elseif ($centerData['starttime'] > 0 && $centerData['starttime'] > $now) {
            $disabled = true;
        } elseif ($centerData['endtime'] > 0 && $centerData['endtime'] < $now) {
            $disabled = true;
        }

        if ($disabled) {
            $ec = GeneralUtility::makeInstance(ErrorController::class);
            $response = $ec->pageNotFoundAction(
                $GLOBALS['TYPO3_REQUEST'], 'Center deaktiviert'
            );
            throw new ImmediateResponseException($response, 1704787252);
        }

        if ($centerData['redirect'] == 0) {
            return;
        }

        $redirCenterUid = intval($centerData['redirect']);
        $redirCenterPid = static::getCenterPid($redirCenterUid);

        $redirCenter = static::getData($redirCenterPid, $request);
        if ($redirCenter === null) {
            //disabled or not found
            static::redirectTo('/');
            return;
        }

        static::redirectTo($redirCenter['url']);
    }

    /**
     * Get the page ID for a given center UID
     */
    protected static function getCenterPid(int $centerUid): ?int
    {
        $queryBuilder = GeneralUtility::makeInstance(ConnectionPool::class)
            ->getQueryBuilderForTable('tx_dbi_center');
        $queryBuilder->getRestrictions()
            ->removeByType(HiddenRestriction::class)
            ->removeByType(StartTimeRestriction::class)
            ->removeByType(EndTimeRestriction::class);

        $centerPid = $queryBuilder->select('pid')
            ->from('tx_dbi_center')
            ->where($queryBuilder->expr()->eq('uid', $centerUid))
            ->executeQuery()
            ->fetchOne();
        return $centerPid ? intval($centerPid) : null;
    }

    /**
     * Load and return the root line - depending on backend or frontend context.
     *
     * We inject mountpoint-overridden original pages into the rootline:
     * page   tree
     * ====== ====================
     * 1      Root
     * 2      ├── Basic Mount Point    <- mount point, mounting page 3
     * 3      └── Company              <- mounted by page 2
     * 4          └── About us
     *
     * Normal rootline for #2 is #3,#1, completely leaving out #2.
     * We inject #2 into it: #2,#3,#1.
     *
     * For subpages, the mounted page gets injected as well:
     * URL path /basic-mount-point/about-us
     * will give rootline #4,#2,#3,#1
     *
     * @return array Page uids of the rootline, deepest first, root last
     */
    protected static function loadRootLineUids($request): array
    {
        if (ApplicationType::fromRequest($request)->isFrontend()) {
            $rootLine = $request->getAttribute('frontend.controller')->rootLine;

        } else {
            $rootLine = BackendUtility::BEgetRootLine(
                $request->getQueryParams()['id']
            );
        }

        $rootLineUids = [];
        foreach ($rootLine as $row) {
            if (isset($row['_MOUNT_OL']) && $row['_MOUNT_OL']) {
                $rootLineUids[] = $row['_MOUNT_PAGE']['uid'];
            }
            $rootLineUids[] = $row['uid'];
        }

        return $rootLineUids;
    }

    protected static function redirectTo(string $url): void
    {
        $response = GeneralUtility::makeInstance(ResponseFactory::class)
            ->createResponse(302)
            ->withAddedHeader('location', $url);
        throw new PropagateResponseException($response);
    }
}
