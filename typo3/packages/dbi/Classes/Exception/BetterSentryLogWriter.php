<?php

namespace Mogic\Dbi\Exception;

use Networkteam\SentryClient\Client;
use Networkteam\SentryClient\Service\SentryService;
use Sentry\Breadcrumb;
use Sentry\State\Scope;
use TYPO3\CMS\Core\Log\LogLevel;
use TYPO3\CMS\Core\Log\LogRecord;

use function Sentry\withScope;

/**
 * Add the scheduler exception message to the log message
 *
 * @link https://github.com/networkteam/sentry_client/issues/106
 */
class BetterSentryLogWriter extends \Networkteam\SentryClient\SentryLogWriter
{
    /**
     * Forwards the log record to Sentry
     *
     * @return \TYPO3\CMS\Core\Log\Writer\WriterInterface $this
     */
    public function writeLog(LogRecord $record)
    {
        if (SentryService::isEnabled()
            && $this->shouldHandleLogMessage($record)
        ) {
            $data = $record->getData();
            if (isset($data['exception']) && $data['exception'] instanceof \Throwable) {
                $this->writeException($record);
                return $this;
            }
        }

        return parent::writeLog($record);
    }

    /**
     * Report a log record with an 'exception' object in its data.
     *
     * Exception objects in Sentry data properties do not get serialized
     * and thus get lost.
     * To keep them and their stacktrace, we send it as exception to Sentry.
     * The log message is kept as breadcrumb.
     *
     * TYPO3 scheduler logs such messages when tasks fail.
     */
    protected function writeException(LogRecord $record): void
    {
        $data = $record->getData();
        $exception = $data['exception'];
        unset($data['exception']);

        withScope(function (Scope $scope) use ($data, $exception, $record): void {
            $scope->setExtra('component', $record->getComponent());
            $scope->setExtra('data', $data);

            $message = $record->getMessage();
            if (method_exists($this, 'interpolate')) {
                $message = $this->interpolate($message, $record->getData());
            }
            $scope->addBreadcrumb(new Breadcrumb('error', 'default', 'log message', $message, []));

            Client::captureException($exception);
        });
    }
}
