<?php

namespace Mogic\Dbi\Service;

use PhpOffice\PhpSpreadsheet\Worksheet\Row;
use TYPO3\CMS\Core\Database\Connection;
use TYPO3\CMS\Core\Database\ConnectionPool;
use TYPO3\CMS\Core\Utility\GeneralUtility;

/**
 * Import a trading area spreadsheet file into the database.
 *
 * Used by the TradingAreaController
 */
class TradingAreaImport
{
    /**
     * Actually import the spreadsheet file
     *
     * @return array Array with import statistics. Keys:
     *               updated, inserted, deleted, unchanged, missingUsername
     *
     * @throws \DomainException
     */
    public function importFile(string $fileName, bool $deleteMissing): array
    {
        $spreadsheet = \PhpOffice\PhpSpreadsheet\IOFactory::load(
            $fileName, \PhpOffice\PhpSpreadsheet\Reader\IReader::READ_DATA_ONLY
        );
        $sheet = $spreadsheet->getSheet(0);

        $firstRow = static::rowToArray($sheet->getRowIterator(1, 1)->current());
        if (count(array_unique($firstRow)) != count($firstRow)) {
            throw new \DomainException('Doppelte Spaltennamen');
        }

        $reqColTitles = [
            'postalcode' => 'Postleitzahl',
            'username'   => 'FIO-Nutzername neu',
        ];
        $colKeys = [
            'postalcode' => null,
            'username'   => null,
        ];
        $flipRow = array_flip($firstRow);
        foreach ($reqColTitles as $key => $title) {
            if (!isset($flipRow[$title])) {
                throw new \DomainException('Spalte mit Titel "' . $title . '" fehlt.');
            }
            $colKeys[$key] = $flipRow[$title];
        }

        $stats = [
            'updated'   => 0,
            'inserted'  => 0,
            'deleted'   => 0,
            'unchanged' => 0,
            'missingUsername' => [],
        ];
        $postalcodes = [];

        /** @var \TYPO3\CMS\Core\Database\Query\QueryBuilder $queryBuilder */
        $queryBuilder = GeneralUtility::makeInstance(ConnectionPool::class)
            ->getQueryBuilderForTable('tx_dbi_tradingareas');
        $dbConn = GeneralUtility::makeInstance(ConnectionPool::class)
            ->getConnectionForTable('tx_dbi_tradingareas');
        $stmtExists = $queryBuilder->select('uid')
            ->from('tx_dbi_tradingareas')
            ->where(
                $queryBuilder->expr()->eq(
                    'postalcode',
                    $queryBuilder->createPositionalParameter(0, Connection::PARAM_STR)
                ),
                $queryBuilder->expr()->eq(
                    'fio_username',
                    $queryBuilder->createPositionalParameter(1, Connection::PARAM_STR)
                )
            )
            ->prepare();

        $rows = $sheet->rangeToArrayYieldRows(
            'A2:' . $sheet->getHighestDataColumn() . $sheet->getHighestDataRow(),
            null, false, false
        );
        foreach ($rows as $row) {
            $rowData = [
                'postalcode' => $row[$colKeys['postalcode']],
                'username'   => $row[$colKeys['username']],
            ];

            if ($deleteMissing) {
                $postalcodes[] = $rowData['postalcode'];
            }

            if ($rowData['username'] == '') {
                $stats['missingUsername'][] = $rowData['postalcode'];
                continue;
            }

            $stmtExists->bindValue(1, $rowData['postalcode']);
            $stmtExists->bindValue(2, $rowData['username']);
            if ($stmtExists->executeQuery()->fetchOne() !== false) {
                //row already exists
                $stats['unchanged']++;
                continue;
            }

            //we have to update to keep the population value!
            $numUpdated = $dbConn->update(
                'tx_dbi_tradingareas',
                [
                    'fio_username' => $rowData['username'],
                    'tstamp'       => time(),
                    'deleted'      => 0,
                    'hidden'       => 0,
                ],
                ['postalcode' => $rowData['postalcode']],
                [Connection::PARAM_STR]
            );
            if ($numUpdated === 0) {
                //update failed, we need to insert
                $dbConn->insert(
                    'tx_dbi_tradingareas',
                    [
                        'pid'       => 0,
                        'crdate'    => time(),
                        'tstamp'    => time(),
                        'cruser_id' => $GLOBALS['BE_USER']->user['uid'],

                        'postalcode'   => $rowData['postalcode'],
                        'fio_username' => $rowData['username'],
                    ],
                );
                $stats['inserted']++;

            } else {
                $stats['updated']++;
            }
        }

        if ($deleteMissing) {
            if (!count($postalcodes)) {
                throw new \DomainException(
                    'Datei enthält keine Vertriebsgebiete, deshalb wird nicht gelöscht.'
                );
            }

            $queryBuilder = GeneralUtility::makeInstance(ConnectionPool::class)
                ->getQueryBuilderForTable('tx_dbi_tradingareas');

            $stats['deleted'] = $queryBuilder
                ->delete('tx_dbi_tradingareas')
                ->where(
                    $queryBuilder->expr()->notIn('postalcode', $postalcodes),
                )
                ->executeStatement();
        }

        return $stats;
    }

    protected static function rowToArray(Row $row): array
    {
        $data = $row->getWorksheet()->rangeToArray(
            'A' . $row->getRowIndex()
            . ':' . $row->getWorksheet()->getHighestDataColumn() . $row->getRowIndex()
        );
        return $data[0];
    }
}
