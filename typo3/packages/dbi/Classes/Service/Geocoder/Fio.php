<?php

namespace Mogic\Dbi\Service\Geocoder;

use Mogic\Dbi\Exception\GeocoderException as GCE;
use TYPO3\CMS\Core\Configuration\ExtensionConfiguration;
use TYPO3\CMS\Core\Utility\GeneralUtility;

/**
 * Use the FIO Systems AG geocoding services
 *
 * <AUTHOR> <<EMAIL>>
 */
class Fio
{
    /**
     * Get the coordinates for an address
     *
     * @param string $country Country name or code
     * @param string $city    City name
     * @param string $zip     Postal code
     * @param string $street  Street and number
     *
     * @return mixed Array with "latitude" and "longitude" keys if successful.
     *               NULL when no address was empty
     *
     * @throws GGE When something went wrong
     */
    public function geocodeAddress($country, $city, $zip, $street)
    {
        $url = 'https://geoapify.fio.de/v1/geocode/search?format=json&limit=1'
             . '&apiKey=47a5703edcab496c91814717f03a3505';

        $query = '';
        if ($country == 'DE') {
            $country = 'de';
            $query .= '&filter=countrycode:' . urlencode($country);
        }
        if (!empty($zip)) {
            $query .= '&postcode=' . $zip;
        }
        if (!empty($city)) {
            $query .= '&city=' . urlencode($city);
        }
        if (($zip || $city) && !empty($street)) {
            if (strpos($street, ' ') > 0) {
                $streetParts = explode(' ', $street);
                $number = array_pop($streetParts);
                $street = implode(' ', $streetParts);

                $query .= '&street=' . urlencode($street);
                $query .= '&housenumber=' . urlencode($number);
            } else {
                $query .= '&street=' . urlencode($street);
            }
        }
        if ($query == '') {
            return null;
        }

        $url .= $query;

        $json = file_get_contents(
            $url,
            false,
            stream_context_create(['http' => ['ignore_errors' => true]])
        );
        if ($json === false) {
            throw new GCE('Error fetching geocoding results', GCE::GENERIC);
        }

        $result = json_decode($json, 1);
        if (!is_array($result) || !isset($result['results'][0])) {
            throw new GCE('No geo coordinates found', GCE::NORESULTS);
            return;
        }

        $coords = $result['results'][0];
        if (isset($coords['lat'])) {
            $lat = number_format($coords['lat'], 7, '.', '');
        }
        if (isset($coords['lon'])) {
            $lon = number_format($coords['lon'], 7, '.', '');
        }

        return [
            'latitude'  => $lat,
            'longitude' => $lon,
            'title'     => $coords['display_name'] ?? null,
        ];
    }
}
