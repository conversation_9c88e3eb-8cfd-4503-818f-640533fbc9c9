<?php

namespace Mogic\Dbi\Service;

use Mogic\Dbi\Exception\GeocoderException as GCE;

/**
 * Convert addresses to coordinates
 *
 * <AUTHOR> <<EMAIL>>
 */
class Geocoder
{
    protected $adapter;

    /**
     * Load geocoding adapter
     */
    public function __construct()
    {
        $this->adapter = new \Mogic\Dbi\Service\Geocoder\Fio();
    }

    /**
     * Get the coordinates for an address
     *
     * @param string $country Country name or code
     * @param string $city    City name
     * @param string $zip     Postal code
     * @param string $street  Street and number
     *
     * @return array Array with "latitude" and "longitude" keys if successful.
     *
     * @throws GGE When something went wrong
     */
    public function geocodeAddress($country, $city, $zip, $street)
    {
        return $this->adapter->geocodeAddress(
            $country, $city, $zip, $street
        );
    }

    /**
     * Get coordinates for a free-text search term
     *
     * @param string $term Search term
     *
     * @return array Array with "latitude" and "longitude" keys
     */
    public function geocodeSearchTerm($term)
    {
        if (is_numeric($term)) {
            //zip code
            return $this->adapter->geocodeAddress(
                'DE', null, $term, null
            );
        } else {
            //city
            return $this->adapter->geocodeAddress(
                'DE', $term, null, null
            );
        }
    }
}
