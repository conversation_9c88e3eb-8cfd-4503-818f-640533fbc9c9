<?php

namespace Mogic\Dbi\Service;

use TYPO3\CMS\Core\Configuration\ExtensionConfiguration;
use TYPO3\CMS\Core\Http\RequestFactory;
use TYPO3\CMS\Core\Utility\GeneralUtility;

/**
 * Helper for sending requests to Elanders MobileSales API
 *
 * <AUTHOR> <<EMAIL>>
 */
class ElandersApi
{
    /**
     * Map "anrede" form values to elanders "gender" values.
     *
     * @var array
     */
    protected static $anredeGenderMap = [
        'Herr' => 'GENDER_MALE',
        'Frau' => 'GENDER_FEMALE',
    ];

    /**
     * Map "titel" form values to elanders "title" values.
     *
     * @var array
     */
    protected static $titelTitleMap = [
        ''      => null,
        'Dr.'   => 'TITLE_DR',
        'dr'    => 'TITLE_DR',
        'dr.'   => 'TITLE_DR',
        'Prof.' => 'TITLE_PROF',
        'prof'  => 'TITLE_PROF',
        'prof.' => 'TITLE_PROF',
    ];

    /**
     * Map ImmoDataRequest::$immotype values to Elanders "objectRealestateType"
     * values.
     *
     * @var array
     */
    public static $immotypeMap = [
        2 => 'OWNED_FLAT',
        3 => 'HOUSE',
        5 => 'PROPERTY',
        6 => 'COMMERCIAL_PROPERTY',
    ];

    /**
     * Map ImmoDataRequest::$development values to Elanders "objectDevelopment"
     * values.
     *
     * @var array
     */
    public static $developmentMap = [
        1 => 'NOT_DEVELOPED',
        2 => 'PARTLY_DEVELOPED',
        3 => 'FULLY_DEVELOPED',
    ];

    /**
     * GET parameters for tracking that need to be preserved by the form.
     * Superset of $additionalGetParams
     *
     * @var array
     */
    public static $trackingParams = [
        'aktion',
        'campaign',
        'channel',
        'ad',
        'city',
        'te',
        'tc',
    ];

    /**
     * Last HTTP response in case of exceptions
     *
     * @var object
     */
    public static $lastRes;

    /**
     * Send a HTTP request to the elanders API and parse the response.
     * Also builds a nice exception message.
     *
     * @param string $url      Full URL
     * @param array  $postData Data to send to Elanders via POST as JSON
     *
     * @return mixed Parsed JSON API response
     *
     * @throws ApiException
     */
    public static function sendRequest(string $url, array $postData)
    {
        $msaConf = static::getConfig();
        $requestFactory = GeneralUtility::makeInstance(RequestFactory::class);
        $options = [
            'headers' => [
                'Accept'        => 'application/json',
                'Content-Type'  => 'application/json',
                'Authorization' => 'Basic ' . base64_encode(
                    $msaConf['user'] . ':' . $msaConf['pass']
                )
            ],
            'body' => json_encode($postData),
            'http_errors' => false,
        ];

        //debug($postData, 'POST ' . $url);

        $res = $requestFactory->request($url, 'POST', $options);
        if (intval($res->getStatusCode() / 100) != 2) {
            static::$lastRes = $res;
            $resData = json_decode((string) $res->getBody());

            $msg = 'API status not 2xx but ' . $res->getStatusCode();
            if (isset($resData->message)) {
                $msg .= ': ' . $resData->message;
            }
            if (isset($resData->fields) && is_array($resData->fields)) {
                $msg .= ': ' . implode(', ', $resData->fields);
            }
            throw new \Mogic\Dbi\Exception\ApiException($msg);
        }

        return json_decode($res->getBody());
    }

    public static function getBaseUrl(): string
    {
        return static::getConfig()['url'];
    }

    /**
     * Load MSA configuration from DBI extension config
     */
    protected static function getConfig(): array
    {
        $confManager = GeneralUtility::makeInstance(ExtensionConfiguration::class);
        return $confManager->get('dbi')['elandersMsa'];
    }

    public static function mapAnredeGender(string $anrede): ?string
    {
        if (!array_key_exists($anrede, static::$anredeGenderMap)) {
            throw new \UnexpectedValueException(
                'Elanders MSA mapping: Unsupported "anrede" value: "' . $anrede . '"',
                1709307711
            );
        }
        return static::$anredeGenderMap[$anrede];
    }

    public static function mapTitelTitle(string $titel): ?string
    {
        if (!array_key_exists($titel, static::$titelTitleMap)) {
            throw new \UnexpectedValueException(
                'Elanders MSA mapping: Unsupported "title" value: "' . $titel . '"',
                1709307722
            );
        }
        return static::$titelTitleMap[$titel];
    }
}
