<?php

namespace Mogic\Dbi\Backend;

use Mogic\Dbi\Powermail\Finisher\MobileSalesApiFinisher;
use TYPO3\CMS\Core\Database\ConnectionPool;
use TYPO3\CMS\Core\DataHandling\DataHandler;
use TYPO3\CMS\Core\Messaging\FlashMessage;
use TYPO3\CMS\Core\Messaging\FlashMessageService;
use TYPO3\CMS\Core\Service\FlexFormService;
use TYPO3\CMS\Core\Type\ContextualFeedbackSeverity;
use TYPO3\CMS\Core\Utility\GeneralUtility;

/**
 * Hook handler for TCA forms/records
 *
 * <AUTHOR> <<EMAIL>>
 */
class TceMainHook
{
    /**
     * If center geo coordinates should be updated.
     * Disabled during center import
     *
     * @var boolean
     */
    public static $updateCenterCoords = true;

    /**
     * Show flash messages, especially during geo coordinate updates
     * Disabled during realtor sync
     */
    public static $showFlashMessages = true;

    /**
     * Automatically set a content element's "header" field
     * from the fluid content element's settings.header value.
     *
     * Gets called after the TCA value array has been modified, before it gets
     * written into database.
     *
     * @param string $status      Modification status (update, new, ...)
     * @param string $table       Database table the record is in
     * @param mixed  $id          UID
     * @param array  $fieldArray  Data that got modified
     * @param object $dataHandler Caller
     *
     * @return void
     */
    public function processDatamap_postProcessFieldArray(
        $status, $table, $id, &$fieldArray, DataHandler $dataHandler
    ) {
        if ($table === 'tt_content') {
            $this->processTtContent(
                $status, $table, $id, $fieldArray, $dataHandler
            );

        } elseif ($table == 'tx_dbi_center') {
            $this->processTxDbiCenter(
                $status, $table, $id, $fieldArray, $dataHandler
            );

        }
    }

    /**
     * Process changes in the tt_content table.
     *
     * @param string $status      Modification status (update, new, ...)
     * @param string $table       Database table the record is in
     * @param mixed  $id          UID
     * @param array  $fieldArray  Data that got modified
     * @param object $dataHandler Caller
     */
    protected function processTtContent(
        $status, $table, $id, &$fieldArray, DataHandler $dataHandler
    ): void {
        $ctype = $dataHandler->datamap['tt_content'][$id]['CType'] ?? null;
        if ($ctype === 'mask_map') {
            $this->updateMapCoords($status, $id, $fieldArray, $dataHandler);

        } elseif ($ctype === 'powermail_pi1') {
            $this->validatePowermailMobileSalesApiFields($id, $fieldArray['pi_flexform'] ?? null);
        }
    }

    /**
     * Process changes in the tx_dbi_center table.
     *
     * @param string $status      Modification status (update, new, ...)
     * @param string $table       Database table the record is in
     * @param mixed  $id          UID
     * @param array  $fieldArray  Data that got modified
     * @param object $dataHandler Caller
     *
     * @return void
     */
    protected function processTxDbiCenter(
        $status, $table, $id, &$fieldArray, DataHandler $dataHandler
    ) {
        $this->updateCenterCoords(
            $status, $table, $id, $fieldArray, $dataHandler
        );
        $this->validateCenterFioNodeIds($status, $id, $fieldArray);
    }

    /**
     * Checks changes in center address fields.
     * If so, it retrieves the new coords and adds them to changes.
     *
     * @param string $status      Modification status (update, new, ...)
     * @param string $table       Database table the record is in
     * @param mixed  $id          UID
     * @param array  $fieldArray  Data that got modified
     * @param object $dataHandler Caller
     *
     * @return void
     */
    protected function updateCenterCoords(
        $status, $table, $id, &$fieldArray, DataHandler $dataHandler
    ) {
        if (!static::$updateCenterCoords) {
            return;
        }
        if ($status != 'update' && $status != 'new') {
            return;
        }

        if (!isset($fieldArray['address_street'])
            && !isset($fieldArray['address_postalcode'])
            && !isset($fieldArray['address_city'])
            && !isset($fieldArray['address_state'])
        ) {
            //no changes to addressfields
            return;
        }

        $zip = isset($fieldArray['address_postalcode'])
            ? $fieldArray['address_postalcode']
            : $dataHandler->checkValue_currentRecord['address_postalcode'];
        $city = isset($fieldArray['address_city'])
            ? $fieldArray['address_city']
            : $dataHandler->checkValue_currentRecord['address_city'];
        $street = isset($fieldArray['address_street'])
            ? $fieldArray['address_street']
            : $dataHandler->checkValue_currentRecord['address_street'];

        try {
            $geocoder = new \Mogic\Dbi\Service\Geocoder();
            $coords = $geocoder->geocodeAddress('DE', $city, $zip, $street);
            if ($coords === null) {
                //address was empty
                return;
            }

            $fieldArray['loc_lat']  = $coords['latitude'];
            $fieldArray['loc_long'] = $coords['longitude'];
            $this->showMessage('Geokoordinaten aktualisiert', ContextualFeedbackSeverity::OK);
        } catch (\Mogic\Dbi\Exception\GeocoderException $e) {
            $this->showMessage($e->getMessage());
        }
    }

    /**
     * Update coordinates in the mask_map content element
     *
     * @param string $status      Modification status (update, new, ...)
     * @param mixed  $id          UID
     * @param array  $fieldArray  Data that got modified
     * @param object $dataHandler Caller
     */
    protected function updateMapCoords($status, $id, &$fieldArray, $dataHandler)
    {
        if ($status != 'update' && $status != 'new') {
            return;
        }

        if (!isset($fieldArray['tx_mask_street'])
            && !isset($fieldArray['tx_mask_postalcode'])
            && !isset($fieldArray['tx_mask_city'])
            && !isset($fieldArray['tx_mask_latitude'])
            && !isset($fieldArray['tx_mask_longitude'])
        ) {
            //no changes to address fields
            return;
        }

        $street = isset($fieldArray['tx_mask_street'])
            ? $fieldArray['tx_mask_street']
            : $dataHandler->checkValue_currentRecord['tx_mask_street'];
        $postalCode = isset($fieldArray['tx_mask_postalcode'])
            ? $fieldArray['tx_mask_postalcode']
            : $dataHandler->checkValue_currentRecord['tx_mask_postalcode'];
        $city = isset($fieldArray['tx_mask_city'])
            ? $fieldArray['tx_mask_city']
            : $dataHandler->checkValue_currentRecord['tx_mask_city'];

        try {
            $geocoder = new \Mogic\Dbi\Service\Geocoder();
            $coords = $geocoder->geocodeAddress('DE', $city, $postalCode, $street);
            if ($coords === null) {
                //address was empty
                return;
            }

            $fieldArray['tx_mask_latitude']  = $coords['latitude'];
            $fieldArray['tx_mask_longitude'] = $coords['longitude'];
            $this->showMessage('Geokoordinaten aktualisiert', ContextualFeedbackSeverity::OK);
        } catch (\Mogic\Dbi\Exception\GeocoderException $e) {
            $this->showMessage($e->getMessage());
        }
    }

    /**
     * Check if other centers have the same node IDs assigned.
     *
     * @param int|string $id tx_dbi_center UID or "NEWxxx"
     */
    protected function validateCenterFioNodeIds(string $status, $id, array $fieldArray): void
    {
        if ($status != 'update' && $status != 'new') {
            return;
        }

        if (!isset($fieldArray['fio_group_ids'])) {
            return;
        }

        $newFioGroupIds = explode(',', $fieldArray['fio_group_ids']);
        if (!count($newFioGroupIds)) {
            return;
        }

        $qb = GeneralUtility::makeInstance(ConnectionPool::class)
            ->getQueryBuilderForTable('tx_dbi_center');
        $qb = $qb->select('name2')
            ->from('tx_dbi_center')
            ->where($qb->expr()->neq('uid', $id));

        $conditions = [];
        foreach ($newFioGroupIds as $fioGroupId) {
            $conditions[] = $qb->expr()->eq(
                'fio_group_ids', $qb->createNamedParameter($fioGroupId)
            );
            $conditions[] = $qb->expr()->like(
                'fio_group_ids', $qb->createNamedParameter('%,' . $fioGroupId . ',%')
            );
            $conditions[] = $qb->expr()->like(
                'fio_group_ids', $qb->createNamedParameter($fioGroupId . ',%')
            );
            $conditions[] = $qb->expr()->like(
                'fio_group_ids', $qb->createNamedParameter('%,' . $fioGroupId)
            );
        }
        $qb->andWhere($qb->expr()->or(...$conditions));

        $otherCenters = $qb->executeQuery()
            ->fetchAllAssociative();
        if (!count($otherCenters)) {
            return;
        }

        $this->showMessage(
            'Andere Center verwenden die gleichen FIO Node-IDs: "'
            . implode('", "', array_column($otherCenters, 'name2'))
            . '"'
        );
    }

    /**
     * Validate that the powermail form has the fields needed for Elanders MSA
     *
     * @param mixed $id tt_content UID or "NEWxxxx"
     */
    protected function validatePowermailMobileSalesApiFields($id, ?string $flexform): void
    {
        if ($flexform === null) {
            //flexform was not changed; we need to load it ourselves
            $queryBuilder = GeneralUtility::makeInstance(ConnectionPool::class)
                ->getQueryBuilderForTable('tt_content');
            $queryBuilder->getRestrictions()
                ->removeAll();
            $flexform = $queryBuilder->select('pi_flexform')
                ->from('tt_content')
                ->where($queryBuilder->expr()->eq('uid', $id))
                ->executeQuery()
                ->fetchOne();
        }

        $flexService = GeneralUtility::makeInstance(FlexFormService::class);
        $formData = $flexService->convertFlexFormContentToArray($flexform);

        $useMsa = (bool) $formData['settings']['flexform']['main']['msa'];
        $formId = (int) $formData['settings']['flexform']['main']['form'];

        if ($useMsa) {
            MobileSalesApiFinisher::validateRequiredFields($formId);
        }
    }

    /**
     * Show a warning flash message
     *
     * @param string $text     Text to show
     * @param int    $severity FlashMessage::WARNING, ERROR or so
     */
    protected function showMessage($text, $severity = ContextualFeedbackSeverity::WARNING): void
    {
        if (!static::$showFlashMessages) {
            return;
        }

        $message = GeneralUtility::makeInstance(
            FlashMessage::class, $text, '', $severity, true
        );
        $fms = GeneralUtility::makeInstance(FlashMessageService::class);
        $fms->getMessageQueueByIdentifier()->addMessage($message);
    }
}
