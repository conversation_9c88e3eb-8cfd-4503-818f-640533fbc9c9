<?php

namespace Mogic\Dbi\Backend;

use TYPO3\CMS\Backend\Routing\UriBuilder;
use TYPO3\CMS\Core\Imaging\Icon;
use TYPO3\CMS\Core\Imaging\IconFactory;
use TYPO3\CMS\Core\Utility\GeneralUtility;
use TYPO3\CMS\Extbase\Configuration\ConfigurationManager;

/**
 * Custom menu backend items in the page tree:
 * - new center wizard
 */
class ContextMenuProvider implements \TYPO3\CMS\Backend\ContextMenu\ItemProviders\ProviderInterface
{
    protected int $centerFolderUid;

    protected string $table = '';

    /**
     * Clicked record identifier (usually uid or file combined identifier)
     */
    protected string $identifier = '';

    /**
     * Context - from where the click menu was triggered (e.g. 'tree')
     */
    protected string $context = '';

    public function addItems(array $items): array
    {
        if ($this->identifier == $this->centerFolderUid) {
            $items = $this->addCenterFolderItems($items);
        }

        return $items;
    }

    protected function addCenterFolderItems(array $items): array
    {
        $iconFactory = GeneralUtility::makeInstance(IconFactory::class);

        $uriBuilder = GeneralUtility::makeInstance(UriBuilder::class);
        $createCenterUrl = (string) $uriBuilder->buildUriFromRoute(
            'dbi_createcenter', ['id' => $this->identifier]
        );

        $items = [
            'tx_dbi_createcenter' => [
                'type'           => 'item',
                'label'          => 'Neues Immobiliencenter',
                'icon'           => $iconFactory
                    ->getIcon('tx-dbi-center', Icon::SIZE_SMALL, 'overlay-new')
                    ->render(),
                'callbackAction' => 'jumpToUrl',
                'additionalAttributes' => [
                    'data-url'             => $createCenterUrl,
                    'data-callback-module' => '@mogic/dbi/context-menu-actions',
                ]
            ]
        ] + $items;

        return $items;
    }

    /**
     * Returns the priority of the provider. Higher priority value means provider is executed first
     */
    public function getPriority(): int
    {
        return 42;
    }

    /**
     * Checks if the provider can add items to the menu
     */
    public function canHandle(): bool
    {
        if ($this->table !== 'pages' || $this->context !== 'tree') {
            return false;
        }

        $this->loadPids();
        if ($this->identifier == $this->centerFolderUid) {
            return true;
        }

        return false;
    }

    /**
     * Initialize the current context.
     * This method is called directly after fetching the provider from the container.
     */
    public function setContext(string $table, string $identifier, string $context = ''): void
    {
        $this->table = $table;
        $this->identifier = $identifier;
        $this->context = $context;
    }

    protected function loadPids(): void
    {
        $typoscript = GeneralUtility::makeInstance(ConfigurationManager::class)->getConfiguration(
            ConfigurationManager::CONFIGURATION_TYPE_FRAMEWORK, 'Dbi'
        );

        $this->centerFolderUid = (int) $typoscript['settings']['centerFolderUid'];
    }
}
