<?php

namespace Mogic\Dbi\Backend\Controller;

use Mogic\Dbi\Domain\Repository\CenterRepository;
use Mogic\Dbi\Domain\Repository\TeammemberRepository;
use Mogic\Dbi\Domain\Repository\WebGroupRepository;
use Mogic\Dbi\Domain\Repository\WebmaklerUserRepository;
use TYPO3\CMS\Backend\Routing\UriBuilder;
use TYPO3\CMS\Backend\Template\Components\ButtonBar;
use TYPO3\CMS\Backend\Template\ModuleTemplate;
use TYPO3\CMS\Backend\Template\ModuleTemplateFactory;
use TYPO3\CMS\Core\Authentication\BackendUserAuthentication;
use TYPO3\CMS\Core\Core\Environment;
use TYPO3\CMS\Core\Database\ConnectionPool;
use TYPO3\CMS\Core\Imaging\Icon;
use TYPO3\CMS\Core\Imaging\IconFactory;
use TYPO3\CMS\Core\Localization\LanguageService;
use TYPO3\CMS\Core\Messaging\FlashMessage;
use TYPO3\CMS\Core\Messaging\FlashMessageService;
use TYPO3\CMS\Core\SysLog\Type as SystemLogType;
use TYPO3\CMS\Core\Type\ContextualFeedbackSeverity;
use TYPO3\CMS\Core\Utility\GeneralUtility;
use TYPO3\CMS\Extbase\Mvc\Controller\ActionController;
use TYPO3\CMS\Extbase\Mvc\Request;
use Psr\Http\Message\ResponseInterface;

/**
 * Backend module to import trading area mappings
 */
class FioApiDataController extends ActionController
{
    public function __construct(
        protected readonly CenterRepository $centerRepo,
        protected readonly TeammemberRepository $teammemberRepo,
        protected readonly WebGroupRepository $webgroupRepo,
        protected readonly WebmaklerUserRepository $webuserRepo,
        protected readonly ModuleTemplateFactory $moduleTemplateFactory,
        protected readonly UriBuilder $beUriBuilder
    ) {
    }

    /**
     * Show the upload and export form
     */
    public function showAction(string $table = null, int $uid = null): ResponseInterface
    {
        if ($table === null && $uid === null) {
            return $this->redirectToUri(
                $this->beUriBuilder->buildUriFromRoute('web_list', ['id' => $_GET['id'] ?? null])
            );
        }

        if ($table === 'tx_dbi_center') {
            return $this->showCenter($uid);

        } elseif ($table === 'tx_dbi_teammember') {
            return $this->showTeammember($uid);

        } else {
            die('Invalid param');
        }
    }

    protected function showCenter(int $uid): ResponseInterface
    {
        $center = $this->centerRepo->findByUid($uid);
        if (!$center) {
            die('Center not found');
        }

        $groupIds = explode(',', $center->fioGroupIds);
        $groups = [];
        foreach ($groupIds as $groupId) {
            try {
                $groups[] = $this->webgroupRepo->findOneById((int) $groupId);
            } catch (\GuzzleHttp\Exception\ClientException $e) {
                $groups[] = $e;
            }
        }

        $this->view->assign('center', $center);
        $this->view->assign('groups', $groups);
        $this->view->setTemplate('showCenter');

        $moduleTemplate = $this->moduleTemplateFactory->create($this->request);
        $moduleTemplate->setContent($this->view->render());
        $moduleTemplate->setTitle('FIO-Webmaklerdaten für Center');
        return $this->htmlResponse($moduleTemplate->renderContent());
    }

    protected function showTeammember(int $uid): ResponseInterface
    {
        $teammember = $this->teammemberRepo->findByUid($uid);
        if (!$teammember) {
            die('Team member not found');
        }

        try {
            $fioUser = $this->webuserRepo->findOneById($teammember->fioUserId);
        } catch (\GuzzleHttp\Exception\ClientException $e) {
            $fioUser = $e;
        }

        $this->view->assign('teammember', $teammember);
        $this->view->assign('fioUser', $fioUser);
        $this->view->setTemplate('showTeammember');

        $moduleTemplate = $this->moduleTemplateFactory->create($this->request);
        $moduleTemplate->setContent($this->view->render());
        $moduleTemplate->setTitle('FIO-Webmaklerdaten für Teammitglied');
        return $this->htmlResponse($moduleTemplate->renderContent());
    }
}
