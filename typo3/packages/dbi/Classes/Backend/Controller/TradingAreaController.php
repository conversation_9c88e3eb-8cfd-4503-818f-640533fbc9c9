<?php

namespace Mogic\Dbi\Backend\Controller;

use Mogic\Dbi\Helper\SpreadsheetStream;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Ods;
use TYPO3\CMS\Backend\Routing\UriBuilder;
use TYPO3\CMS\Backend\Template\Components\ButtonBar;
use TYPO3\CMS\Backend\Template\ModuleTemplate;
use TYPO3\CMS\Backend\Template\ModuleTemplateFactory;
use TYPO3\CMS\Core\Authentication\BackendUserAuthentication;
use TYPO3\CMS\Core\Core\Environment;
use TYPO3\CMS\Core\Database\ConnectionPool;
use TYPO3\CMS\Core\Imaging\Icon;
use TYPO3\CMS\Core\Imaging\IconFactory;
use TYPO3\CMS\Core\Localization\LanguageService;
use TYPO3\CMS\Core\Messaging\FlashMessage;
use TYPO3\CMS\Core\Messaging\FlashMessageService;
use TYPO3\CMS\Core\SysLog\Type as SystemLogType;
use TYPO3\CMS\Core\Type\ContextualFeedbackSeverity;
use TYPO3\CMS\Core\Utility\GeneralUtility;
use TYPO3\CMS\Extbase\Mvc\Controller\ActionController;
use TYPO3\CMS\Extbase\Mvc\Request;
use Psr\Http\Message\ResponseInterface;

/**
 * Backend module to import trading area mappings
 */
class TradingAreaController extends ActionController
{
    public function __construct(
        protected readonly IconFactory $iconFactory,
        protected readonly ModuleTemplateFactory $moduleTemplateFactory,
        protected readonly UriBuilder $beUriBuilder
    ) {
    }

    /**
     * Show the upload and export form
     */
    public function operationsAction(): ResponseInterface
    {
        if ($this->request->getMethod() === 'POST') {
            try {
                $stats = $this->handleUpload($this->request);

                $fms = GeneralUtility::makeInstance(FlashMessageService::class);
                $fms->getMessageQueueByIdentifier()->addMessage(
                    GeneralUtility::makeInstance(
                        FlashMessage::class,
                        sprintf(
                            '%d aktualisiert, %d eingefügt, %d gelöscht, %d unverändert',
                            $stats['updated'],
                            $stats['inserted'],
                            $stats['deleted'],
                            $stats['unchanged']
                        ),
                        'Import abgeschlossen',
                        ContextualFeedbackSeverity::OK, true
                    )
                );
                if (count($stats['missingUsername']) > 0) {
                    $fms->getMessageQueueByIdentifier()->addMessage(
                        GeneralUtility::makeInstance(
                            FlashMessage::class,
                            sprintf(
                                'Benutzernamensspalte war bei %d Zeilen leer: %s',
                                count($stats['missingUsername']),
                                implode(', ', $stats['missingUsername'])
                            ),
                            'Importproblem',
                            ContextualFeedbackSeverity::WARNING, true
                        )
                    );
                }

                $this->getBackendUser()->writelog(
                    SystemLogType::DB,
                    2,//update
                    0,//message
                    1,
                    'Trading area import: %d updated, %d inserted, %d deleted, %d unchanged',
                    [
                        $stats['updated'],
                        $stats['inserted'],
                        $stats['deleted'],
                        $stats['unchanged']
                    ]
                );
            } catch (\DomainException $e) {
                $this->view->assign(
                    'errors',
                    ['file' => $e->getMessage()]
                );
            }
        }

        $numAreas = GeneralUtility::makeInstance(ConnectionPool::class)
            ->getConnectionForTable('tx_dbi_tradingareas')
            ->count('*', 'tx_dbi_tradingareas', []);

        $this->view->assign('numAreas', $numAreas);

        $this->view->setTemplate('operations');
        $moduleTemplate = $this->moduleTemplateFactory->create($this->request);
        $this->addHeaderButtons($moduleTemplate, 'operations');
        $moduleTemplate->setContent($this->view->render());
        $moduleTemplate->setTitle('Vertriebsgebietszuordnungen verwalten');
        return $this->htmlResponse($moduleTemplate->renderContent());
    }

    public function datacheckAction(): ResponseInterface
    {
        $this->view->setTemplate('datacheck');

        $this->view->assign('missingMembers', $this->getMissingMembers());
        $this->view->assign('disabledMembers', $this->getDisabledMembers());
        $this->view->assign('memberlessAreas', $this->getMemberlessAreas());

        $moduleTemplate = $this->moduleTemplateFactory->create($this->request);
        $moduleTemplate->setTitle('Datenprüfung Vertriebsgebiete');
        $this->addHeaderButtons($moduleTemplate, 'datacheck');
        $moduleTemplate->setContent($this->view->render());
        return $this->htmlResponse($moduleTemplate->renderContent());
    }

    /**
     * Download the current postal codes and FIO user names as .ods file
     */
    public function exportAction(): ResponseInterface
    {
        $spreadsheet = new Spreadsheet();
        $activeWorksheet = $spreadsheet->getActiveSheet();
        $activeWorksheet->setTitle('Vertriebsgebietzuordnungen');
        $activeWorksheet->getColumnDimension('A')->setAutoSize(true);
        $activeWorksheet->getColumnDimension('B')->setAutoSize(true);
        $activeWorksheet->getStyle('A1:B1')->applyFromArray(
            ['font' => ['bold' => true]]
        );

        $rowNum = 1;
        $activeWorksheet->setCellValue([1, $rowNum], 'Postleitzahl');
        $activeWorksheet->setCellValue([2, $rowNum], 'FIO-Nutzername');
        $rowNum++;

        $queryBuilder = GeneralUtility::makeInstance(ConnectionPool::class)
            ->getQueryBuilderForTable('tx_dbi_tradingareas');
        $tradingAreasResult = $queryBuilder
            ->select('postalcode', 'fio_username')
            ->from('tx_dbi_tradingareas')
            ->executeQuery();

        while ($taRow = $tradingAreasResult->fetchAssociative()) {
            $activeWorksheet->setCellValue([1, $rowNum], $taRow['postalcode']);
            $activeWorksheet->setCellValue([2, $rowNum], $taRow['fio_username']);
            $rowNum++;
        }

        $writer = new Ods($spreadsheet);

        return $this->responseFactory->createResponse()
            ->withHeader('Content-Type', 'application/vnd.oasis.opendocument.spreadsheet')
            ->withHeader(
                'Content-Disposition',
                'attachment; filename=DBI-Vertriebsgebietszuordnungen ' . date('Y-m-d H-i') . '.ods'
            )
            ->withBody(new \Mogic\Dbi\Helper\SpreadsheetStream($writer));
    }

    /**
     * Get member names that hav a trading area but are hidden or deleted
     *
     * @return array
     */
    protected function getDisabledMembers(): array
    {
        $sql = <<<SQL
            SELECT DISTINCT
              ta.fio_username AS username,
              tm1.uid, tm1.deleted, tm1.hidden,
              ta.uid as ta_uid, ta.postalcode
            FROM tx_dbi_tradingareas AS ta
              JOIN tx_dbi_teammember AS tm1
                ON ta.fio_username = tm1.fio_username
              LEFT JOIN tx_dbi_teammember as tm2
                ON tm1.fio_username = tm2.fio_username
                  AND tm1.uid != tm2.uid
                  AND tm2.hidden = 0
                  AND tm2.deleted = 0

            WHERE (tm1.deleted = 1 OR tm1.hidden = 1)
              AND tm2.deleted IS NULL
              AND ta.fio_username != ''
            ORDER BY ta.fio_username, ta.postalcode
            SQL;

        $res = GeneralUtility::makeInstance(ConnectionPool::class)
            ->getConnectionForTable('tx_dbi_tradingareas')
            ->prepare($sql)->executeQuery();

        //group by name
        $disabledMembers = [];
        while ($row = $res->fetchAssociative()) {
            if (!isset($disabledMembers[$row['username']])) {
                $disabledMembers[$row['username']] = [
                    'username' => $row['username'],
                    'uid'      => $row['uid'],
                    'hidden'   => $row['hidden'],
                    'deleted'  => $row['deleted'],
                ];
            }
            $disabledMembers[$row['username']]['areas'][] = [
                'uid'        => $row['ta_uid'],
                'postalcode' => $row['postalcode'],
            ];
        }

        return $disabledMembers;
    }

    /**
     * @return array Array of arrays arrays with "uid" and "postalcode"
     */
    protected function getMemberlessAreas(): array
    {
        $sql = <<<SQL
            SELECT ta.uid, ta.postalcode
            FROM tx_dbi_tradingareas AS ta
            WHERE ta.fio_username = ''
            ORDER BY ta.postalcode
            SQL;

        $res = GeneralUtility::makeInstance(ConnectionPool::class)
            ->getConnectionForTable('tx_dbi_tradingareas')
            ->prepare($sql)->executeQuery();

        return $res->fetchAllAssociative();
    }

    /**
     * Get member names that have a trading area but no member record
     *
     * @return array Key is the fio user name.
     *               Value is an array of arrays with "uid" and "postalcode"
     */
    protected function getMissingMembers(): array
    {
        $sql = <<<SQL
            SELECT ta.fio_username, ta.uid, ta.postalcode
            FROM tx_dbi_tradingareas AS ta
            LEFT JOIN tx_dbi_teammember AS tm
                ON ta.fio_username = tm.fio_username
            WHERE tm.fio_username IS NULL
            ORDER BY ta.fio_username, ta.postalcode
            SQL;

        $res = GeneralUtility::makeInstance(ConnectionPool::class)
            ->getConnectionForTable('tx_dbi_tradingareas')
            ->prepare($sql)->executeQuery();

        //group by name
        $missingMembers = [];
        while ($row = $res->fetchAssociative()) {
            $missingMembers[$row['fio_username']][] = [
                'uid'        => $row['uid'],
                'postalcode' => $row['postalcode'],
            ];
        }

        return $missingMembers;
    }

    /**
     * @return array Array with import statistics. Keys:
     *               updated, inserted, deleted, unchanged
     *
     * @throws \DomainException On errors
     */
    protected function handleUpload(Request $request): array
    {
        $files = $request->getUploadedFiles();
        if (!isset($files['file'])) {
            throw new \DomainException('Datei fehlt');
        }

        /** @var \TYPO3\CMS\Core\Http\UploadedFile $file */
        $file = $files['file'];
        if ($file->getError() != UPLOAD_ERR_OK) {
            throw new \DomainException('Uploadfehler: ' . $file->getError());
        }

        $temporaryPath = Environment::getVarPath() . '/transient/';
        if (!@is_dir($temporaryPath)) {
            GeneralUtility::mkdir($temporaryPath);
        }

        $fileName = $temporaryPath
            . 'trading-upload_' . date('YmdHi', $GLOBALS['EXEC_TIME'])
            . '.' . pathinfo($file->getClientFilename(), PATHINFO_EXTENSION);

        $file->moveTo($fileName);

        $deleteMissing = (bool) ($request->getParsedBody()['deleteMissing'] ?? false);

        $tai = new \Mogic\Dbi\Service\TradingAreaImport();
        $stats = $tai->importFile($fileName, $deleteMissing);

        //clean up
        if (file_exists($fileName)) {
            unlink($fileName);
        }

        return $stats;
    }

    protected function addHeaderButtons(ModuleTemplate $template, string $action): void
    {
        $docHeader = $template->getDocHeaderComponent();
        $menu = $docHeader->getMenuRegistry()->makeMenu();
        $menu->setIdentifier('TradingAreaOperationsMenu');
        $menu->addMenuItem(
            $menu->makeMenuItem()
                ->setTitle('Datenprüfung')
                ->setHref(
                    $this->beUriBuilder->buildUriFromRoute('dbi_tradingarea.TradingArea_datacheck')
                )
                ->setActive($action === 'datacheck')
        );
        $menu->addMenuItem(
            $menu->makeMenuItem()
                ->setTitle('Import/Export')
                ->setHref(
                    $this->beUriBuilder->buildUriFromRoute('dbi_tradingarea.TradingArea_operations')
                )
                ->setActive($action === 'operations')
        );

        $docHeader->getMenuRegistry()->addMenu($menu);

        $buttonBar = $template->getDocHeaderComponent()->getButtonBar();

        $currentRoute = 'dbi_tradingarea.TradingArea_' . $action;
        $reloadButton = $buttonBar->makeLinkButton()
            ->setTitle(
                $this->getLanguageService()->sL(
                    'LLL:EXT:core/Resources/Private/Language/locallang_core.xlf:labels.reload'
                )
            )
            ->setIcon($this->iconFactory->getIcon('actions-refresh', Icon::SIZE_SMALL))
            ->setHref($this->beUriBuilder->buildUriFromRoute($currentRoute));

        $buttonBar->addButton($reloadButton, ButtonBar::BUTTON_POSITION_RIGHT, 1);
    }

    protected function getBackendUser(): BackendUserAuthentication
    {
        return $GLOBALS['BE_USER'];
    }

    protected function getLanguageService(): LanguageService
    {
        return $GLOBALS['LANG'];
    }
}
