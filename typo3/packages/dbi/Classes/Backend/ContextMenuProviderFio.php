<?php

namespace Mogic\Dbi\Backend;

use TYPO3\CMS\Backend\Routing\UriBuilder;
use TYPO3\CMS\Core\Imaging\Icon;
use TYPO3\CMS\Core\Imaging\IconFactory;
use TYPO3\CMS\Core\Utility\GeneralUtility;
use TYPO3\CMS\Extbase\Configuration\ConfigurationManager;

/**
 * Custom menu backend items for centers and team members
 */
class ContextMenuProviderFio implements \TYPO3\CMS\Backend\ContextMenu\ItemProviders\ProviderInterface
{
    protected string $table = '';

    /**
     * Clicked record identifier (usually uid or file combined identifier)
     */
    protected string $identifier = '';

    /**
     * Context - from where the click menu was triggered (e.g. 'tree')
     */
    protected string $context = '';

    public function addItems(array $items): array
    {
        $iconFactory = GeneralUtility::makeInstance(IconFactory::class);

        $uriBuilder = GeneralUtility::makeInstance(UriBuilder::class);
        $detailsUrl = (string) $uriBuilder->buildUriFromRoute(
            'dbi_fioapidata',
            ['table' => $this->table, 'uid' => $this->identifier]
        );

        $items = $items + [
            'tx_dbi_fioapidata' => [
                'type'           => 'item',
                'label'          => 'FIO-Webmaklerdaten',
                'icon'           => $iconFactory
                    ->getIcon('actions-webhook', Icon::SIZE_SMALL)
                    ->render(),
                'callbackAction' => 'jumpToUrl',
                'additionalAttributes' => [
                    'data-url'             => $detailsUrl,
                    'data-callback-module' => '@mogic/dbi/context-menu-actions',
                ]
            ]
        ];

        return $items;
    }

    /**
     * Returns the priority of the provider. Higher priority value means provider is executed first
     */
    public function getPriority(): int
    {
        return 43;
    }

    /**
     * Checks if the provider can add items to the menu
     */
    public function canHandle(): bool
    {
        return $this->table === 'tx_dbi_center' || $this->table === 'tx_dbi_teammember';
    }

    /**
     * Initialize the current context.
     * This method is called directly after fetching the provider from the container.
     */
    public function setContext(string $table, string $identifier, string $context = ''): void
    {
        $this->table = $table;
        $this->identifier = $identifier;
        $this->context = $context;
    }
}
