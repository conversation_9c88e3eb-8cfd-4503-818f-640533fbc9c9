<?php

namespace Mogic\Dbi\Backend\EventListener;

use TYPO3\CMS\Backend\Controller\Event\ModifyPageLayoutContentEvent;
use TYPO3\CMS\Core\Database\ConnectionPool;
use TYPO3\CMS\Core\Messaging\FlashMessage;
use TYPO3\CMS\Core\Messaging\FlashMessageRendererResolver;
use TYPO3\CMS\Core\Type\ContextualFeedbackSeverity;
use TYPO3\CMS\Core\Utility\GeneralUtility;

/**
 * Modify "Web > Page"
 */
class PageLayoutEventListener
{
    public function __invoke(ModifyPageLayoutContentEvent $event): void
    {
        // Get the current page ID
        $pageUid = (int)($event->getRequest()->getQueryParams()['id'] ?? 0);
        if ($pageUid === 0) {
            return;
        }

        $this->showCenterNotes($event, $pageUid);
    }

    /**
     * Add a hint for editors about the center
     */
    protected function showCenterNotes(ModifyPageLayoutContentEvent $event, int $pageUid): void
    {
        $centerRecord = $this->getCenterRecord($pageUid);
        if (!$centerRecord) {
            return;
        }

        $messages = [
            $this->getCenterDisabledNote($centerRecord['hidden']),
            $this->getCenterRedirectNote($centerRecord['uid']),
            $this->getCenterPageNote(),
        ];

        $event->addHeaderContent(
            GeneralUtility::makeInstance(FlashMessageRendererResolver::class)
                ->resolve()
                ->render(array_filter($messages))
        );
    }

    /**
     * Add a hint for editors where to edit center content elements
     */
    protected function getCenterPageNote(): FlashMessage
    {
        return GeneralUtility::makeInstance(
            FlashMessage::class,
            'Der Seiteninhalt für Immobiliencenter wird zentral gepflegt,'
            . ' und zwar unter System > Centerinhalt',
            'Immobiliencenter',
            ContextualFeedbackSeverity::INFO,
            false
        );
    }

    /**
     * Flash message that a center redirects to a different one
     */
    protected function getCenterDisabledNote(bool $disabled): ?FlashMessage
    {
        if (!$disabled) {
            return null;
        }

        return GeneralUtility::makeInstance(
            FlashMessage::class,
            'Der Centerdatensatz ist deaktiviert',
            'Immobiliencenter',
            ContextualFeedbackSeverity::ERROR,
            false
        );
    }

    /**
     * Flash message that a center redirects to a different one
     */
    protected function getCenterRedirectNote(int $centerRecordUid): ?FlashMessage
    {
        $queryBuilder = GeneralUtility::makeInstance(ConnectionPool::class)
            ->getQueryBuilderForTable('tx_dbi_center');
        $queryBuilder->getRestrictions()
            ->removeAll();
        $targetRow = $queryBuilder->select('target.name2', 'target.uid', 'target.pid')
            ->from('tx_dbi_center')
            ->join(
                'tx_dbi_center',
                'tx_dbi_center', 'target',
                $queryBuilder->expr()->eq('tx_dbi_center.redirect', 'target.uid')
            )
            ->where($queryBuilder->expr()->eq('tx_dbi_center.uid', $centerRecordUid))
            ->executeQuery()
            ->fetchAssociative();

        if (!$targetRow) {
            return null;
        }

        return GeneralUtility::makeInstance(
            FlashMessage::class,
            'Dieses Center leitet um auf'
            . ' "' . $targetRow['name2'] . '"'
            . ' #' . $targetRow['uid'],
            'Umleitung',
            ContextualFeedbackSeverity::WARNING,
            false
        );
    }

    protected function getCenterRecord(int $pageUid): ?array
    {
        $queryBuilder = GeneralUtility::makeInstance(ConnectionPool::class)
            ->getQueryBuilderForTable('tx_dbi_center');
        $queryBuilder->getRestrictions()
            ->removeAll();
        $res = $queryBuilder->select('uid', 'hidden')
            ->from('tx_dbi_center')
            ->where($queryBuilder->expr()->eq('pid', $pageUid))
            ->executeQuery()
            ->fetchAssociative();

        return $res === false ? null : $res;
    }
}
