<?php

namespace Mogic\Dbi\Backend\EventListener;

use Psr\Http\Message\ServerRequestInterface;
use TYPO3\CMS\Backend\Template\Components\ModifyButtonBarEvent;
use TYPO3\CMS\Backend\Controller\PageLayoutController;
use TYPO3\CMS\Backend\Routing\UriBuilder;
use TYPO3\CMS\Backend\Template\Components\ButtonBar;
use TYPO3\CMS\Core\Database\ConnectionPool;
use TYPO3\CMS\Core\Database\Query\Restriction\EndTimeRestriction;
use TYPO3\CMS\Core\Database\Query\Restriction\HiddenRestriction;
use TYPO3\CMS\Core\Database\Query\Restriction\StartTimeRestriction;
use TYPO3\CMS\Core\Imaging\Icon;
use TYPO3\CMS\Core\Imaging\IconFactory;
use TYPO3\CMS\Core\Utility\GeneralUtility;

/**
 * Add buttons to the doc header button bar
 *
 * 1. web>page: to edit the immo project record
 * 2. web>list: to manage trading areas
 *
 * <AUTHOR> <<EMAIL>>
 */
class PageLayoutButtonBarEventListener
{
    public function __invoke(ModifyButtonBarEvent $event): void
    {
        $routeKey = $this->getRequest()->getAttribute('route')->getOption('_identifier');
        if ($routeKey !== 'web_layout' && $routeKey !== 'web_list') {
            return;
        }

        $buttons = $event->getButtons();

        $pageUid = (int)($this->getRequest()->getQueryParams()['id'] ?? 0);
        if ($pageUid === 0) {
            $table = $this->getRequest()->getQueryParams()['table'] ?? null;
            if ($table === 'tx_dbi_tradingareas') {
                $buttons = $this->addTradingAreaButton($buttons, $event->getButtonBar());
            }
        }

        if ($pageUid !== 0) {
            $buttons = $this->addCenterRecordButton(
                $routeKey, $pageUid, $buttons, $event->getButtonBar()
            );
        }

        $event->setButtons($buttons);
    }

    /**
     * Add a "edit center record" button to top of web>page
     */
    protected function addCenterRecordButton(
        string $routeKey, int $pageUid, array $buttons, $buttonBar
    ): array {
        $centerRecordUid = $this->getCenterRecordUid($pageUid);
        if (!$centerRecordUid) {
            return $buttons;
        }

        $uriBuilder = GeneralUtility::makeInstance(UriBuilder::class);
        $editUrl = $uriBuilder->buildUriFromRoute(
            'record_edit',
            [
                'edit' => [
                    'tx_dbi_center' => [
                        $centerRecordUid => 'edit'
                    ]
                ],
                'returnUrl' => GeneralUtility::getIndpEnv('REQUEST_URI')
            ]
        );

        $iconFactory = GeneralUtility::makeInstance(IconFactory::class);
        $button = $buttonBar->makeLinkButton();
        $button->setShowLabelText(true);
        $button->setIcon(
            $iconFactory->getIcon('tcarecords-tx_dbi_center-default', Icon::SIZE_SMALL)
        );
        $button->setTitle('Center bearbeiten');
        $button->setHref($editUrl);

        $posMap = [
            'web_layout' => 2,
            'web_list' => 20,
        ];

        $buttons[ButtonBar::BUTTON_POSITION_LEFT][$posMap[$routeKey]][] = $button;
        return $buttons;
    }

    protected function getCenterRecordUid(int $pageUid): ?int
    {
        $queryBuilder = GeneralUtility::makeInstance(ConnectionPool::class)
            ->getQueryBuilderForTable('tx_dbi_center');
        $queryBuilder->getRestrictions()
            ->removeByType(HiddenRestriction::class)
            ->removeByType(StartTimeRestriction::class)
            ->removeByType(EndTimeRestriction::class);
        $res = $queryBuilder->select('uid')
            ->from('tx_dbi_center')
            ->where($queryBuilder->expr()->eq('pid', $pageUid))
            ->executeQuery()
            ->fetchOne();

        return $res === false ? null : $res;
    }

    protected function getRequest(): ServerRequestInterface
    {
        return $GLOBALS['TYPO3_REQUEST'];
    }

    /**
     * Add a "Manage trading areas" button to top of web>list view when
     * the tx_dbi_tradingareas table is selected.
     */
    protected function addTradingAreaButton(array $buttons, $buttonBar): array
    {
        $uriBuilder = GeneralUtility::makeInstance(UriBuilder::class);
        $taUrl = $uriBuilder->buildUriFromRoute('dbi_tradingarea');

        $iconFactory = GeneralUtility::makeInstance(IconFactory::class);
        $button = $buttonBar->makeLinkButton();
        $button->setShowLabelText(true);
        $button->setIcon(
            $iconFactory->getIcon('tx-dbi-tradingarea', Icon::SIZE_SMALL)
        );
        $button->setTitle('Vertriebsgebietsverwaltung');
        $button->setHref($taUrl);

        $buttons[ButtonBar::BUTTON_POSITION_LEFT][20][] = $button;
        return $buttons;
    }

}
