{"name": "mogic/deubaimmo-typo3", "description": "Deutsche Bank Immobilien TYPO3", "license": "GPL-2.0-or-later", "type": "project", "repositories": [{"type": "path", "url": "packages/*"}, {"type": "vcs", "url": "https://github.com/mogic-le/aus_driver_amazon_s3"}], "config": {"allow-plugins": {"typo3/class-alias-loader": true, "typo3/cms-composer-installers": true, "php-http/discovery": true, "cweagans/composer-patches": true}, "platform": {"php": "8.2"}, "sort-packages": true}, "extra": {"patches": {"typo3/cms-backend": {"#106917 First save of table field fails when delimiter field is missing": "patches/typo3-cms-backend-106917-table.patch"}, "typo3/cms-core": {"#103215 Duplicate error page content": "patches/typo3-cms-core-103215-duplicate-error-content.patch", "#104928: Avoid PHP errors with broken FE URL arguments": "patches/typo3-cms-core-104928-arg-validate.patch"}, "typo3/cms-frontend": {"#104928: Avoid PHP errors with broken FE URL arguments": "patches/typo3-cms-frontend-104928-arg-validate.patch"}}}, "require": {"andersundsehr/aus-driver-amazon-s3": "dev-mogic-master", "b13/container": "^3.1", "b13/masi": "*", "cweagans/composer-patches": "^1.7", "fluidtypo3/vhs": "*", "fullstackfreelancer/ce-timeline": "^4.0", "helhum/typo3-console": "*", "ichhabrecht/content-defender": "*", "in2code/powermail": "^12.1", "jigal/t3adminer": "^12.0", "kamermans/guzzle-oauth2-subscriber": "^1.0", "mask/mask": "*", "mogic/deubaimmo-dbi": "dev-develop", "mogic/t3x-scheduler-status": "^0.3", "netresearch/jsonmapper": "^4.4", "networkteam/sentry-client": "*", "php-http/guzzle7-adapter": "^1.0", "phpoffice/phpspreadsheet": "^2.1", "sitegeist/fluid-components": "^3.6", "symfony/var-dumper": "^7.2", "typo3/cms-backend": "^12.4.0", "typo3/cms-belog": "^12.4.0", "typo3/cms-beuser": "^12.4.0", "typo3/cms-core": "^12.4.0", "typo3/cms-dashboard": "^12.4.0", "typo3/cms-extbase": "^12.4.0", "typo3/cms-extensionmanager": "^12.4.0", "typo3/cms-filelist": "^12.4.0", "typo3/cms-fluid": "^12.4.0", "typo3/cms-fluid-styled-content": "^12.4.0", "typo3/cms-frontend": "^12.4.0", "typo3/cms-info": "^12.4.0", "typo3/cms-install": "^12.4.0", "typo3/cms-linkvalidator": "^12.4.0", "typo3/cms-lowlevel": "^12.4", "typo3/cms-reactions": "^12.4.0", "typo3/cms-redirects": "^12.4.0", "typo3/cms-rte-ckeditor": "^12.4.0", "typo3/cms-scheduler": "^12.4", "typo3/cms-seo": "^12.4.0", "typo3/cms-setup": "^12.4.0", "typo3/cms-sys-note": "^12.4", "typo3/cms-t3editor": "^12.4.0", "typo3/cms-tstemplate": "^12.4.0", "typo3/cms-viewpage": "^12.4.0", "typo3/cms-webhooks": "^12.4.0"}, "require-dev": {"mogic/mogic-phpcs": "dev-master"}}