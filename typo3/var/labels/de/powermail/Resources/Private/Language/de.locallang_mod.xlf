<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<xliff version="1.0">
  <file source-language="en" datatype="plaintext" original="EXT:powermail/Resources/Private/Language/locallang_mod.xlf" date="2014-05-02T12:00:00Z" product-name="powermail" target-language="de">
    <header/>
    <body>
      <trans-unit id="mlang_tabs_tab" resname="mlang_tabs_tab" approved="yes">
        <source>Mails</source>
        <target state="final">Powermail</target>
      </trans-unit>
      <trans-unit id="mlang_labels_tabdescr" resname="mlang_labels_tabdescr" approved="yes">
        <source>List of all registered mails in database. Possibility to filter and export to CSV (Excel).</source>
        <target state="final">Liste aller in die Datenbank eingetragenen Mails mit Filter-Möglichkeit und CSV-Export (Excel).</target>
      </trans-unit>
      <trans-unit id="mlang_labels_tablabel" resname="mlang_labels_tablabel" approved="yes">
        <source>Powermail list</source>
        <target state="final">Powermail Liste</target>
      </trans-unit>
      <trans-unit id="pluginWizardTitle" resname="pluginWizardTitle" approved="yes">
        <source>Powermail</source>
        <target state="final">Powermail</target>
      </trans-unit>
      <trans-unit id="pluginWizardDescription" resname="pluginWizardDescription" approved="yes">
        <source>Powerful and easy mailform extension optimized for editors. Powermail offers data storing, extended input validation, advanced spam-prevention, marketing analyses and a lot of configuration settings.</source>
        <target state="final">Umfangreiche und einfache Erweiterung für E-Mail-Formulare, optimiert für Redakteure. Powermail bietet die Speicherung von Daten, erweiterte Validierung von Eingaben, fortschrittlichen Spam-Schutz, Marketing-Informationen und sehr viele Optionen für die Konfiguration.</target>
      </trans-unit>
    </body>
  </file>
</xliff>
