stages:
  - docker
  - preparation
  - testing
  - pushing
  - startVPN
  - pre-deploy
  - deploy
  - onSystem
  - onSystem-cleanup
  - stopVPN
  - manual
  - sbom

# Variables
variables:
  PRODUCTIONBRANCH: master
  STAGINGBRANCH: develop
  BASEIMAGE: gitlab-docker.mogic.com/docker/mogic-base-image:webapp-jammy-php8.2
  BUILDIMAGE: gitlab-docker.mogic.com/fio/deubaimmo/frontend-assets:latest


.add_deploy_key: &add_deploy_key
  - 'which ssh-agent || ( apt-get update -y && apt-get install openssh-client git -y )'
  - eval $(ssh-agent -s)
  - DEPLOY_KEY=$(vault kv get -field=ssh-key-private devops/SSH/Mogic-General-Refresh)
  - echo "$DEPLOY_KEY" | tr -d '\r' | ssh-add - > /dev/null
  - mkdir -p ~/.ssh
  - chmod 700 ~/.ssh
  - ssh-keyscan gitlab.mogic.com >> ~/.ssh/known_hosts
  - chmod 644 ~/.ssh/known_hosts
  - if [ "$CI_COMMIT_REF_SLUG" == "$PRODUCTIONBRANCH" ];
      then export ENVIRONMENT='production'; export VAULTPATH_KV2="projects_prod/data/$CI_PROJECT_NAMESPACE/$CI_PROJECT_NAME"; export VAULTPATH_KV1="projects_prod/$CI_PROJECT_NAMESPACE/$CI_PROJECT_NAME";
      else export ENVIRONMENT='staging'; export VAULTPATH_KV2="projects/data/$CI_PROJECT_NAMESPACE/$CI_PROJECT_NAME/staging"; VAULTPATH_KV1="projects/$CI_PROJECT_NAMESPACE/$CI_PROJECT_NAME/staging"; fi

################ Docker build section for images in this repo ######################################

.buildLocalDockerImages: &buildLocalDockerImages
  - docker login -u gitlab-ci-token -p $CI_JOB_TOKEN gitlab-docker.mogic.com
  - docker run --rm --privileged multiarch/qemu-user-static --reset -p yes || true
  - docker context create mogic-context || true
  - docker buildx create mogic-context --name multiarch --driver docker-container --use || true
  - docker buildx build --no-cache --platform linux/amd64 --push -t gitlab-docker.mogic.com/fio/deubaimmo/$IMAGENAME $DOCKERFILEPATH

.docker_build_template: &docker_build_template
  image: gitlab-docker.mogic.com/docker/gitlab-ci-git-dind
  variables:
    DOCKER_DRIVER: overlay2
    DOCKER_TLS_CERTDIR: "/certs"
  script:
    - *buildLocalDockerImages

build-frontend-assets:
  <<: *docker_build_template
  stage: docker
  variables:
    IMAGENAME: frontend-assets:latest
    DOCKERFILEPATH: ./docker/build-frontend-assets/
  only:
    changes:
      - docker/build-frontend-assets/**/*
    refs:
      - develop

###################################################################################################

composer:
  stage: preparation
  image: $BASEIMAGE
  script:
    - cd typo3/ && composer2 install
  cache:
    - key: composer-$CI_COMMIT_REF_SLUG
      paths:
        - ./typo3/vendor/
        - ./typo3/public/
        - ./typo3/public/_assets/
      policy: pull-push
  only:
    refs:
      - branches
    changes:
      - typo3/composer.json
      - typo3/composer.lock
      - .gitlab-ci.yml

assets:
  stage: preparation
  image: $BUILDIMAGE
  script:
    - cd typo3/packages/dbi/ && source ~/.profile; nvm install && yarn install && yarn build
  artifacts:
    paths:
      - ./typo3/packages/dbi/node_modules/
      - ./typo3/packages/dbi/Resources/Public/
    expire_in: 1 days
    when: always
  cache:
    paths:
      - ./typo3/packages/dbi/node_modules/
  only:
    - branches

phive:
  stage: preparation
  image: $BASEIMAGE
  before_script: *add_deploy_key
  script:
    - cd typo3 && phive install --copy --temporary --trust-gpg-keys 0xE82B2FB314E9906E
  cache:
    - key: phive-$CI_COMMIT_REF_SLUG
      paths:
        - ./typo3/tools/
      policy: pull-push
  only:
    refs:
      - branches
    changes:
      - typo3/.phive/phars.xml
      - .gitlab-ci.yml
  except:
    - schedules

test-php:
  stage: testing
  image: $BASEIMAGE
  before_script: *add_deploy_key
  script:
    - cd typo3/
    - ./tools/php-cs-fixer check --diff --show-progress=none
  cache:
    - key: checkstyle-$CI_COMMIT_REF_SLUG
      paths:
        - ./typo3/.php-cs-fixer.cache
      policy: pull-push
    - key: composer-$CI_COMMIT_REF_SLUG
      paths:
        - ./typo3/public/
        - ./typo3/vendor/
      policy: pull
    - key: phive-$CI_COMMIT_REF_SLUG
      paths:
        - ./typo3/tools/
      policy: pull
  dependencies:
    - composer
    - assets
    - phive
  only:
    - branches

test-html:
  stage: testing
  image: $BUILDIMAGE
  script:
    - cd typo3/packages/dbi && make local-checkstyle
  only:
    - branches

.push-script: &push-script
  - export GIT_TAG_SENTRY=$(git describe --tags --always)
  # Login to Docker-Registry
  - docker login -u gitlab-ci-token -p $CI_JOB_TOKEN gitlab-docker.mogic.com
  - gitlabci-templating -inputTemplate ./template-data/ci/site-deubaimmo-template -vaultPath $VAULTPATH_KV2 -outputFile ./docker/typo3/conf/nginx/site-dbi
  # Build and Push Dockerimage
  - docker pull $BASEIMAGE
  - docker build --build-arg BASEIMAGE=$BASEIMAGE -f docker/typo3/Dockerfile -t gitlab-docker.mogic.com/$CI_PROJECT_NAMESPACE/$CI_PROJECT_NAME:${CI_COMMIT_REF_SLUG} .
  - docker push gitlab-docker.mogic.com/$CI_PROJECT_NAMESPACE/$CI_PROJECT_NAME:${CI_COMMIT_REF_SLUG}
  # Replace Variables in docker-compose-file (environment-related)
  - export DOCKERIMAGE=gitlab-docker.mogic.com/$CI_PROJECT_NAMESPACE/$CI_PROJECT_NAME:${CI_COMMIT_REF_SLUG}
  - gitlabci-templating -inputTemplate ./template-data/ci/docker-compose-template-$ENVIRONMENT -vaultPath $VAULTPATH_KV2 -outputFile $CI_PROJECT_DIR/docker-compose.yml

.deploy_script: &deploy_script
  - export TARGET_HOST=$(vault kv get -field="TARGET_HOST" $VAULTPATH_KV1)
  - ssh-keyscan $TARGET_HOST >> ~/.ssh/known_hosts
  - ssh root@$TARGET_HOST "mkdir -p /opt/$CI_PROJECT_NAMESPACE-$CI_PROJECT_NAME-$CI_COMMIT_REF_NAME"
  - rsync -havz --delete -e 'ssh -p 22' docker-compose.yml "root@$TARGET_HOST:/opt/$CI_PROJECT_NAMESPACE-$CI_PROJECT_NAME-$CI_COMMIT_REF_NAME"
  - ssh root@$TARGET_HOST "docker login -u gitlab-ci-token -p $CI_JOB_TOKEN gitlab-docker.mogic.com"
  - ssh root@$TARGET_HOST "cd /opt/$CI_PROJECT_NAMESPACE-$CI_PROJECT_NAME-$CI_COMMIT_REF_NAME/ && docker-compose pull"
  - INSTANCES=$(ssh root@$TARGET_HOST "cd /opt/$CI_PROJECT_NAMESPACE-$CI_PROJECT_NAME-$CI_COMMIT_REF_NAME/ && docker-compose ps -q typo3")
  - if [[ -z $INSTANCES ]]; then ssh root@$TARGET_HOST "cd /opt/$CI_PROJECT_NAMESPACE-$CI_PROJECT_NAME-$CI_COMMIT_REF_NAME/ && docker-compose up -d --force-recreate"; exit 0; fi;
  - NEW_INSTANCE_ID=$(ssh root@$TARGET_HOST "cd /opt/$CI_PROJECT_NAMESPACE-$CI_PROJECT_NAME-$CI_COMMIT_REF_NAME/ && docker inspect \$(docker-compose ps -q typo3) | jq 'if .[0].Created > .[1].Created then .[0].Id else .[1].Id end'")
  - OLD_INSTANCE_ID=$(ssh root@$TARGET_HOST "cd /opt/$CI_PROJECT_NAMESPACE-$CI_PROJECT_NAME-$CI_COMMIT_REF_NAME/ && docker inspect \$(docker-compose ps -q typo3) | jq 'if .[0].Created < .[1].Created then .[0].Id else if .[1].Id != "null" then .[1].Id else .[0].Id end end'")
  - if [[ $NEW_INSTANCE_ID != $OLD_INSTANCE_ID ]]; then ssh root@$TARGET_HOST "docker stop $OLD_INSTANCE_ID && docker rm $OLD_INSTANCE_ID"; fi
  - ssh root@$TARGET_HOST "cd /opt/$CI_PROJECT_NAMESPACE-$CI_PROJECT_NAME-$CI_COMMIT_REF_NAME/ && docker-compose up -d --no-recreate --scale typo3=2";

buildAndPushStaging:
  stage: pushing
  image: gitlab-docker.mogic.com/docker/gitlab-ci-git-dind
  before_script: *add_deploy_key
  variables:
    DOCKER_DRIVER: overlay2
    TYPO3_CONTEXT: Production/Staging
  script: *push-script
  dependencies:
    - composer
    - assets
  artifacts:
    paths:
      - docker-compose.yml
    expire_in: 1 days
    when: always
  cache:
    - key: composer-$CI_COMMIT_REF_SLUG
      paths:
        - ./typo3/public/
        - ./typo3/vendor/
      policy: pull
  only:
    variables:
      - $CI_COMMIT_REF_SLUG == $STAGINGBRANCH

buildAndPushProduction:
  stage: pushing
  image: gitlab-docker.mogic.com/docker/gitlab-ci-git-dind
  before_script: *add_deploy_key
  variables:
    DOCKER_DRIVER: overlay2
    TYPO3_CONTEXT: Production
  script: *push-script
  dependencies:
    - composer
    - assets
  artifacts:
    paths:
      - docker-compose.yml
    expire_in: 1 days
    when: always
  cache:
    - key: composer-$CI_COMMIT_REF_SLUG
      paths:
        - ./typo3/public/
        - ./typo3/vendor/
      policy: pull
  only:
    variables:
      - $CI_COMMIT_REF_SLUG == $PRODUCTIONBRANCH

.startBoreusVPN: &startBoreusVPN
  - BOREUS_VPN_PW=$(vault kv get -field=Mogic-VPN-Passwort-password devops/Boreus)
  - sed -i '/^verb 3/a askpass\ /etc/openvpn/bovpn.pass' /etc/openvpn/boreus.conf
  - echo $BOREUS_VPN_PW > /etc/openvpn/bovpn.pass
  - sudo <NAME_EMAIL>
  - sleep 20
  - sudo <NAME_EMAIL>

start-boreus-vpn:
  stage: startVPN
  resource_group: vpn
  script:
    - *startBoreusVPN
  only:
    variables:
      - $CI_COMMIT_REF_SLUG == $STAGINGBRANCH
      - $CI_COMMIT_REF_NAME == $PRODUCTIONBRANCH
  dependencies: []
  tags:
    - deployer

onSystem-stopCron:
  stage: pre-deploy
  image: gitlab-docker.mogic.com/docker/gitlab-ci-git-dind
  before_script: *add_deploy_key
  resource_group: vpn
  script:
    - export TARGET_HOST=$(vault kv get -field="TARGET_HOST" $VAULTPATH_KV1)
    - ssh-keyscan $TARGET_HOST >> ~/.ssh/known_hosts
    # change permissions on system
    - NEW_INSTANCE_ID=$(ssh root@$TARGET_HOST "cd /opt/$CI_PROJECT_NAMESPACE-$CI_PROJECT_NAME-$CI_COMMIT_REF_NAME/ && docker inspect \$(docker-compose ps -q typo3) | jq 'if .[0].Created > .[1].Created then .[0].Id else .[1].Id end'")
    - ssh root@$TARGET_HOST "docker exec -t $NEW_INSTANCE_ID sh -c 'supervisorctl stop cron'"
  only:
    variables:
      - $CI_COMMIT_REF_SLUG == $PRODUCTIONBRANCH
      - $CI_COMMIT_REF_SLUG == $STAGINGBRANCH
  tags:
    - deployer

deploy_staging:
  stage: deploy
  image: gitlab-docker.mogic.com/docker/gitlab-ci-git-dind
  variables:
    DOCKER_DRIVER: overlay2
  before_script: *add_deploy_key
  resource_group: vpn
  script: *deploy_script
  dependencies:
    - buildAndPushStaging
  environment:
    name: review/$CI_COMMIT_REF_NAME
    url: https://immobilien-deutschebank-stage.fio.de
  only:
    variables:
      - $CI_COMMIT_REF_SLUG == $STAGINGBRANCH
  tags:
    - deployer

deploy_production:
  stage: deploy
  image: gitlab-docker.mogic.com/docker/gitlab-ci-git-dind
  variables:
    DOCKER_DRIVER: overlay2
  before_script: *add_deploy_key
  resource_group: vpn
  script: *deploy_script
  dependencies:
    - buildAndPushProduction
  environment:
    name: review/$CI_COMMIT_REF_NAME
    url: https://immobilien-deutschebank.de
  only:
    variables:
      - $CI_COMMIT_REF_SLUG == $PRODUCTIONBRANCH
  tags:
    - deployer

onSystem-permissions-and-db:
  stage: onSystem
  image: gitlab-docker.mogic.com/docker/gitlab-ci-git-dind
  before_script: *add_deploy_key
  resource_group: vpn
  script:
    - export TARGET_HOST=$(vault kv get -field="TARGET_HOST" $VAULTPATH_KV1)
    - ssh-keyscan $TARGET_HOST >> ~/.ssh/known_hosts
    # change permissions on system
    - NEW_INSTANCE_ID=$(ssh root@$TARGET_HOST "cd /opt/$CI_PROJECT_NAMESPACE-$CI_PROJECT_NAME-$CI_COMMIT_REF_NAME/ && docker inspect \$(docker-compose ps -q typo3) | jq 'if .[0].Created > .[1].Created then .[0].Id else .[1].Id end'")
    - ssh root@$TARGET_HOST "docker exec -t $NEW_INSTANCE_ID sh -c 'chown -R www-data:www-data /var/www/'"
    - ssh root@$TARGET_HOST "docker exec -t $NEW_INSTANCE_ID su www-data -s /bin/bash -c './vendor/bin/typo3 database:updateschema safe'"
    - ssh root@$TARGET_HOST "docker exec -t $NEW_INSTANCE_ID su www-data -s /bin/bash -c './vendor/bin/typo3 install:fixfolderstructure'"
    - ssh root@$TARGET_HOST "docker exec -t $NEW_INSTANCE_ID su www-data -s /bin/bash -c './vendor/bin/typo3 upgrade:run'"
  only:
    variables:
      - $CI_COMMIT_REF_SLUG == $PRODUCTIONBRANCH
      - $CI_COMMIT_REF_SLUG == $STAGINGBRANCH
  tags:
    - deployer

onSystem-clearCaches:
  stage: onSystem
  image: gitlab-docker.mogic.com/docker/gitlab-ci-git-dind
  before_script: *add_deploy_key
  resource_group: vpn
  script:
    - export TARGET_HOST=$(vault kv get -field="TARGET_HOST" $VAULTPATH_KV1)
    - ssh-keyscan $TARGET_HOST >> ~/.ssh/known_hosts
    # change permissions on system
    - NEW_INSTANCE_ID=$(ssh root@$TARGET_HOST "cd /opt/$CI_PROJECT_NAMESPACE-$CI_PROJECT_NAME-$CI_COMMIT_REF_NAME/ && docker inspect \$(docker-compose ps -q typo3) | jq 'if .[0].Created > .[1].Created then .[0].Id else .[1].Id end'")
    - ssh root@$TARGET_HOST "docker exec -t $NEW_INSTANCE_ID su www-data -s /bin/bash -c './vendor/bin/typo3 cache:flush'"
  only:
    variables:
      - $CI_COMMIT_REF_SLUG == $PRODUCTIONBRANCH
      - $CI_COMMIT_REF_SLUG == $STAGINGBRANCH
  tags:
    - deployer

onSystem-cleanupOldInstance:
  stage: onSystem-cleanup
  image: gitlab-docker.mogic.com/docker/gitlab-ci-git-dind
  before_script: *add_deploy_key
  resource_group: vpn
  script:
    - export TARGET_HOST=$(vault kv get -field="TARGET_HOST" $VAULTPATH_KV1)
    - ssh-keyscan $TARGET_HOST >> ~/.ssh/known_hosts
    - NEW_INSTANCE_ID=$(ssh root@$TARGET_HOST "cd /opt/$CI_PROJECT_NAMESPACE-$CI_PROJECT_NAME-$CI_COMMIT_REF_NAME/ && docker inspect \$(docker-compose ps -q typo3) | jq 'if .[0].Created > .[1].Created then .[0].Id else .[1].Id end'")
    - OLD_INSTANCE_ID=$(ssh root@$TARGET_HOST "cd /opt/$CI_PROJECT_NAMESPACE-$CI_PROJECT_NAME-$CI_COMMIT_REF_NAME/ && docker inspect \$(docker-compose ps -q typo3) | jq 'if .[0].Created < .[1].Created then .[0].Id else if .[1].Id != "null" then .[1].Id else .[0].Id end end'")
    - if [[ $NEW_INSTANCE_ID != $OLD_INSTANCE_ID ]]; then ssh root@$TARGET_HOST "docker stop $OLD_INSTANCE_ID && docker rm $OLD_INSTANCE_ID"; else echo 'There is just one running instance, did nothing'; fi
  only:
    variables:
      - $CI_COMMIT_REF_SLUG == $PRODUCTIONBRANCH
      - $CI_COMMIT_REF_SLUG == $STAGINGBRANCH
  tags:
    - deployer

.stopBoreusVPN: &stopBoreusVPN
  - sudo <NAME_EMAIL>
  - rm /etc/openvpn/bovpn.pass
  - sed -i '/^askpass/d' /etc/openvpn/boreus.conf

stop-boreus-vpn:
  stage: stopVPN
  script:
    - *stopBoreusVPN
  dependencies: []
  only:
    variables:
      - $CI_COMMIT_REF_SLUG == $STAGINGBRANCH
      - $CI_COMMIT_REF_NAME == $PRODUCTIONBRANCH
  tags:
    - deployer

overwrite-with-master-data:
  stage: manual
  image: gitlab-docker.mogic.com/docker/gitlab-ci-git-dind
  before_script: *add_deploy_key
  script:
    - *startBoreusVPN
    # Mysql
    - export TARGET_HOST=$(vault kv get -field="TARGET_HOST" $VAULTPATH_KV1)
    - ssh-keyscan $TARGET_HOST >> ~/.ssh/known_hosts
    - export MINIO_DUMP_USER=$(vault kv get -field="db-dumps-id" $VAULTPATH_KV1)
    - export MINIO_DUMP_PASSWORD=$(vault kv get -field="db-dumps-password" $VAULTPATH_KV1)
    - export MINIO_DUMP_DOMAIN=$(vault kv get -field="db-dumps_login_url" $VAULTPATH_KV1)
    - MARIADB_INSTANCE_ID=$(ssh root@$TARGET_HOST "cd /opt/$CI_PROJECT_NAMESPACE-$CI_PROJECT_NAME-$CI_COMMIT_REF_NAME/ && docker inspect \$(docker-compose ps -q mariadb-deubaimmo) | jq 'if .[0].Created > .[1].Created then .[0].Id else .[1].Id end'")
    - TYPO3_INSTANCE_ID=$(ssh root@$TARGET_HOST "cd /opt/$CI_PROJECT_NAMESPACE-$CI_PROJECT_NAME-$CI_COMMIT_REF_NAME/ && docker inspect \$(docker-compose ps -q typo3) | jq 'if .[0].Created > .[1].Created then .[0].Id else .[1].Id end'")
    - ssh root@$TARGET_HOST "mc alias set mogic-minio $MINIO_DUMP_DOMAIN $MINIO_DUMP_USER $MINIO_DUMP_PASSWORD" || true
    - mc alias set mogic-minio $MINIO_DUMP_DOMAIN $MINIO_DUMP_USER $MINIO_DUMP_PASSWORD || true
    # List all files in the minio bucket
    - files=($(mc find "mogic-minio/fio-dumps/deubaimmo/production/"))
    # Find the newest file
    - newest_file=${files[0]}
    - |+
      for file in "${files[@]}"; do
        if [[ "$file" > "$newest_file" ]]; then
          newest_file="$file"
        fi
      done
    - chosen_file="$newest_file"
    - ssh root@$TARGET_HOST "mc cp $chosen_file /tmp/datadump_dbi.sql.gz"
    - ssh root@$TARGET_HOST "docker cp /tmp/datadump_dbi.sql.gz $MARIADB_INSTANCE_ID:/tmp/datadump_dbi.sql.gz"
    - ssh root@$TARGET_HOST "cd /opt/$CI_PROJECT_NAMESPACE-$CI_PROJECT_NAME-$CI_COMMIT_REF_NAME/ && docker-compose exec -T mariadb-deubaimmo sh -c 'zcat /tmp/datadump_dbi.sql.gz | mysql -u"\$MYSQL_USER" -p"\$MYSQL_PASSWORD" "\$MYSQL_DATABASE"'"
    - ssh root@$TARGET_HOST "docker exec -t $TYPO3_INSTANCE_ID su www-data -s /bin/bash -c './vendor/bin/typo3 database:updateschema'"
    - rsync -avz data/mariadb/local-storage.sql root@$TARGET_HOST:/tmp/local-storage.sql
    - ssh root@$TARGET_HOST "docker cp /tmp/local-storage.sql $MARIADB_INSTANCE_ID:/tmp/local-storage.sql"
    - ssh root@$TARGET_HOST "cd /opt/$CI_PROJECT_NAMESPACE-$CI_PROJECT_NAME-$CI_COMMIT_REF_NAME/ && docker-compose exec -T mariadb-deubaimmo sh -c ' mysql -u"\$MYSQL_USER" -p"\$MYSQL_PASSWORD" "\$MYSQL_DATABASE" < /tmp/local-storage.sql'"
    - ssh root@$TARGET_HOST "docker exec -t $TYPO3_INSTANCE_ID su www-data -s /bin/bash -c './vendor/bin/typo3 cache:flush'"
    - ssh root@$TARGET_HOST "rm /tmp/datadump_dbi.sql.gz"
    - ssh root@$TARGET_HOST "cd /opt/$CI_PROJECT_NAMESPACE-$CI_PROJECT_NAME-$CI_COMMIT_REF_NAME/ && docker-compose exec -T mariadb-deubaimmo sh -c 'rm /tmp/datadump_dbi.sql.gz'"
    - ssh root@$TARGET_HOST "mc alias remove mogic-minio" || true
    - *stopBoreusVPN
  when: manual
  only:
    variables:
      - $CI_COMMIT_REF_SLUG == $STAGINGBRANCH
  tags:
    - deployer

.create_data_dump: &create_data_dump
  - *startBoreusVPN
  - export TARGET_HOST=$(vault kv get -field="TARGET_HOST" $VAULTPATH_KV1)
  - ssh-keyscan $TARGET_HOST >> ~/.ssh/known_hosts
  # get db dump gzip file
  - DATE=$(date '+%Y-%m-%d_%H-%M-%S')
  - DUMP_FILE_NAME=$DATE-deubaimmo.sql
  - export DATABASE_NAME=$(vault kv get -field="db_database" $VAULTPATH_KV1)
  - ssh root@$TARGET_HOST "mysqldump --no-tablespaces $DATABASE_NAME | gzip > /tmp/$DUMP_FILE_NAME.gz"
  # get minio connection parameters
  - export MINIO_WRITE_USER=$(vault kv get -field="db-dumps-id" $VAULTPATH_KV1)
  - export MINIO_WRITE_PASSWORD=$(vault kv get -field="db-dumps-password" $VAULTPATH_KV1)
  - export MINIO_DOMAIN=$(vault kv get -field="db-dumps_login_url" $VAULTPATH_KV1)
  # create minio connection
  - ssh root@$TARGET_HOST "mc alias set mogic-minio $MINIO_DOMAIN $MINIO_WRITE_USER $MINIO_WRITE_PASSWORD" || true
  # sync db dump file to minio
  - ssh root@$TARGET_HOST "mc cp /tmp/$DUMP_FILE_NAME.gz mogic-minio/fio-dumps/deubaimmo/$ENVIRONMENT/$DUMP_FILE_NAME.gz"
  # cleanup dump files from host
  - ssh root@$TARGET_HOST "rm /tmp/$DUMP_FILE_NAME.gz"
  - ssh root@$TARGET_HOST "mc alias remove mogic-minio" || true
  - *stopBoreusVPN

create-prod-data_dump:
  stage: manual
  image: gitlab-docker.mogic.com/docker/gitlab-ci-git-dind
  resource_group: vpn
  before_script: *add_deploy_key
  script: *create_data_dump
  when: manual
  dependencies: []
  only:
    variables:
      - $CI_COMMIT_REF_SLUG == $PRODUCTIONBRANCH
  tags:
    - deployer

generate-sbom:
  stage: sbom
  image: $BASEIMAGE
  before_script: *add_deploy_key
  script:
    - composer2 global remove hirak/prestissimo || true
    - composer2 global config --no-plugins allow-plugins.cyclonedx/cyclonedx-php-composer true || true
    - composer2 global require cyclonedx/cyclonedx-php-composer
    - cd typo3 && composer2 make-bom || composer2 CycloneDX:make-sbom  --output-file=bom.xml
    - DEPTRACK_URL=$(vault kv get -field="deptrack_api_url" devops/Dependencytrack)
    - DEPTRACK_API_KEY=$(vault kv get -field="deptrack_api_key" devops/Dependencytrack)
    - "curl --fail-with-body -X POST $DEPTRACK_URL -H \"Content-Type: multipart/form-data\" -H \"X-API-Key: $DEPTRACK_API_KEY\" -F \"autoCreate=true\" -F \"projectName=$CI_PROJECT_NAMESPACE-$CI_PROJECT_NAME-composer\" -F \"projectVersion=$CI_COMMIT_TAG\" -F bom=@bom.xml"
  dependencies: []
  only:
    - tags
