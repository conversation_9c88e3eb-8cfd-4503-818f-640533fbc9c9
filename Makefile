up-new: #remove all files, checkout again and re-create all containers
	git fetch && git reset --hard origin/develop
	make up-new-keep-sources

up-new-keep-sources: stop prepare-data clean #new containers, new database
	docker compose -f compose.dev.yml pull
	docker compose -f compose.dev.yml up -d --force-recreate
	make autoconfig
	make importNewDB
	make build-frontend-assets
	make -s devinfo

start: prepare-data #start the existing system
	docker compose -f compose.dev.yml up -d
	make build-frontend-assets
	make -s devinfo

stop: #shutdown
	docker compose -f compose.dev.yml stop

clean: #remove all containers
	docker compose -f compose.dev.yml down --rmi all -v --remove-orphans

autoconfig: #automatically configure typo3
	docker compose -f compose.dev.yml exec -T typo3 su -s /bin/bash www-data -c "composer2 install"
	docker compose -f compose.dev.yml exec -T typo3 su -s /bin/bash www-data -c "./vendor/bin/typo3 cache:flush --group system"
	docker compose -f compose.dev.yml exec -T typo3 su -s /bin/bash www-data -c "./vendor/bin/typo3 database:updateschema"
	docker compose -f compose.dev.yml exec -T typo3 su -s /bin/bash www-data -c "./vendor/bin/typo3 cache:flush --group all"

prepare-data: #copy data from templates-data/ if missing
	test ! -d data/minio && cp -a template-data/minio data/ || true

devinfo:
	@echo "Frontend-URL: http://deubaimmo.test:5667/"
	@echo "Backend-URL:  http://deubaimmo.test:5667/typo3/  admin87dbi"

importNewDB: #fetch + install new database
	docker compose -f compose.dev.yml run --rm -it sync-database
	docker compose -f compose.dev.yml exec --user www-data:www-data typo3 ./vendor/bin/typo3 database:updateschema
	docker compose -f compose.dev.yml cp data/mariadb/local-storage.sql mariadb:/tmp/local-storage.sql\
	    && docker compose -f compose.dev.yml exec mariadb sh -c '\
	    mysql -u$$MYSQL_USER -p$$MYSQL_PASSWORD $$MYSQL_DATABASE < /tmp/local-storage.sql'
	docker compose -f compose.dev.yml exec --user www-data:www-data typo3 ./vendor/bin/typo3 cache:flush

dump-local-db:
	docker compose -f compose.dev.yml exec mariadb sh -c '\
	    mysqldump -u$$MYSQL_USER -p$$MYSQL_PASSWORD $$MYSQL_DATABASE\
	    --ignore-table-data=$$MYSQL_DATABASE.be_sessions\
	    --ignore-table-data=$$MYSQL_DATABASE.cache_hash\
	    --ignore-table-data=$$MYSQL_DATABASE.cache_hash_tags\
	    --ignore-table-data=$$MYSQL_DATABASE.cache_imagesizes\
	    --ignore-table-data=$$MYSQL_DATABASE.cache_imagesizes_tags\
	    --ignore-table-data=$$MYSQL_DATABASE.cache_pages\
	    --ignore-table-data=$$MYSQL_DATABASE.cache_pages_tags\
	    --ignore-table-data=$$MYSQL_DATABASE.cache_rootline\
	    --ignore-table-data=$$MYSQL_DATABASE.cache_rootline_tags\
	    --ignore-table-data=$$MYSQL_DATABASE.cache_treelist\
	    --ignore-table-data=$$MYSQL_DATABASE.cache_vhs_main\
	    --ignore-table-data=$$MYSQL_DATABASE.cache_vhs_main_tags\
	    --ignore-table-data=$$MYSQL_DATABASE.cache_vhs_markdown\
	    --ignore-table-data=$$MYSQL_DATABASE.cache_vhs_markdown_tags\
	    --ignore-table-data=$$MYSQL_DATABASE.sys_history\
	    --ignore-table-data=$$MYSQL_DATABASE.sys_log\
	    ' > my-local-dump.sql

build-frontend-assets:
	docker compose -f compose.dev.yml run --rm\
	    build sh -c "source ~/.profile; cd var/www && nvm install && yarn install && yarn build"

checkstyle: typo3/tools
	docker compose -f compose.dev.yml exec -it -u www-data typo3\
	    ./tools/php-cs-fixer check --diff
	docker compose -f compose.dev.yml run\
	    --rm --workdir /var/www\
	    build make local-checkstyle

fix-codestyle: typo3/tools
	docker compose -f compose.dev.yml exec -it -u www-data typo3\
	    ./tools/php-cs-fixer fix

typo3/tools: #install tool dependencies
	docker-compose -f compose.dev.yml exec -it --user $$(id -u):www-data --env HOME=/var/www/typo3 typo3\
	    phive install --copy --trust-gpg-keys 0xE82B2FB314E9906E

typo3-clear-cache:
	docker compose -f compose.dev.yml exec -it -u www-data typo3\
	    ./vendor/bin/typo3 cache:flush

init-git-hooks:
	@for file in ./template/githooks/*; do \
	  	target="./.git/hooks/$$(basename $$file)"; \
	  	if [ -f "$$target" ]; then \
	  		if cmp -s "$$file" "$$target"; then \
	  			echo "File $$target already exists with the same content. Ignoring."; \
	  		else \
	  			echo "Warning: File $$target exists with different content. Renaming to $$target.old"; \
	  			mv "$$target" "$$target.old"; \
	  			cat "$$file" >> "$$target"; \
	  			echo "moved $$(basename $$file)"; \
	  		fi; \
	  	else \
	  		cat "$$file" >> "$$target"; \
	  		echo "moved $$(basename $$file)"; \
	  	fi; \
		chmod +x "$$target"; \
	done
